import { HttpClient } from '@angular/common/http';
import { HttpClientTestingModule, HttpTestingController, TestRequest } from '@angular/common/http/testing';
import { TestBed, waitForAsync } from '@angular/core/testing';
import { MigrationRuleRuns } from '../interfaces/MigrationRuleRuns';
import { ConfigService } from './config.service';
import { MigrationRuleRunsService } from './migration-rule-runs.service';
import { of } from 'rxjs';

describe('MigrationRuleRunService', () => {
  const mockApiUrl = 'api/MigrationRuleRuns';
  const mockMigrationRuleRuns: MigrationRuleRuns[] = [
    {
      runId: 37331,
      status: 'EXPORTED',
      ruleSetName: "ink prdev rule",
      ruleSetId: 7495,
      sourceComponentId: 27362,
      targetComponentId: 27362,
      scopeTypeId: 0,
      jobId: 222220164,
      message: '',
      createdBy: 'eajaro',
      created: new Date(),
      started: new Date(),
      finished: new Date()
    }
  ];
  const mockRequestBody = {
    countryIds: [15,17],
    startDate: new Date()
  };
  const mockRunId = 123;

  let service: MigrationRuleRunsService;
  let configService: ConfigService;
  let httpTestingController: HttpTestingController;
  let httpClient: HttpClient;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [ConfigService]
    });

    service = TestBed.inject(MigrationRuleRunsService);
    configService = TestBed.inject(ConfigService);

    httpTestingController = TestBed.inject(HttpTestingController);
    httpClient = TestBed.inject(HttpClient);
  });

  afterEach(() => {
    httpTestingController.verify();
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('getAsync() [Method]', () => {
    it('should exist', () => {
      jest.spyOn(service, 'getAsync');

      expect(service.getAsync).toBeTruthy();
    });

    it('should be called with POST method', waitForAsync(() => {
      jest.spyOn(httpClient, 'post');

      httpClient.post(mockApiUrl, mockRequestBody).subscribe();

      const testRequest: TestRequest = httpTestingController.expectOne(mockApiUrl);

      expect(testRequest.request.method).toEqual('POST');

      testRequest.flush(null);
    }));
  });

  describe('getDetailAsync() [Method]', () => {
    it('should exist', () => {
      jest.spyOn(service, 'getDetailAsync');

      expect(service.getDetailAsync).toBeTruthy();
    });

    it('should be called with GET method', waitForAsync(() => {
      jest.spyOn(httpClient, 'get');

      httpClient.get(mockApiUrl + '/'  + mockRunId).subscribe();

      const testRequest: TestRequest = httpTestingController.expectOne(mockApiUrl + '/' + mockRunId);

      expect(testRequest.request.method).toEqual('GET');

      testRequest.flush(null);
    }));
  });
});
