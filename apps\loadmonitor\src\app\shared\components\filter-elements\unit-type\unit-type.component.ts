import { Component, ElementRef, Input, OnChanges, OnInit, SimpleChanges, ViewChild } from '@angular/core';
import { COMMA, ENTER } from '@angular/cdk/keycodes';
import { FormControl } from '@angular/forms';
import { Observable } from 'rxjs/internal/Observable';
import { map, startWith } from 'rxjs/operators';
import { get } from 'lodash';
import { MatAutocompleteSelectedEvent } from '@angular/material/autocomplete';
import { UnitType } from '@loadmonitor/shared/interfaces/UnitType';
import { UnitTypeService } from '@loadmonitor/shared/services/unitType.service';
const EMPTY = null;

@Component({
	selector: 'lm-unit-type-autocomplete',
	templateUrl: './unit-type.component.html',
	styleUrls: ['./unit-type.component.scss'],
})
export class UnitTypeChipAutoCompleteComponent implements OnInit, OnChanges {
	//@Input() jobType: JobTypes = JobTypes.none;
	@Input() cachedUnitTypeIds: number[] = [];

	@ViewChild('unitTypeIdsInput')
	unitTypeIdsInput!: ElementRef<HTMLInputElement>;
	selectedUnitTypeIds!: Observable<UnitType[]>;

	public unitTypeIds: UnitType[] = [];

	//Tag-Chip-List
	readonly separatorKeysCodes = [ENTER, COMMA] as const;

	// Mat-Chips - UnitType
	unitTypeIdsCtrl = new FormControl();
	filteredUnitTypeIds: Observable<UnitType[]> | undefined;
	allUnitTypeIds: UnitType[] = [];

	constructor(private unitTypeService: UnitTypeService) {}

	ngOnInit() {
		this.fillUnitTypes();
		this.selectedUnitTypeIds = this.unitTypeIdsCtrl.valueChanges.pipe(
			startWith(EMPTY),
			map(() => this.filterSelectedUnitTypeIds())
		);
		this.filteredUnitTypeIds = this.unitTypeIdsCtrl.valueChanges.pipe(
			startWith(null),
			map((unitType) => this.setUnitTypeFilter(get(unitType, 'name', unitType)))
		);
	}
	public setSelectedUnitTypes(unitTypeIds: number[]): void {
		unitTypeIds?.forEach((PPTid: number) => {
			const found = this.allUnitTypeIds.find((PPT: UnitType) => PPT.id === PPTid);
			if (found) {
				found.selected = true;
			} else {
				this.allUnitTypeIds.push({ id: PPTid, name: 'Loading...', selected: true });
			}
		});
	}

	public getSelectedUnitTypeNames() {
		return this.filterSelectedUnitTypeIds().map((status) => status.name);
	}

	public getSelectedUnitTypeIds() {
		return this.filterSelectedUnitTypeIds().map((status) => status.id);
	}

	filterSelectedUnitTypeIds(): UnitType[] {
		return this.allUnitTypeIds.filter((bpType) => bpType.selected);
	}

	removeAllUnitTypes() {
		this.filterSelectedUnitTypeIds().map((x) => (x.selected = false));
		this.refreshSuggestionsAndEmptyInput();
	}
	fillUnitTypes(): void {
		this.unitTypeService.getAsync().subscribe((result) => {
			const allUnitTypeIds = result.map((r) => {
				const tag: UnitType = { id: r.id, name: r.name, selected: false };
				return tag;
			});
			this.allUnitTypeIds = allUnitTypeIds.sort((firstService: UnitType, otherService: UnitType) =>
				firstService.name.localeCompare(otherService.name)
			);

			this.setSelectedUnitTypes(this.cachedUnitTypeIds);
			this.refreshSuggestionsAndEmptyInput();
		});
	}

	// UnitType Chips
	removeUnitTypeChips(item: UnitType): void {
		this.toggleSelection(item);
	}

	optionClicked(event: Event, item: UnitType) {
		event.stopPropagation();
		this.toggleSelection(item);
	}

	toggleSelection(item: UnitType) {
		item.selected = !item.selected;
		this.refreshSuggestionsAndEmptyInput();
	}

	refreshSuggestionsAndEmptyInput() {
		if (this.unitTypeIdsInput != undefined) this.unitTypeIdsInput.nativeElement.value = '';
		this.unitTypeIdsCtrl.reset();
	}

	selectedUnitTypeChips(event: MatAutocompleteSelectedEvent): void {
		this.toggleSelection(event.option.value);
		this.unitTypeIdsCtrl.setValue(null);
	}

	private setUnitTypeFilter(value: string): UnitType[] {
		if (value === '' || value === null) {
			return this.allUnitTypeIds;
		}
		const filterValue = value.toLowerCase();

		return this.allUnitTypeIds.filter((unitType) => unitType.name.toLowerCase().indexOf(filterValue) === 0);
	}
	ngOnChanges(changes: SimpleChanges){
		this.allUnitTypeIds.forEach(function(i){ i.selected=false })
		this.setSelectedUnitTypes(changes.cachedUnitTypeIds.currentValue);
		this.refreshSuggestionsAndEmptyInput();
	  }
}
