import { Component, ElementRef, ViewChild, Input, AfterViewInit, ChangeDetectorRef, SimpleChanges, OnChanges } from '@angular/core';
import { FormControl } from '@angular/forms';
import { Observable } from 'rxjs';
import { debounceTime, map, startWith } from 'rxjs/operators';
import { get } from 'lodash';
import { COMMA, ENTER } from '@angular/cdk/keycodes';
import { TagItem } from '@loadmonitor/shared/interfaces/TagItem';
import { SectorService } from '@loadmonitor/shared/services/sector.service';
import { HelperService } from '@loadmonitor/shared/services/helper.service';
import { MatAutocompleteSelectedEvent } from '@angular/material/autocomplete';

const EMPTY = null;

@Component({
  selector: 'lm-sector-chip-autocomplete',
  templateUrl: './sector-chip-autocomplete.component.html',
  styleUrls: ['./sector-chip-autocomplete.component.scss']
})
export class SectorChipAutocompleteComponent implements AfterViewInit,OnChanges {

  @ViewChild('sectorIdsInput')
  sectorIdsInput!: ElementRef<HTMLInputElement>;

  sectorIds!: Observable<TagItem[]>;

  @Input() cachedSectors: number[] = [];

  readonly separatorKeysCodes = [ENTER, COMMA] as const;

  // Mat-Chips - Sector
  sectorIdsCtrl = new FormControl();
  filteredSectorIds!: Observable<TagItem[]>;
  allSectorIds: TagItem[] = [];

  constructor(
    private sectorService: SectorService,
    public util: HelperService,
    private cdRef : ChangeDetectorRef
  ) {
    this.fillSectors();
    this.filteredSectorIds = this.sectorIdsCtrl.valueChanges.pipe(
      startWith(EMPTY),
      debounceTime(300),
      map((sector) => this.setSectorFilter(get(sector, 'name', sector)))
    );

    this.sectorIds = this.sectorIdsCtrl.valueChanges.pipe(
      map(() =>
        this.filterSelectedSectors(),
      ),
    );
  }

  ngAfterViewInit(): void {
    this.setSelectedSectorIds(this.cachedSectors);
    this.util.sendSelectedSectors(this.allSectorIds.filter(i => i.selected).map((i) => i.id));
    this.cdRef.detectChanges();
  }

  public getSelectedSectorIds() {
    return this.filterSelectedSectors().map((Sector) => Sector.id);
  }


  filterSelectedSectors(): TagItem[] {
    return this.allSectorIds.filter(
      (Sector) => Sector.selected,
    );
  }

  removeSectors(): void {
    this.filterSelectedSectors().map((x) => x.selected = false);
    this.refreshSuggestionsAndEmptyInput();
  }

  fillSectors(): void {
    this.sectorService.getAsync().subscribe((result) => {
      const allSectorIds = result.map((r) => {
        const tag: TagItem = { id: r.id, name: r.name, selected: false };
        return tag;
      });
      this.allSectorIds = allSectorIds.sort((firstId: TagItem, otherId: TagItem) => firstId.name.localeCompare(otherId.name));

      this.setSelectedSectorIds(this.cachedSectors);
      this.refreshSuggestionsAndEmptyInput();
    });

  }

  public setSelectedSectorIds(SectorIds: number[]): void {

    SectorIds?.forEach((PPTid: number) => {
      const found = this.allSectorIds.find((PPT: TagItem) => PPT.id === PPTid);
      if (found) {
        found.selected = true;
      } else {
        this.allSectorIds.push({ id: PPTid, name: 'Loading...', selected: true })
      }
    });
    this.refreshSuggestionsAndEmptyInput();
  }

  // Sector Chips

  private setSectorFilter(value: string): TagItem[] {
    if (value === '' || value === null) {
      return this.allSectorIds;
    }
    else {
      const filterValue = value.toLowerCase();
      return this.allSectorIds.filter(
        (PPT) => PPT.name.toLowerCase().indexOf(filterValue) === 0,
      );
    }
  }

  removeChipsTagItem(item: TagItem): void {
    this.toggleSelection(item);
}

  optionClicked(event: Event, item: TagItem) {
    event.stopPropagation();
    this.toggleSelection(item);
  }

  toggleSelection(item: TagItem) {
    item.selected = !item.selected;
    this.util.sendSelectedSectors(this.allSectorIds.filter(i => i.selected).map((i) => i.id));
    this.refreshSuggestionsAndEmptyInput();
  }

  refreshSuggestionsAndEmptyInput() {
    this.sectorIdsCtrl.reset();
    if (this.sectorIdsInput != undefined)
      this.sectorIdsInput.nativeElement.value = '';
  }

  selectedSectorChips(event: MatAutocompleteSelectedEvent): void {
    this.toggleSelection(event.option.value);
    this.sectorIdsCtrl.setValue(null);
    this.util.sendSelectedSectors(this.allSectorIds.filter(i => i.selected).map((i) => i.id));
  }

  ngOnChanges(changes: SimpleChanges){
    this.allSectorIds.forEach(function(i){ i.selected=false })
   this.setSelectedSectorIds(changes.cachedSectors.currentValue);
  }
}
