// Example usage of gfk theme variables:
@use 'node_modules/@angular/material' as mat;
@use 'sass:map' as map;
@use '../../../styles/theme/gfk-light.palette' as gfk;
@use '../../../styles/theme/material-gfk.typography' as type;
@use '../../../styles/theme/material-gfk-light.palette' as mat-colors;
@use '../../../styles/theme/gfk-light.palette' as colors;

$config: mat.get-color-config(mat-colors.$theme);
$background: map-get($config, background);
$foreground: map-get($config, foreground);

%custom-component-selector {
  .something-that-looks-like-h2 {
    //@include mat.typography-level($gfk-typography, 'title');
    //or
    font-size: mat.font-size(type.$typography, 'title');
    font-family: mat.font-family(type.$typography, 'title');
    font-weight: mat.font-weight(type.$typography, 'title');
    line-height: mat.line-height(type.$typography, 'title');
    color: mat.get-color-from-palette($foreground, text);
    background: mat.get-color-from-palette($background, 'background');
  }
}

.home-page-header {
	flex: 1 1 auto;
	display: flex;
	justify-content: center;
	align-items: center;
	height: auto;
	font-size: 40px;
	margin-top: 60px;
	margin-bottom: 5px;
}

.home-page {
	flex: 1 1 auto;
	display: flex;
	justify-content: center;
	align-items: center;
	height: auto;
	margin: 25px;
	font-size: 40px;
}

.page-button {
	justify-content: center;
	align-items: center;
	display: flex;
	background-color: map.get(gfk.$palette, 'orange');
	color: #f4f4f4;
	margin: 15px;
	font-size: 1em;
	height: 120px;
	width: 350px;
	white-space: pre-wrap !important;
}

.page-button:hover {
	background-color: map.get(gfk.$palette, 'orange-dark');
}

/* Remove extra left and right margins, due to padding */
.card-row {
	margin: 2em 0px 0px 10px;
	max-width: 1440px;
	margin: 0 auto;
}

/* Clear floats after the columns */
.card-row:after {
	content: '';
	display: table;
	clear: both;
}

.card-column {
	float: left;
	width: 33%;
	padding: 8px 8px;
	box-sizing: border-box;
	display: inline-flex;
}

.logo-container {
	width: 100%;
}
  
.spacer {
	flex: 1 1 auto;
}
  
.navigation-toolbar {
	background-color: map.get(colors.$palette, 'orange');
	color: map.get(colors.$palette, 'bg-light');
}
  
.atlwdg-fake-trigger {
	border-top: 2px solid;
	border-radius: 5px;
	position: static;
	line-height: 1.5;
}
  
.headline {
	padding-left: 10px;
	cursor: pointer;
}
  
.version {
	color: #808080;
}
  
.help-button {
	margin-right: 5px;
	box-sizing: border-box;
	min-width: 64px;
	text-align: center;
}
  
.navigation-toolbar-home-button {
	font-size: 19px;
	background-color: map.get(colors.$palette, 'orange');
	color: map.get(colors.$palette, 'bg-light');
	border-color: map.get(colors.$palette, 'orange');
}
  
.logout-button {
	background: transparent;
	width: auto;
}
  
.login-name {
	border-right: 1px solid white;
	font-size: 14px;
	margin-right: 16px;
	padding-right: 16px;
	margin-left: 16px;
}
  
.nav-bg {
	background-color: #f5f6f7;
}
  
.gfk-top-header {
	position: fixed;
	width: 100%;
	top: 0;
	z-index: 999;
  
	.logo {
		padding: 0 32px 0 24px;
	}
  
	.gfk-nav {
		border-bottom: 0px !important;
		display: inline-flex;
		width: 100%;
		position: sticky;
		cursor: pointer;
		top: 0;
		z-index: 1;
	}
  
	.gfk-nav {
		border-bottom: 0px !important;
		&.sub-nav {
			background-color: #fff;
			box-shadow: 0 0 1px #bbc0c9;
			z-index: 2;
			position: fixed;
			cursor: pointer;
			font-weight: 400;
		}
	}
}
  
.gfk-secondary-header {
	width: 100%;
	position: fixed;
	z-index: 998;
	cursor: pointer;
  
	.gfk-nav {
	  &.sub-nav {
		background-color: #fff;
		box-shadow: 0 0 1px #bbc0c9;
		z-index: 2;
		position: fixed;
		top: 48px;
		.nav-tab {
		  line-height: 2.5rem;
		}
	  }
	}
}


/* Responsive columns - one column layout (vertical) on small screens */
@media screen and (max-width: 600px) {
	.card-column {
		width: 100%;
		display: block;
		margin-bottom: 20px;
	}
}
