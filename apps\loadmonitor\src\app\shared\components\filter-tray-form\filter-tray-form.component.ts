import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { FormBuilder, FormGroup } from '@angular/forms';
import { AutocompleteOption } from '@dwh/lmx-lib/src';
import { Category } from '@loadmonitor/shared/interfaces/Category';
import { DomainProductGroups } from '@loadmonitor/shared/interfaces/DomainProductGroups';
import { TagItem } from '@loadmonitor/shared/interfaces/TagItem';
import { BasechannelService } from '@loadmonitor/shared/services/basechannel.service';
import { CategoryService } from '@loadmonitor/shared/services/category.service';
import { ChannelService } from '@loadmonitor/shared/services/channel.service';
import { CountryService } from '@loadmonitor/shared/services/country.service';
import { DomainProductGroupService } from '@loadmonitor/shared/services/domainProductgroup.service';
import { SectorService } from '@loadmonitor/shared/services/sector.service';
import { StatusService } from '@loadmonitor/shared/services/status.service';
import { UserService } from '@loadmonitor/shared/services/user.service';
import { Observable, Subject, Subscription, map, merge, of } from 'rxjs';
import * as moment from 'moment';
import { WorkspaceService } from '@loadmonitor/shared/services/workspace.service';
import { UnitTypeService } from '@loadmonitor/shared/services/unitType.service';
import { UserCache } from '@loadmonitor/shared/interfaces/caching/UserCache';
import { Country } from '@loadmonitor/shared/interfaces/Country';
import { Sector } from '@loadmonitor/shared/interfaces/Sector';
import { ProductionProjectType } from '@loadmonitor/shared/interfaces/ProductionProjectType';
import { ProductionProjectTypeService } from '@loadmonitor/shared/services/production-project-type.service';
import { JobTypes } from '@loadmonitor/shared/jobTypes';
import { BaseProjectTypeSelectService } from '@loadmonitor/shared/services/base-project-type-select.service';
import { JobType } from '@loadmonitor/shared/interfaces/JobType';
import { JobTypeService } from '@loadmonitor/shared/services/job-type.service';
import { Status } from '@loadmonitor/shared/interfaces/Status';
import { keyBy, merge as lodMerge, values } from 'lodash';
import { ExportFormatService } from '@loadmonitor/shared/services/export-format.service';
import { ExportFormat } from '@loadmonitor/shared/interfaces/ExportFormat';
 import { OperationService } from '@loadmonitor/shared/services/operation.service';
import { Workspace } from '@loadmonitor/shared/interfaces/Workspace';
import { UnitType } from '@loadmonitor/shared/interfaces/UnitType';
import { Periodicity } from '@loadmonitor/shared/interfaces/Periodicity';
import { PeriodCache } from '@loadmonitor/shared/interfaces/caching/PeriodCache';
import { FilterTrayCacheService } from '@loadmonitor/shared/services/filter-tray-cache.service';
import { PeriodicityService } from '@loadmonitor/shared/services/periodicity.service';
import { PeriodService } from '@loadmonitor/shared/services/period.service';
import { Router } from '@angular/router';
import { FileTypeService } from '@loadmonitor/shared/services/fileType.service';

export interface CacheObjects {
	sectorIds: number[];
	categoryIds: number[];
	domainProductGroupIds: number[];
	channelIds: number[];
	baseChannelIds: number[];
	countryIds: number[];
	status: number[];
	selectedDays: string;
	startDate?: Date;
	endDate?: Date;
	users?: UserCache[];
	jobIds: number[];
	loadIds: number[];
	bcRearrangedIds: number[];
	feature: string;
	countries: Country[];
	domainProductGroup: DomainProductGroups[];
	category: Category[];
	sector: Sector[];
	periodicityIds: Periodicity;
	periodIds: PeriodCache[];
	productionProjectIds: number[];
	dataOrderIds: number[];
	productionProjectType: ProductionProjectType[];
	productionProjectName: string;
	baseProjectIds: number[];
	qcProjectIds: number[];
	baseProjectType: number[];
	baseProjectName: string;
	qcProjectflag: any;
	jobType: JobType[];
	qcProjectName: string;
	reportingProjectIds: number[];
	exportIds: number[];
	exportName: string;
	exportFormatIds: ExportFormat[];
	reportingProjectName: null;
  	rbProjectName: string;
	reportingGroupIds: number[];
	cdmDeliveryIds: number[];

	statusProcessStageId: number[];

	domainProductGroupType: number;
	loadUnitIds: number[];
	prepUnitIds: number[];
	unitIds: number[];
	importIds: number[];

	runIds: number[];
	ruleSetIds: number[];
	sourceComponentIds: number[];
	targetComponentIds: number[];
	ruleSetName: string;
	scope: string;
	scopeTypeId: number;

	loadDefinitionIds: number[];
	productionProjectTypeIds: number[];
	productionProjectTypeNames: string[];

	project: string;

	rbProjectIds: number[];
	operationIds: number[];
	workspaceIds: Workspace[];
	unitType: UnitType[];
	fileType: [];
}

export interface EDSAutocompleteOption {
	value: string;
	label: string;
}

@Component({
	selector: 'lm-filter-tray-form',
	templateUrl: './filter-tray-form.component.html',
	styleUrls: ['./filter-tray-form.component.scss'],
})
export class FilterTrayFormComponent implements OnInit {
	@Input() resetForm!: Subject<boolean>;
	@Input() sliceName!: string;
	@Input() isPeriodHidden!: boolean;
	@Output() selectedFilters = new EventEmitter();
	@Output() fileTypeData = new EventEmitter();
	allStatusIds: Status[] = [];
	categoryIds!: number[];
	sectorIds!: number[];
	resetPeriodicityFilter: Subject<boolean> = new Subject();
	category$!: Observable<AutocompleteOption[]>;
	domainProductGroup$!: Observable<AutocompleteOption[]>;
	users$!: Observable<AutocompleteOption[]>;

	withOutQCProject = [
		{ label: 'With QC', value: '1' },
		{ label: 'Without QC', value: '2' },
	];

	domainProductGroupType = [
		{ label: 'Panel', value: '1' },
		{ label: 'TSC', value: '2' },
	];

	scopeTypeId = [
		{ label: 'Reporting Groups / LOBs', value: '1' },
		{ label: 'Reporting projects', value: '2' },
	];

	subscription!: Subscription | undefined;
	tempcacheObj: CacheObjects = {} as CacheObjects;
	cacheObj: CacheObjects = {} as CacheObjects;
	filterFG!: FormGroup;
	countries$!: Observable<EDSAutocompleteOption[]>;
	sectors$!: Observable<EDSAutocompleteOption[]>;
	channelIds$!: Observable<EDSAutocompleteOption[]>;
	baseChannelIds$!: Observable<EDSAutocompleteOption[]>;
	status$!: Observable<EDSAutocompleteOption[]>;
	workspacIds$!: Observable<EDSAutocompleteOption[]>;
	baseProjectTypeList!: EDSAutocompleteOption[];
	jobTypeList!: EDSAutocompleteOption[];
	jobType!: EDSAutocompleteOption[];
	productionProjectType$!: Observable<EDSAutocompleteOption[]>;
	operations$!: Observable<EDSAutocompleteOption[]>;
	exportFormat$!: Observable<EDSAutocompleteOption[]>;
	statusExports$!: Observable<EDSAutocompleteOption[]>;
	unitType$!: Observable<EDSAutocompleteOption[]>;
	fileType$!: Observable<EDSAutocompleteOption[]>;
	periodicity$!: Observable<EDSAutocompleteOption[]>;
	period$!: Observable<EDSAutocompleteOption[]>;
	countriesList: any = [];
	sectorsList: any = [];
	channelList: any = [];
	baseChannelList: any = [];
	statusList: any = [];
	workSpaceList: any = [];
	productionProjectTypeList: any = [];
	operationList: any = [];
	exportFormatList: any = [];
	statusExportsList: any = [];
	unitTypeList: any = [];
	fileTypeList: any = [];
	periodicityList: any = [];
	periodList: any = [];
	periodicities: any;
	
	constructor(
		private formBuilder: FormBuilder,
		private countryService: CountryService,
		private domainProductGroupService: DomainProductGroupService,
		private categoryService: CategoryService,
		private sectorService: SectorService,
		private userService: UserService,
		private statusService: StatusService,
		private channelService: ChannelService,
		private basechannelService: BasechannelService,
		private workspaceService: WorkspaceService,
		private unitTypeService: UnitTypeService,
		private fileTypeService: FileTypeService,
		private productionProjectTypeService: ProductionProjectTypeService,
		private bpTypeSelect: BaseProjectTypeSelectService,
		private jobTypeService: JobTypeService,
		private exportFormatService: ExportFormatService,
		private operationService: OperationService,
		public filterTrayCacheService: FilterTrayCacheService,
		private periodicityService: PeriodicityService,
		private periodService: PeriodService,
		private router: Router
	) {}

	ngOnInit(): void {
		let jobTypeUrl: any;
		switch(this.router.url){
			case '/ranged-weighting-import':
				jobTypeUrl = JobTypes.rangedWeightingImport;
				break;
			case '/bcr-reworks':
				jobTypeUrl = JobTypes.bcrRework;
				break;
			case '/logging-qc':
				jobTypeUrl = JobTypes.loggingQC;
				break;
			case '/coverage-imports':
				jobTypeUrl = JobTypes.coverageImport;
				break;
			case '/ld-general':
				jobTypeUrl = JobTypes.ldGeneral;
				break;
			case '/publish-republish':
				jobTypeUrl = JobTypes.rbPublishUnpublish;
				break;
			case '/item-master-data':
				jobTypeUrl = JobTypes.itemMasterData;
				break;
			case '/dwhrelease':
				jobTypeUrl = JobTypes.DWHRelease;
				break;
			case '/feature-move':
				jobTypeUrl = JobTypes.featureMove;
				break;
			case '/migration-rule-runs':
				jobTypeUrl = JobTypes.migrationRuleRuns;
				break;
			case '/data-order':
				jobTypeUrl = JobTypes.DataOrder;
				break;
			case '/csv-imports':
				jobTypeUrl = JobTypes.CsvImport;
				break;
			case '/export':
				jobTypeUrl = JobTypes.export;
				break;
			case '/shop-master-data':
				jobTypeUrl = JobTypes.shopMasterData;
				break;
			default:
				jobTypeUrl = JobTypes.none;
		}
		this.filterFGFormInitialization();
		this.getCountryList();
		this.getSectorList();
		this.getChannelList();
		this.getBaseChannelList();
		this.getStatusList();
		this.getWorkspaceList();
		this.getProjectTypeList();
		this.getJobTypeListForItemTypeMaster(jobTypeUrl);
		this.getJobTypeForDWHRelease(jobTypeUrl);
		this.getProductionProjectTypeList(jobTypeUrl);
		this.getOperationList();
		this.getExportFormatList();
		this.getStatusExportList();
		this.getUnitTypeList();
		this.getFileTypeList();
		this.getPeriodicitiesList();

		this.category$ = this.categoryService.getAsync().pipe(
			map((category) => {
				const allCategoryIds = category.map((r) => {
					const tag: Category = { id: r.id, name: r.name, sectorId: r.sectorId, selected: false };
					return tag;
				});
				allCategoryIds.sort((firstCategory: Category, otherCategory: Category) => firstCategory.name.localeCompare(otherCategory.name));
				return allCategoryIds.map((d) => ({ name: d.name, id: d.id } as AutocompleteOption));
			})
		);

		this.domainProductGroup$ = this.domainProductGroupService.getAsync(this.sectorIds, this.categoryIds).pipe(
			map((domainProductGroup) => {
				const allDomainProductGroupIds = this.filterDomainProductGroup(domainProductGroup);
				return allDomainProductGroupIds.map((dgp: any) => dgp as AutocompleteOption);
			})
		);

		this.users$ = this.userService.getAsync('').pipe(
			map((users) => users.map((d) => ({ name: d.lastName + ', ' + d.firstName + ' (' + d.userName + ')', id: d.userId, userName: d.userName } as AutocompleteOption))));

		this.subscription = this.filterFG.get('domainProductGroup')?.valueChanges.subscribe((result) => {
			this.tempcacheObj.domainProductGroup = result;
		});

		this.subscription = this.filterFG.get('sector')?.valueChanges.subscribe(() => {
			this.category$ = this.categoryService.getAsync().pipe(
				map((category) => {
					let allCategoryIds = category.map((r) => {
						const tag: Category = { id: r.id, name: r.name, sectorId: r.sectorId, selected: false };
						return tag;
					});
					allCategoryIds.sort((firstCategory: Category, otherCategory: Category) => firstCategory.name.localeCompare(otherCategory.name));
					if (this.filterFG.get('sector')?.value?.length > 0) {
						allCategoryIds = allCategoryIds.filter((i) =>
							this.filterFG.get('sector')?.value.some((j: any) => j == i.sectorId)
						);
					}
					return allCategoryIds.map((d) => ({ name: d.name, id: d.id } as AutocompleteOption));
				})
			);

			this.domainProductGroup$ = this.domainProductGroupService
				.getAsync(
					this.filterFG.get('sector')?.value != null ? this.filterFG.get('sector')?.value?.map((sector: any) => sector) : [],
					this.filterFG.get('category')?.value?.map((category: any) => category.id)
				)
				.pipe(
					map((domainProductGroup) => {
						const allDomainProductGroupIds = this.filterDomainProductGroup(domainProductGroup);
						return allDomainProductGroupIds.map((dgp: any) => dgp as AutocompleteOption);
					})
				);
			this.filterFG.get('category')?.setValue(this.tempcacheObj.category);
			this.filterFG.get('domainProductGroup')?.setValue(this.tempcacheObj.domainProductGroup);
		});

		this.subscription = this.filterFG.get('category')?.valueChanges.subscribe((result) => {
			this.tempcacheObj.category = result;
			this.domainProductGroup$ = this.domainProductGroupService
				.getAsync(
					this.filterFG.get('sector')?.value != null ? this.filterFG.get('sector')?.value?.map((sector: any) => sector) : [],
					this.filterFG.get('category')?.value?.map((category: any) => category.id)
				)
				.pipe(
					map((domainProductGroup) => {
						const allDomainProductGroupIds = this.filterDomainProductGroup(domainProductGroup);
						return allDomainProductGroupIds.map((dgp: any) => dgp as AutocompleteOption);
					})
				);
			this.filterFG.get('domainProductGroup')?.setValue(this.tempcacheObj.domainProductGroup);
		});

		this.subscription = this.filterFG.get('periodicityIds')?.valueChanges.subscribe((result) => {
			if(result){
				this.tempcacheObj.periodicityIds = result;
				this.period$ = this.periodService
					.getAsync(
						this.filterFG.get('periodicityIds')?.value
					)
					.pipe(
						map((periodList) => {
							return periodList.map((period: any) => ({ label: period.name, value: (period.id).toString() } as EDSAutocompleteOption));
						})
					  );
					this.period$.subscribe({
						next: (result) => {
							this.periodList = result;
						}
					});
				this.filterFG.get('periodIds')?.setValue(this.tempcacheObj.periodIds);
			}
			else{
				this.periodList = [];
				this.filterFG.get('periodIds')?.setValue([]);
			}
			this.userkey('');
		});

		this.filterFG.valueChanges.subscribe((value) => {
			this.selectedFilters.emit(value);
		});

		this.filterTrayCacheService.cacheValueData.subscribe((value: any) => {
			if (value) {
				this.cacheObj = value;
				this.updateFormControl();
			}
		});

		this.resetForm.subscribe((value) => {
			if (value) {
				this.filterFG.reset();
				this.resetPeriodicityFilter.next(true);
				this.filterTrayCacheService.cacheValueData.subscribe((value: any) => {
					localStorage.setItem(value, JSON.stringify(this.cacheObj));
				})
				
			}
		});
	}

	/**
	 * @name getCountryList
	 * @desc get country list data
	*/
	getCountryList(){
		this.countries$ = this.countryService.getAsync()
			.pipe(
				map((countries: any) => countries.map((country: any) => ({ label: country.name, value: (country.id).toString(), isoName: country.isoName } as EDSAutocompleteOption)))
			);
		this.countries$.subscribe({
			next: (result) => {
				this.countriesList = result;
			}
		});
	}

	/**
	 * @name getSectorList
	 * @desc get sector list data
	*/
	getSectorList(){
		this.sectors$ = this.sectorService.getAsync().pipe(
			map((sector) => {
				const allSectorIds = sector.map((r) => {
					const tag: TagItem = { id: r.id, name: r.name, selected: false };
					return tag;
				});
				allSectorIds.sort((firstId: TagItem, otherId: TagItem) => firstId.name.localeCompare(otherId.name));
				return allSectorIds.map((d) => ({ label: d.name, value: (d.id).toString() } as EDSAutocompleteOption));
			})
		);
		this.sectors$.subscribe({
			next: (result) => {
				this.sectorsList = result;
			}
		});
	}

	/**
	 * @name getChannelList
	 * @desc get channel list data
	*/
	getChannelList(){
		this.channelIds$ = this.channelService.getAsync().pipe(
			map((channel) => {
				const allChannelIds = this.filterChannels(channel);
				return allChannelIds.map((d) => ({ label: d.name, value: (d.id).toString() } as EDSAutocompleteOption));
			})
		);
		this.channelIds$.subscribe({
			next: (result) => {
				this.channelList = result;
			}
		});
	}

	/**
	 * @name getBaseChannelList
	 * @desc get base channel list data
	*/
	getBaseChannelList(){
		this.baseChannelIds$ = this.basechannelService.getAsync().pipe(
			map((baseChannel) => {
				const allBaseChannelIds = this.filterBaseChannels(baseChannel);
				return allBaseChannelIds.map((d) => ({ label: d.name, value: (d.id).toString() } as EDSAutocompleteOption));
			})
		);
		this.baseChannelIds$.subscribe({
			next: (result) => {
				this.baseChannelList = result;
			}
		});
	}

	/**
	 * @name getStatusList
	 * @desc get status list data
	*/
	getStatusList(){
		this.status$ = this.statusService.getAsync(this.sliceName).pipe(
			map((status) => status.map((d) => ({ label: (d.description == 'Dele' ? 'Deleted' : d.description), value: (d.id).toString() } as EDSAutocompleteOption))));
		this.status$.subscribe({
			next: (result) => {
				this.statusList = result;
				this.filterTrayCacheService.statusList = this.statusList;
			}
		});
	}

	/**
	 * @name getWorkspaceList
	 * @desc get workSpace list data
	*/
	getWorkspaceList(){
		this.workspacIds$ = this.workspaceService.getAsync().pipe(
			map((workspace) => {
				const allWorkspace = this.filterWorkspace(workspace);
				return allWorkspace.map((d) => ({ label: d.name, value: (d.id).toString() } as EDSAutocompleteOption));
			})
		);
		this.workspacIds$.subscribe({
			next: (result) => {
				this.workSpaceList = result;
			}
		});
	}

	/**
	 * @name getProjectTypeList
	 * @desc get baseProjectType list data
	*/
	getProjectTypeList(){
		this.baseProjectTypeList = this.bpTypeSelect.fillBaseProjectTypes()
		.map((bptype) => ({ label: bptype.name, value: (bptype.id).toString() } as EDSAutocompleteOption));
	}

	/**
	 * @name getJobTypeListForItemTypeMaster
	 * @desc get jobType list data for item type master
	*/
	getJobTypeListForItemTypeMaster(jobTypeUrl: any){
		this.jobTypeList = this.jobTypeService.getAsync(jobTypeUrl)
		.map((jobType) => ({ label: jobType.name, value: (jobType.id).toString() } as EDSAutocompleteOption));
	}

	/**
	 * @name getJobTypeForDWHRelease
	 * @desc get jobType list data for DWH release
	*/
	getJobTypeForDWHRelease(jobTypeUrl: any){
		this.jobType = this.jobTypeService.getAsync(jobTypeUrl)
		.map((jobType) => ({ label: jobType.name, value: (jobType.id).toString() } as EDSAutocompleteOption));
	}

	/**
	 * @name getProductionProjectTypeList
	 * @desc get production project type list data
	*/
	getProductionProjectTypeList(jobTypeUrl: any){
		this.productionProjectType$ = this.productionProjectTypeService.getAsync(jobTypeUrl)
		.pipe(
			map((productionProjectType) => productionProjectType.map((pjType) => ({ label: pjType.name, value: (pjType.id).toString() } as EDSAutocompleteOption)))
		);
		this.productionProjectType$.subscribe({
			next: (result) => {
				this.productionProjectTypeList = result;
			}
		});
	}

	/**
	 * @name getOperationList
	 * @desc get operation list data
	*/
	getOperationList(){
		this.operations$ = this.operationService.getAsync().pipe(
			map((operation) => {
				const allOperationIds = operation.map((r) => {
					const tag: TagItem = { id: r.id, name: r.name, selected: false };
					return tag;
				});
				allOperationIds.sort((firstId: TagItem, otherId: TagItem) => firstId.name.localeCompare(otherId.name));
				return allOperationIds.map((d) => ({ label: d.name, value: (d.id).toString() } as EDSAutocompleteOption));
			})
		);
		this.operations$.subscribe({
			next: (result) => {
				this.operationList = result;
				this.filterTrayCacheService.operationList = this.operationList;
			}
		});
	}

	/**
	 * @name getExportFormatList
	 * @desc get export format list data
	*/
	getExportFormatList(){
		this.exportFormat$ = this.exportFormatService.getAsync()
		.pipe(map((exportFormat) => exportFormat.map((d) => ({ label: d.description, value: (d.id).toString() } as EDSAutocompleteOption))));
		this.exportFormat$.subscribe({
			next: (result) => {
				this.exportFormatList = result;
				this.filterTrayCacheService.exportFormatList = this.exportFormatList;
			}
		});
	}

	/**
	 * @name getStatusExportList
	 * @desc get status export list data
	*/
	//getStatusExportList(){
	//	this.statusExports$ = this.statusService.getAsync('Exports').pipe(
	//		map((status) => {
	//			const merged = lodMerge(keyBy(this.allStatusIds, 'id'), keyBy(status, 'id'));
	//			this.allStatusIds = values(merged);
	//			const exportedState = this.allStatusIds.filter((status) => status.description.toLowerCase().indexOf('exported') === 0);
	//			const sendingState = this.allStatusIds.filter((status) => status.description.toLowerCase().indexOf('sending') === 0);

	//			if (exportedState.length === 1 && sendingState.length === 1) {
	//				const specialStatusArray = this.getSpecialStatusesForExport(exportedState[0].id, sendingState[0].id);
	//				if (specialStatusArray) {
	//					specialStatusArray.forEach((element) => {
	//						this.allStatusIds.push(element);
	//					});
	//				}
	//			}
	//			this.allStatusIds.sort((a, b) => a.description.localeCompare(b.description));
	//			return this.allStatusIds.map((d) => ({ label: d.description, value: (d.id).toString() } as EDSAutocompleteOption));
	//		})
	//	);
	//	this.statusExports$.subscribe({
	//		next: (result) => {
	//			this.statusExportsList = result;
	//		}
	//	});
	//}

  getStatusExportList() {
    this.statusExports$ = this.statusService.getAsync('Exports').pipe(
      map((status) => {
        const merged = lodMerge(keyBy(this.allStatusIds, 'id'), keyBy(status, 'id'));
        this.allStatusIds = values(merged);

        // Add sub-statuses
        const exportedState = this.allStatusIds.find((s) => s.description.toLowerCase() === 'exported');
        const sendingState = this.allStatusIds.find((s) => s.description.toLowerCase() === 'sending');

        if (exportedState && sendingState) {
          const specialStatusArray = this.getSpecialStatusesForExport(exportedState.id, sendingState.id);
          this.allStatusIds.push(...specialStatusArray);
        }

        this.allStatusIds.sort((a, b) => a.description.localeCompare(b.description));

        this.filterTrayCacheService.statusList = this.allStatusIds.map((d) => ({
          label: d.parent ? ` ${d.description}` : d.description, // Indent child
          value: d.id.toString()
        }) as EDSAutocompleteOption);

        return this.filterTrayCacheService.statusList;
      })
    );

    this.statusExports$.subscribe({
      next: (result) => {
        this.statusExportsList = result;
      }
    });
  }

	/**
	 * @name getUnitTypeList
	 * @desc get unit type list data
	*/
	getUnitTypeList(){
		this.unitType$ = this.unitTypeService.getAsync()
		.pipe(map((unitTypeList) => unitTypeList.map((unitType) => ({ label: unitType.name, value: (unitType.id).toString() } as EDSAutocompleteOption))));
		this.unitType$.subscribe({
			next: (result) => {
				this.unitTypeList = result;
			}
		});
	}

	/**
	 * @name getFileTypeList
	 * @desc get file type list data
	*/
	getFileTypeList(){
		this.fileType$ = this.fileTypeService.getAsync()
		.pipe(map((fileTypeList) => fileTypeList.map((fileType) => ({ label: fileType.name.toUpperCase(), value: (fileType.id).toString() } as EDSAutocompleteOption))));
		this.fileType$.subscribe({
			next: (result) => {
				this.fileTypeList = result;
				this.fileTypeData.emit(result);
			}
		});
	}

	/**
	 * @name getPeriodicitiesList
	 * @desc get unit type list data
	*/
	getPeriodicitiesList(){
		this.periodicity$ = this.periodicityService.getAsync()
		.pipe(map((periodicityList) => periodicityList.map((periodicty) => ({ label: periodicty.name, value: (periodicty.id).toString() } as EDSAutocompleteOption))));
		this.periodicity$.subscribe({
			next: (result) => {
				const dailyIndex = result.findIndex((x: any) => x.value === '1')
				result.splice(dailyIndex, 1);
				this.periodicityList = result.sort((a: any,b: any) => a.value - b.value);
			}
		});
	}

	/**
	 * @name userkey
	 * @desc sort user list
	 * @param val
	 */
	userkey(val: any): void {
		if (typeof val != 'object') {
			this.users$ = this.userService.getAsync(val).pipe(
				map((users) =>
					users.map((d) => ({ name: d.lastName + ', ' + d.firstName + ' (' + d.userName + ')', id: d.userId, userName: d.userName } as AutocompleteOption))
				)
			);
		}
	}

	/**
	 * @name filterDomainProductGroup
	 * @desc sort domain product group
	 * @param domainProductGroup
	 */
	filterDomainProductGroup(domainProductGroup: any) {
		const domainProductGroupIds = domainProductGroup.map((r: any) => {
			const tag: DomainProductGroups = {
				id: r.id,
				name: r.name + ' (' + r.id + ')',
				categoryId: r.categoryId,
				selected: false,
			};
			return tag;
		});
		return domainProductGroupIds.sort((firstId: DomainProductGroups, otherId: DomainProductGroups) => firstId.name.localeCompare(otherId.name));
	}

	/**
	 * @name filterWorkspace
	 * @desc sort Workspace
	 * @param workspace
	 */
	filterWorkspace(workspace: any) {
		const workspaceIds = workspace.map((r: any) => {
			const tag: TagItem = {
				id: r.id,
				name: r.name + ' (' + r.id + ')',
			};
			return tag;
		});
		return workspaceIds.sort((firstId: TagItem, otherId: TagItem) => firstId.name.localeCompare(otherId.name));
	}

	/**
	 * @name filterChannels
	 * @desc sort channels
	 * @param channels
	 */
	filterChannels(channels: any) {
		const channelIds = channels.map((r: any) => {
			const tag: TagItem = {
				id: r.id,
				name: r.name + ' (' + r.id + ')',
			};
			return tag;
		});
		return channelIds.sort((firstId: TagItem, otherId: TagItem) => firstId.name.localeCompare(otherId.name));
	}

	/**
	 * @name filterBaseChannels
	 * @desc sort channels
	 * @param baseChannels
	 */
	filterBaseChannels(baseChannels: any) {
		const baseChannelIds = baseChannels.map((r: any) => {
			const tag: TagItem = {
				id: r.id,
				name: r.name + ' (' + r.id + ')',
			};
			return tag;
		});
		return baseChannelIds.sort((firstId: TagItem, otherId: TagItem) => firstId.name.localeCompare(otherId.name));
	}

	/**
	 * @name selectedDateFilter
	 * @desc sort channels
	 * @param baseChannels
	 */
	selectedDateFilter(value: any) {
		this.filterFG.get('selectedDays')?.setValue(value.selectedDays);
		this.filterFG.get('startDate')?.setValue(value.startDate);
		this.filterFG.get('endDate')?.setValue(value.endDate);
	}

	selectedPeriodicity(value: any) {
		this.filterFG.get('periodicityIds')?.setValue(value.periodicityIds);
	}

	selectedPeriod(value: any) {
		this.filterFG.get('periodIds')?.setValue(value);
	}

	/**
	 * @name filterFGFormInitialization
	 * @desc initializes filterFG form 
	*/
	filterFGFormInitialization(){
		this.filterFG = this.formBuilder.group({
			countries: [[], { nonNullable: true }],
			users: [],
			status: [[], { nonNullable: true }],
			domainProductGroup: [],
			category: [null, { nonNullable: true }],
			sector: [[], { nonNullable: true }],
			selectedDays: null,
			startDate: null,
			endDate: null,
			feature: null,
			loadIds: [],
			jobIds: [],
			bcRearrangedIds: [],
			channelIds: [[], { nonNullable: true }],
			baseChannelIds: [[], { nonNullable: true }],
			periodicityIds: [null, { nonNullable: true }],
			periodIds: [[], { nonNullable: true }],
			productionProjectType: [[], { nonNullable: true }],
			productionProjectName: '',
			dataOrderIds: [],
			productionProjectIds: [],
	
			baseProjectIds: [],
			qcProjectIds: [],
			baseProjectName: '',
			baseProjectType: [[], { nonNullable: true }],
			qcProjectflag: [null, { nonNullable: true }],
			jobType: [[], { nonNullable: true }],
			qcProjectName: '',
	
			reportingProjectIds: [],
			exportIds: [],
			exportName: '',
			exportFormatIds: [[], { nonNullable: true }],
			reportingProjectName: null,
			reportingGroupIds: [],
			cdmDeliveryIds: [],
	
			statusProcessStageId: [],
	
			domainProductGroupType: [null, { nonNullable: true }],
			prepUnitIds: [],
			loadUnitIds: [],
	
			unitIds: [],
			importIds: [],
	
			scope: '',
			ruleSetName: '',
			targetComponentIds: [],
			sourceComponentIds: [],
			scopeTypeId: [null, { nonNullable: true }],
			runIds: [],
			ruleSetIds: [],
			loadDefinitionIds: [],
			rbProjectName:'',
			rbProjectIds: [],
			operationIds: [[], { nonNullable: true }],
			workspaceIds: [[], { nonNullable: true }],
			unitType: [[], { nonNullable: true }],
			fileType: [[], { nonNullable: true }]
		});
	}

	/**
	 * @name updateFormControl
	 * @desc update form controls values
	 */
	 updateFormControl(): void {
		const keys = [
			'countries', 'users', 'status', 'domainProductGroup', 'category',
			'sector', 'selectedDays', 'startDate', 'endDate', 'feature',
			'loadIds', 'jobIds', 'bcRearrangedIds', 'channelIds', 
			'baseChannelIds', 'periodicityIds', 'periodIds', 
			'productionProjectIds', 'dataOrderIds', 'productionProjectType', 
			'productionProjectName', 'baseProjectType', 'qcProjectflag', 
			'baseProjectName', 'baseProjectIds', 'qcProjectIds', 'jobType', 
			'qcProjectName', 'exportIds', 'reportingProjectIds', 
			'reportingGroupIds', 'cdmDeliveryIds', 'exportName', 
			'reportingProjectName', 'exportFormatIds', 'statusProcessStageId', 
			'domainProductGroupType', 'prepUnitIds', 'loadUnitIds', 
			'unitIds', 'importIds', 'scope', 'ruleSetName', 
			'scopeTypeId', 'targetComponentIds', 'sourceComponentIds', 
			'runIds', 'ruleSetIds', 'loadDefinitionIds', 'rbProjectIds', 
			'operationIds', 'rbProjectName', 'workspaceIds', 'unitType', 
			'fileType'
		];
	
		keys.forEach(key => {
			if (this.cacheObj[key] != null) {
				this.filterFG.get(key)?.setValue(this.cacheObj[key]);
			}
		});
	}

	filterStatusWithProcessStageId(): Status[] {
		return this.allStatusIds.filter((status) => status.selected && status.processStageId !== undefined);
	}

	public getSelectedProcessStageIds() {
		return this.filterStatusWithProcessStageId().map((status) => status.processStageId);
	}

	getSpecialStatusesForExport(exportId: number, sendingId: number): Status[] {
    //const arrStatus: Status[] = new Array<Status>();
    const arrStatus: Status[] = [];
		const sentObj = { description: 'Sent', processStageId: 1 };
		const sendingObj = { description: 'Sending', processStageId: 2 };
		const errorObj = { description: 'Error', processStageId: 4 };
		const exportedText = 'Exported + ';

		// Sent
    arrStatus.push(<Status>{
      description: `${exportedText}${sentObj.description}`,
      id: exportId + 1000,
      selected: false,
      processStageId: sentObj.processStageId,
      parent: 'Exported' // 👈
    });
    arrStatus.push(<Status>{
      description: `${exportedText}${sendingObj.description}`,
      id: exportId + 2000,
      selected: false,
      processStageId: sendingObj.processStageId,
      parent: 'Exported'
    });
    arrStatus.push(<Status>{
      description: `${exportedText}${errorObj.description}`,
      id: exportId + 3000,
      selected: false,
      processStageId: errorObj.processStageId,
      parent: 'Exported'
    });

    arrStatus.push(<Status>{
      description: `Sending + ${sendingObj.description}`,
      id: sendingId + + 5000,
      selected: false,
      processStageId: sendingObj.processStageId,
      parent: 'Sending'
    });

    return arrStatus;
  }

	//setExportStatus(merge: any) {
	//	this.allStatusIds = values(merge);
	//	const exportedState = this.allStatusIds.filter((status) => status.description.toLowerCase().indexOf('exported') === 0);
	//	const sendingState = this.allStatusIds.filter((status) => status.description.toLowerCase().indexOf('sending') === 0);
	//	if (exportedState.length === 1 && sendingState.length === 1) {
	//		const specialStatusArray = this.getSpecialStatusesForExport(exportedState[0].id, sendingState[0].id);
	//		if (specialStatusArray) {
	//			specialStatusArray.forEach((element) => {
	//				this.allStatusIds.push(element);
	//			});
	//		}
	//	}
	//	this.allStatusIds.sort((a, b) => a.description.localeCompare(b.description));
	//	return this.allStatusIds.map((d) => ({ name: d.description, id: d.id } as AutocompleteOption));
  //}

  setExportStatus(merge: any) {
    this.allStatusIds = values(merge);
    this.allStatusIds.sort((a, b) => a.description.localeCompare(b.description));
    return this.allStatusIds.map((d) => ({
      name: d.description,
      id: d.id
    }) as AutocompleteOption);
  }

}
