import { Component, ElementRef, ViewChild, OnInit, AfterContentInit, Input, HostListener, On<PERSON><PERSON>roy } from '@angular/core';
import { MatSort } from '@angular/material/sort';
import { MatTable, MatTableDataSource } from '@angular/material/table';
import { DWHRELEASE } from '@loadmonitor/shared/interfaces/dwhrelease';
import { PropertyPage } from '@loadmonitor/shared/interfaces/PropertyPage';
import { MatPaginator } from '@angular/material/paginator';
import { COMMA, ENTER } from '@angular/cdk/keycodes';
import { FormBuilder, FormControl, FormGroup } from '@angular/forms';
import { BehaviorSubject, Observable, Subject, Subscription } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { map, startWith } from 'rxjs/operators';
import { MatSnackBar } from '@angular/material/snack-bar';
import { DefaultSnackbarComponent } from '@loadmonitor/shared/components/default-snackbar/default-snackbar.component';
import { DwhReleaseFilterParams, DWHReleaseService } from '@loadmonitor/shared/services/dwhrelease.service';
import { HelperService } from '@loadmonitor/shared/services/helper.service';
import { JobTypes } from '@loadmonitor/shared/jobTypes';
import { TagItem } from '@loadmonitor/shared/interfaces/TagItem';
import { SettingsConstants } from '@loadmonitor/shared/settings.constants';
import { DetailMenuConfiguration } from '../../shared/interfaces/DetailMenuConfiguration';
import { MatBottomSheetRef } from '@angular/material/bottom-sheet';
import { animate, state, style, transition, trigger } from '@angular/animations';
import { DwhReleaseCache } from '@loadmonitor/shared/interfaces/caching/DwhReleaseCache';
import { MatDialog } from '@angular/material/dialog';
import { CreateTicketDialogComponent } from '@loadmonitor/shared/components/create-ticket-dialog/create-ticket-dialog.component';
import { DwhReleaseJiraProperty } from '@loadmonitor/shared/interfaces/DwhReleaseJiraProperty';
import { SelectionModel } from '@angular/cdk/collections';
import { Filter, FilterRecords, FilterService, FilterTrayComponent } from '@dwh/lmx-lib/src';
import { DatePipe } from '@angular/common';
import { ToggleButtonComponent } from '@gfk/ng-lib';
import { environment } from 'apps/loadmonitor/src/environments/environment';
import { CountryService } from '@loadmonitor/shared/services/country.service';
import { UserService } from '@loadmonitor/shared/services/user.service';
import { CountrySelectService } from '@loadmonitor/shared/services/country-select.service';
import { StatusService } from '@loadmonitor/shared/services/status.service';
import { StatusSelectService } from '@loadmonitor/shared/services/status-select.service';
import { JobTypeService } from '@loadmonitor/shared/services/job-type.service';
import { DomainProductGroupService } from '@loadmonitor/shared/services/domainProductgroup.service';
import { DomainProductGroups } from '@loadmonitor/shared/interfaces/DomainProductGroups';
import { CategoryService } from '@loadmonitor/shared/services/category.service';
import { BaseProjectTypeSelectService } from '@loadmonitor/shared/services/base-project-type-select.service';
import { SectorService } from '@loadmonitor/shared/services/sector.service';
import * as moment from 'moment';
import { FilterTrayCacheService } from '@loadmonitor/shared/services/filter-tray-cache.service';

@Component({
	selector: 'lm-dwh-release',
	templateUrl: './dwhrelease.component.html',
	styleUrls: ['./dwhrelease.component.scss'],
	animations: [
		trigger('detailExpand', [
			state('collapsed', style({ height: '0px', minHeight: '0' })),
			state('expanded', style({ height: '*' })),
			transition('expanded <=> collapsed', animate('225ms cubic-bezier(0.4, 0.0, 0.2, 1)')),
		]),
	],
})
export class DwhReleaseComponent implements OnInit, AfterContentInit, OnDestroy {
	/* *********
	 * ViewChild
	 ********* */
	@Input() icon = 'delete';
	@ViewChild(MatSort)
	sort: MatSort = new MatSort();

	@ViewChild(MatTable)
	table!: MatTable<DWHRELEASE>;

	@ViewChild(MatPaginator)
	paginator!: MatPaginator;

	@ViewChild('statusIdsInput')
	statusIdsInput!: ElementRef<HTMLInputElement>;

	@ViewChild(FilterTrayComponent) filterTrayComponent: FilterTrayComponent | undefined;

	@ViewChild(ToggleButtonComponent) togglebuttonComponent: ToggleButtonComponent | undefined;

	/* *********************
	 * Components variables
	 ********************* */
	jobType: JobTypes = JobTypes.DWHRelease;
	withOrWithoutQcId!: boolean;

	selectedFilters: any;
	cacheValue: BehaviorSubject<any> = new BehaviorSubject<any>({});
	resetForm: Subject<boolean> = new Subject();
	resetPeriodicityFilter: Subject<boolean> = new Subject();
	isPeriodFilterHidden = false;

	selectedData!: DWHRELEASE;
	detailMenuConfiguration!: DetailMenuConfiguration;
	tableView!: 'default' | 'selectable';
	anyRbaseProjectLabelowSelected!: boolean;
  anyRowSelected!: boolean;
	bottomSheetObject!: MatBottomSheetRef;

	/* *********************
	 * Components parameters
	 ********************* */

	// Progress Spinner visibility
	isLoading = false;
	selectedRow: any;

	public cacheObj: DwhReleaseCache = {} as DwhReleaseCache;
	cacheName = 'cache::DwhRelease';

	//Mat-Table
	dataSource = new MatTableDataSource<DWHRELEASE>();
	selection = new SelectionModel<DWHRELEASE>(true, []);
	IsSelectAll = false;
	displayedColumns: string[] = [
		'details',
		'menu',
		'loadId',
		'jobType',
		'qcProjectId',
		'baseProjectName',
		'baseProjectType',
		'periodDesc',
		'status',
		'message',
		'jobId',
		'jeeJobInfo',
		'createdBy',
		'created',
		'started',
		'finished',
		'runtime',
	];

	//Mat-Expansion-Panel
	panelOpenState = false;

	//Tag-Chip-List
	readonly separatorKeysCodes = [ENTER, COMMA] as const;

	//date format
	readonly date_with_time = SettingsConstants.DATE_FORMAT_WITH_TIME;
	readonly only_time = SettingsConstants.FORMAT_ONLY_TIME;

	DwhReleaseForm!: FormGroup;

	// Mat-Chips
	statusIdsCtrl = new FormControl();
	filteredStatusIds: Observable<TagItem[]>;
	allStatusIds: TagItem[] = [];

	// Mat-Chips - BaseProject Type
	expandedElement!: PropertyPage | null;
	dwhReleaseDetail!: PropertyPage[];

	/* **************
	 * Public Methods
	 ************** */

	/* *********************
	 * Components variables
	 ********************* */
	eventsSubject: Subject<void> = new Subject<void>();
	IsDisabled = false;
	environments = environment.environments;
	IsEnabled = false;
	FilterTrayOpenFlag = true;
	currentfilter = JSON.parse(localStorage.getItem('QuickFilter') || '[]').filter(
		(x) => x.user == localStorage.getItem('username') && x.slice == window.location.href.split('/')[3]
	);
	modalshow = false;
	filterCount = '';
	toggleInterval!: any;
	timer = 60;
	EnableRefresh = true;
	cacheInterval!: any;
	defaultoption = localStorage.getItem(this.cacheName + '-LastFilter') || 'Default';
	defaultfilter = JSON.parse(localStorage.getItem(this.cacheName + '-DefaultFilter') || '[]');
	private subscription!: Subscription | undefined;
	refreshedDateTime!: boolean;
	dwhReleaseData!: any[];

	@HostListener('document:visibilitychange', ['$event'])
	appVisibility() {
		if (document.hidden) {
			if (this.cacheInterval) {
				clearInterval(this.cacheInterval);
			}
			if (this.toggleInterval) {
				clearInterval(this.toggleInterval);
			}
		} else {
			if (this.cacheObj.isAutoRefresh === true) {
				this.cacheInterval = setInterval(() => {
					if (this.defaultoption != 'Default') {
						this.refresh();
					}
				}, this.timer * 1000);
			} else {
				if (this.cacheInterval !== undefined) clearInterval(this.cacheInterval);
				if (this.toggleInterval !== undefined) clearInterval(this.toggleInterval);
			}
		}
	}
	private destroy$ = new Subject<void>();

	constructor(
		private dwhReleaseService: DWHReleaseService,
		private snackBar: MatSnackBar,
		private formBuilder: FormBuilder,
		private helperService: HelperService,
		public dialog: MatDialog,
		public filterService: FilterService,
		public datepipe: DatePipe,
		private filterTrayCacheService: FilterTrayCacheService,
		private jobTypeService: JobTypeService
	) {
		this.filteredStatusIds = this.statusIdsCtrl.valueChanges.pipe(
			startWith(null),
			map((item: TagItem | null) => (item ? this.tagItemsFilter(item.name) : this.allStatusIds.slice()))
		);
	}

	private updateFC(): void {
		if(this.cacheObj){
			this.filterTrayCacheService.getCacheValueData(this.cacheObj);
		}
	}

	ngAfterContentInit() {
		this.afterChildComponentsLoaded();
		this.updateFC();
		this.CreateFilterSummaryChips();
		this.SetFiltersCount();
		if (this.cacheObj.isAutoRefresh === true) {
			if (this.cacheInterval === undefined) {
				this.cacheInterval = window.setInterval(() => {
					if (this.defaultoption != 'Default') {
						this.refresh();
					}
				}, this.timer * 1000);
			}
		} else {
			if (this.cacheInterval !== undefined) clearInterval(this.cacheInterval);
			if (this.toggleInterval !== undefined) clearInterval(this.toggleInterval);
		}
	}

	afterChildComponentsLoaded() {
		this.cacheObj = JSON.parse(localStorage.getItem(this.cacheName) || '{}');
	}

	checkAnyRowSelected(selected: boolean, row: DWHRELEASE) {
		this.anyRowSelected = selected;
		row.isSelected = selected;
	}

	setBottomSheetObject(bottomSheetObj: MatBottomSheetRef) {
		this.bottomSheetObject = bottomSheetObj;
	}

	ngOnInit(): void {
		if (this.environments.filter((x) => x.location == window.location.origin)[0]) {
			this.IsEnabled = this.environments.filter((x) => x.location == window.location.origin)[0].enabled;
		} else {
			this.IsEnabled = false;
		}
		this.DwhReleaseForm = this.formBuilder.group({
			isAutoRefresh: this.togglebuttonComponent?.checked,
		});
		this.updateFC();
	}

	saveInCache(autoRefreshStatus, AutoToggle) {
		this.setFormValue(autoRefreshStatus);
		localStorage.setItem(this.cacheName, JSON.stringify(this.selectedFilters));

		this.cacheObj = JSON.parse(localStorage.getItem(this.cacheName) || '{}');
		if(this.cacheObj.qcProjectName == null){
			this.cacheObj.qcProjectName = '';
		}
		if(this.cacheObj.periodIds && this.cacheObj.periodIds.length == 0){
			this.cacheObj.periodIds = null;
		}
		if (!AutoToggle) {
			this.defaultfilter = [];
			this.defaultfilter.push({
				data: this.selectedFilters,
				slice: window.location.href.split('/')[3],
				user: localStorage.getItem('username'),
			});
			localStorage.setItem(this.cacheName + '-DefaultFilter', JSON.stringify(this.defaultfilter));
			let nofilter = 0;
			let selectedfilter = '';
			this.currentfilter.forEach((i) => {
				// if (i.filter == this.defaultoption) {
				if(JSON.stringify(i.data) == JSON.stringify(this.cacheObj)){
					nofilter++;
					selectedfilter = i.filter;
				}
			});
			if (nofilter == 0) {
				this.defaultoption = 'Default';
			} else {
				this.defaultoption = selectedfilter;
			}

			localStorage.setItem(this.cacheName + '-LastFilter', this.defaultoption);
		}

		this.updateFC();
	}

	selectRow(row: DWHRELEASE) {
		this.dwhReleaseDetail = [];
		row.isSelected = true;

		this.dataSource.data.forEach((element) => {
			if (row.loadId != element.loadId) {
				element.isExpanded = false;
				element.isSelected = false;
			}
		});

		this.selectedRow = row;
		this.dwhReleaseService.getDetailAsync(row.loadId).subscribe((result) => {
			this.helperService.floatingPanelData$.next({ data: result.records, jobType: JobTypes.DWHRelease });
		});
	}

	checkVisibility(element: DWHRELEASE) {
		const isAllowedJobType =
			element.jobType.toLowerCase().trim() === 'createshop execute' || element.jobType.toLowerCase().trim() === 'createshop template';
		return isAllowedJobType;
	}

	changeQCProject(WithQcProject: any) {
		this.withOrWithoutQcId = WithQcProject;
	}

	getFilterFormParams(): DwhReleaseFilterParams {
		const selectedJobTypeList: any = [];
		this.jobTypeService.getAsync(JobTypes.DWHRelease).forEach((jobType: any) => {
			this.selectedFilters.jobType.forEach((selectedJobType: any) => {
				if(jobType.id == selectedJobType){
					selectedJobTypeList.push(jobType.name);
				}
			})
		})
		return {
			loadIds: this.selectedFilters.loadIds,
			jeejobIds: this.selectedFilters.jobIds,
			countryIds: this.selectedFilters.countries?.map((country) => country),
			periodIds: this.selectedFilters.periodIds?.map((period) => period),
			periodicityIds: this.selectedFilters.periodicityIds ? [this.selectedFilters.periodicityIds] : [],
			baseProjectIds: this.selectedFilters.baseProjectIds,
			qcProjectIds: this.selectedFilters.qcProjectIds,
			baseProjectTypeIds: this.selectedFilters.baseProjectType?.map((base) => base),
			jobTypes: selectedJobTypeList,
			qcProjectName: this.selectedFilters.qcProjectName,
			userNames:  this.selectedFilters.users?.map((users) => users.userName),
			statusIds: 		this.selectedFilters.status?.map((status) => status),
			startDate:  this.selectedFilters.startDate ? this.selectedFilters.startDate : null,
      		endDate:   this.selectedFilters.endDate ? moment(this.selectedFilters.endDate).format().split('T')[0] + 'T23:59:59.999Z' : null,
			categoryIds: this.selectedFilters.category?.map((category) => category.id),
			sectorIds:    this.selectedFilters.sector?.map((sector) => sector),
			domainProductGroupIds: this.selectedFilters.domainProductGroup?.map((domainProductGroup) => domainProductGroup.id),
   };
  }

	refresh(filterParams?: DwhReleaseFilterParams): void {
		this.refreshedDateTime = false;
		this.filterTrayComponent?.showSpinner(true);
		this.isLoading = true;
		this.destroy$.next();
		this.anyRowSelected = false;

		this.dwhReleaseService.getAsync(filterParams || this.getFilterFormParams())
		.pipe(
			takeUntil(this.destroy$)
		)
		.subscribe((result) => {
			this.refreshedDateTime = true;
			if (result.moreRecordsAvailable) {
				this.notify('More than 1000 results exist!');
			}

			if (result.count === 0) {
				this.notify('No result found!');
				this.dwhReleaseData = [];
			}
			this.isLoading = false;
			this.dwhReleaseData = result.records;
			result.records.forEach((element) => {
				element.detailMenuConfig = [];
				if (this.checkVisibility(element)) {
					element.detailMenuConfig.push({
						isShowDetails: true,
						menuText: 'ShowDetails',
						menuIcon: 'open_in_new',
						service: 'showpublishlog',
						params: { loadid: element.loadId },
					});
				}

				// set runtime column
				if (element.started != null && element.finished != null) {
					element.runtime = new Date();
					const startedDate = new Date(element.started);
					const finishedDate = new Date(element.finished);
					element.runtime.setHours(finishedDate.getHours() - startedDate.getHours());
					element.runtime.setMinutes(finishedDate.getMinutes() - startedDate.getMinutes());
					element.runtime.setSeconds(finishedDate.getSeconds() - startedDate.getSeconds());
				}
			});

			this.dataSource = new MatTableDataSource<DWHRELEASE>(result.records);
			this.dataSource.sort = this.sort;
			this.paginator.pageIndex = 0;
			this.dataSource.paginator = this.paginator;
			this.table.dataSource = this.dataSource;
			this.filterTrayComponent?.showSpinner(false);
		},
		(error) => {
			if (error.status === 504) {
				this.notify('Gateway Timeout. The server took too long to respond. Please try again later.');
			} 
			else {
				this.notify(`An error occurred: ${error.message}`);
			}
			this.isLoading = false;
			this.filterTrayComponent?.showSpinner(false);
		});

		this.SetFiltersCount();
	}

	private tagItemsFilter(value: string): TagItem[] {
		const filterValue = value.toLowerCase();

		return this.allStatusIds.filter((item) => item.name.toLowerCase().indexOf(filterValue) === 0);
	}

	private notify(message: string) {
		this.snackBar.openFromComponent(DefaultSnackbarComponent, {
			duration: 10 * 1000,
			data: message,
		});
	}

	clickNextButton() {
		this.selectedRow.isSelected = false;
	}

	rowToggleSelection(ev, item) {
		item.isChecked = ev.checked;
	}

	checkAtleastOneRowChecked() {
		return this.dataSource.data.filter((i) => i.isChecked).length > 0;
	}

	openJiraTicket(): void {
		const checkedList = this.dataSource.data.filter((i) => i.isChecked);
		const dwhReleaseJiraProperties: Array<DwhReleaseJiraProperty> = new Array<DwhReleaseJiraProperty>();

		checkedList.map(function (value) {
			dwhReleaseJiraProperties.push({
				jeeJobId: value.jobId,
				jobType: value.jobType,
				qcProjectId: value.qcProjectId,
				status: value.status,
				errorMessage: value.message,
			});
		});

		const dialogRef = this.dialog.open(CreateTicketDialogComponent, {
			width: '1000px',
			height: '487px',
			data: {
				dwhReleaseJiraProperties: dwhReleaseJiraProperties,
				jobType: this.jobType,
				title: 'DwhRelease',
			},
		});

		dialogRef.afterClosed().subscribe((resSubmit) => {
			if (resSubmit === true) {
				this.dataSource.data.forEach((i) => (i.isChecked = false));
				this.selection.clear();
				this.IsSelectAll = false;
			}
		});
	}

	selectRows(selectrow) {
		this.IsSelectAll = !this.IsSelectAll;
		let endIndex: number;
		const page = this.dataSource.paginator !== null ? this.dataSource.paginator.pageSize : 0;
		const pageindex = this.dataSource.paginator !== null ? this.dataSource.paginator.pageIndex : 0;

		if (!selectrow.checked) {
			this.selection.clear();
			if (this.dataSource.data.length > (pageindex + 1) * page) {
				endIndex = (pageindex + 1) * page;
			} else {
				endIndex = this.dataSource.data.length;
			}

			for (let index = pageindex * page; index < endIndex; index++) {
				const row = this.dataSource.data[index];
				this.selection.deselect(row);
				this.dataSource.data[index].isChecked = false;
			}
		} else {
			if (this.dataSource.data.length > (pageindex + 1) * page) {
				endIndex = (pageindex + 1) * page;
			} else {
				endIndex = this.dataSource.data.length;
			}
			for (let index = pageindex * page; index < endIndex; index++) {
				const row = this.dataSource.sortData(this.dataSource.data, this.sort)[index];
				this.selection.select(row);
				this.dataSource.sortData(this.dataSource.data, this.sort)[index].isChecked = true;
			}
		}
	}

	isSelectedPage() {
		const numSelected = this.selection.selected.length;
		const page = this.dataSource.paginator !== null ? this.dataSource.paginator.pageSize : 0;
		const pageindex = this.dataSource.paginator !== null ? this.dataSource.paginator.pageIndex : 0;
		let endIndex: number;
		if (this.dataSource.data.length > (pageindex + 1) * page) {
			endIndex = (pageindex + 1) * page;
		} else {
			endIndex = this.dataSource.data.length - pageindex * page;
		}

		return numSelected === endIndex;
	}

	//Quick Filter Methods//
	openmodal() {
		this.eventsSubject.next();
	}

	disablesave(status) {
		this.IsDisabled = status;
	}

	setFormValue(autoRefreshStatus) {
		this.DwhReleaseForm.setValue({
			isAutoRefresh: autoRefreshStatus === undefined || autoRefreshStatus === false ? false : true,
		});
	}

	saveInQuickFilter(autoRefreshStatus, filterName) {
		this.setFormValue(autoRefreshStatus);
		this.currentfilter = JSON.parse(localStorage.getItem('QuickFilter') || '[]');
		this.currentfilter.push({
			data: this.selectedFilters,
			filter: filterName,
			slice: window.location.href.split('/')[3],
			user: localStorage.getItem('username'),
		});
		this.defaultoption = filterName;
		const oldcache = this.cacheObj;
		const olddefault = localStorage.getItem(this.cacheName + '-DefaultFilter');
		this.ConfirmApply(autoRefreshStatus);
		this.cacheObj = oldcache;
		localStorage.setItem(this.cacheName + '-DefaultFilter', olddefault || '[]');
		localStorage.setItem('QuickFilter', JSON.stringify(this.currentfilter));
		localStorage.setItem(this.cacheName + '-LastFilter', filterName);
	}

	applyfilter(selectedvalue) {
    this.resetForm.next(true);
		const oldautotoggle = this.cacheObj.isAutoRefresh;
		selectedvalue = selectedvalue.value ? selectedvalue.value : selectedvalue;
		this.defaultoption = selectedvalue;
		localStorage.setItem(this.cacheName + '-LastFilter', this.defaultoption || 'Default');
		if (selectedvalue == 'Default') {
			if (
				JSON.parse(localStorage.getItem(this.cacheName + '-DefaultFilter') || '[]').filter(
					(x) => x.user == localStorage.getItem('username') && x.slice == window.location.href.split('/')[3]
				)[0]
			) {
				this.cacheObj = JSON.parse(localStorage.getItem(this.cacheName + '-DefaultFilter') || '[]').filter(
					(x) => x.user == localStorage.getItem('username') && x.slice == window.location.href.split('/')[3]
				)[0]['data'];
			}

			localStorage.setItem(this.cacheName, JSON.stringify(this.cacheObj));
		} else {
			this.cacheObj = JSON.parse(localStorage.getItem('QuickFilter') || '[]')
				.filter((x) => x.user == localStorage.getItem('username') && x.slice == window.location.href.split('/')[3])
				.filter((x) => x.filter == selectedvalue)[0]['data'];
			localStorage.setItem(this.cacheName, JSON.stringify(this.cacheObj));
		}
		this.cacheObj.isAutoRefresh = oldautotoggle;
		this.CreateFilterSummaryChips();
    this.selectedFilters = this.cacheObj;
    if (this.cacheObj.selectedDays == '3') {
			const date = new Date();
			this.cacheObj.startDate = new Date(Date.UTC(date.getFullYear(), date.getMonth(), date.getDate() - 2, 0, 0, 0, 0));
			this.cacheObj.endDate = new Date(Date.UTC(date.getFullYear(), date.getMonth(), date.getDate(), 0, 0, 0, 0));
		} else if (this.cacheObj.selectedDays == '2') {
			const date = new Date();
			this.cacheObj.startDate = new Date(Date.UTC(date.getFullYear(), date.getMonth(), date.getDate(), 0, 0, 0, 0));
			this.cacheObj.endDate = new Date(Date.UTC(date.getFullYear(), date.getMonth(), date.getDate(), 0, 0, 0, 0));
		}
		setTimeout(() => {
			this.updateFC();
			this.refresh();
		}, 200);
		localStorage.setItem(this.cacheName, JSON.stringify(this.cacheObj));
	}

	CreateFilterSummaryChips() {
		if (this.cacheObj && Object.keys(this.cacheObj).length) {
			this.datesFilterSummary(this.cacheObj.startDate, this.cacheObj.endDate);
			this.usersFilterSummary();
			this.statusFilterSummary();
			this.countryFilterSummary();
			this.sectorFilterSummary();
			this.categoryFilterSummary();
			this.domainFilterSummary();
			this.periodicityFilterSummary();
			this.perriodFilterSummary();
			this.statusFilterSummary();
			this.loadidsFilterSummary();
			this.jeeJobIDFilterSummary();
			this.baseProjectidsFilterSummary();
			this.qcProjectIdsFilterSummary();
			this.jobTypeFilterSummary();
			this.baseprojectTypeFilterSummary();
			this.qcFilterSummary();
		}
	}

	ConfirmApply(autoRefreshStatus) {
		this.saveInCache(autoRefreshStatus, false);
		this.CreateFilterSummaryChips();

		let count;
		this.filterService.filtersSelected$.subscribe((selectedFilters: FilterRecords) => {
			count = Number(Object.keys(selectedFilters).length);
		});

		if (count > 0) {
			this.refresh();
		} else {
			if (confirm('Loading results without any filters selected will take some time to load. Are you sure you want to proceed?')) {
				this.refresh();
			}
		}
	}

	public isShownFilterTrayEventHandler($event: boolean) {
		this.filterTrayComponent?.toggleShowTray($event);
		this.FilterTrayOpenFlag = $event;
	}

	getWidth() {
		this.FilterTrayOpenFlag = !this.FilterTrayOpenFlag;
		return '100%';
	}

	filterSummaryChipClear(filteredItems) {
		if (filteredItems == 'ALL') {
			if (confirm('This will remove all of your selected filters and could not be reversed. Are you sure you want to do that?')) {
				this.resetForm.next(true);
				this.filterTrayCacheService.getCacheNameData(this.cacheName);
				this.resetPeriodicityFilter.next(true);
        		this.filterCount = '';
			}
		}
		this.saveInCache(this.cacheObj.isAutoRefresh, false);
		this.CreateFilterSummaryChips();
		this.SetFiltersCount();
	}

	toggleChecked(check: any) {
		if (check === true) {
			this.saveInCache(check, true);
			this.toggleInterval = setInterval(() => {
				if (this.defaultoption != 'Default') {
					this.refresh();
				}
			}, this.timer * 1000);
		} else {
			if (this.toggleInterval !== undefined) clearInterval(this.toggleInterval);
			if (this.cacheInterval !== undefined) clearInterval(this.cacheInterval);
			this.saveInCache(check, true);
		}
	}

	SetFiltersCount() {
		let count;
		this.filterService.filtersSelected$.subscribe((selectedFilters: FilterRecords) => {
			console.log(selectedFilters)
			count = Number(Object.keys(selectedFilters).length);
		});
		if (count > 0) {
			this.filterCount = '(' + count.toString() + ')';
		} else {
			this.filterCount = '';
		}
	}

	ngOnDestroy() {
		if (this.cacheInterval) {
			clearInterval(this.cacheInterval);
		}

		if (this.toggleInterval) {
			clearInterval(this.toggleInterval);
		}
		this.destroy$.next();
	    this.destroy$.complete();
	}

	usersFilterSummary() {
		this.filterService.setFilters({
			userIds: {
				name: 'User',
				values: this.cacheObj.users?.map((i) => i.name),
				slice: window.location.href.split('/')[3],
			} as Filter,
		});
	}

	statusFilterSummary() {
		this.filterService.setFilters({
			statusIds: {
				name: 'Status',
				values: this.cacheObj.status,
				slice: window.location.href.split('/')[3],
			} as Filter,
		});
	}

	countryFilterSummary() {
		this.filterService.setFilters({
			countryIds: {
				name: 'Country',
				values: this.cacheObj.countries,
				slice: window.location.href.split('/')[3],
			} as unknown as Filter,
		});
	}
	domainFilterSummary() {
		this.filterService.setFilters({
			domainProductGroupIds: {
				name: 'Domain',
				values: this.cacheObj.domainProductGroup,
				slice: window.location.href.split('/')[3],
			} as Filter,
		});
	}

	sectorFilterSummary() {
		this.filterService.setFilters({
			sectorids: {
				name: 'Sector',
				values: this.cacheObj.sector,
				slice: window.location.href.split('/')[3],
			} as Filter,
		});
	}
	categoryFilterSummary() {
		this.filterService.setFilters({
			categoryIds: {
				name: 'Category',
				values: this.cacheObj.category,
				slice: window.location.href.split('/')[3],
			} as Filter,
		});
	}

	periodicityFilterSummary() {
		this.filterService.setFilters({
			periodicityIds: {
				name: 'Periodicity',
				values: this.cacheObj.periodicityIds? [{ value: this.cacheObj.periodicityIds, label: this.cacheObj.periodicityIds }] : null,
				slice: window.location.href.split('/')[3],
			} as Filter,
		});
	}

	perriodFilterSummary() {
		this.filterService.setFilters({
			Period: {
				name: 'Period',
				values: this.cacheObj.periodIds,
				slice: window.location.href.split('/')[3],
			} as Filter,
		});
	}

	loadidsFilterSummary() {
		this.filterService.setFilters({
			loadIds: {
				name: 'LoadIDs',
				values: this.cacheObj.loadIds,
				slice: window.location.href.split('/')[3],
			} as Filter,
		});
	}
	baseProjectidsFilterSummary() {
		this.filterService.setFilters({
			baseProjectIds: {
				name: 'baseProjectIds',
				values: this.cacheObj.baseProjectIds,
				slice: window.location.href.split('/')[3],
			} as Filter,
		});
	}
	qcProjectIdsFilterSummary() {
		this.filterService.setFilters({
			qcProjectIds: {
				name: 'qcProjectIds',
				values: this.cacheObj.qcProjectIds,
				slice: window.location.href.split('/')[3],
			} as Filter,
		});
	}

	jobTypeFilterSummary() {
		this.filterService.setFilters({
			JobType: {
				name: 'JobType',
				values: this.cacheObj.jobType,
				slice: window.location.href.split('/')[3],
			} as Filter,
		});
	}

	baseprojectTypeFilterSummary() {
		this.filterService.setFilters({
			BaseProjectType: {
				name: 'BaseProjectType',
				values: this.cacheObj.baseProjectType,
				slice: window.location.href.split('/')[3],
			} as Filter,
		});
	}

	qcFilterSummary() {
		this.filterService.setFilters({
			qc: {
				name: 'qc',
				values: this.cacheObj.qcProjectName,
				slice: window.location.href.split('/')[3],
			} as unknown as Filter,
		});
	}

	jeeJobIDFilterSummary() {
		this.filterService.setFilters({
			jeeJobIds: {
				name: 'jeeJobIds',
				values: this.cacheObj.jobIds,
				slice: window.location.href.split('/')[3],
			} as Filter,
		});
	}

	datesFilterSummary(dateStart: any, dateEnd: any) {
		if (dateStart != null || dateEnd != null) {
			const start = dateStart == 'Invalid Date' || dateStart == null ? '' : this.datepipe.transform(dateStart, 'yyyy-MM-dd');
			const end = dateEnd == 'Invalid Date' || dateEnd == null ? '' : this.datepipe.transform(dateEnd, 'yyyy-MM-dd');
			this.filterService.setFilters({
				dates: {
					name: 'Date',
					values: (start !== undefined && end !== undefined) ? [start, end] : null,
					slice: window.location.href.split('/')[3],
				} as Filter,
			});
		} else {
			this.filterService.setFilters({
				dates: {
					name: 'Date',
					values: null,
					slice: window.location.href.split('/')[3],
				} as unknown as Filter,
			});
		}
	}

	//Quick Filter Methods End//
  getSelectedFilters(selectedFilters: any) {
		this.cacheObj = selectedFilters;
		this.selectedFilters = selectedFilters;
	}
}
