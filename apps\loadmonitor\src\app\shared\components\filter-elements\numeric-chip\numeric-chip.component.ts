import { Component, Input, Output, EventEmitter, OnInit, SimpleChanges, OnChanges } from '@angular/core';
import { COMMA, ENTER } from '@angular/cdk/keycodes';
import { MatChipInputEvent } from '@angular/material/chips';
import { FormControl, Validators } from '@angular/forms';
import { map, Observable } from 'rxjs';


@Component({
  selector: 'lm-numeric-chip',
  templateUrl: './numeric-chip.component.html',
  styleUrls: ['./numeric-chip.component.scss'],
})
export class NumericChipComponent implements OnInit, OnChanges {
  numericControl: FormControl;
  isValid = false;
  numberRegEx = /^[0-9]*$/;

  constructor() {
    this.numericControl = new FormControl(null, {
      validators: Validators.pattern(this.numberRegEx),
    });
  }

  @Input() label = 'numeric chip';
  @Input() ids: number[] = [];
  @Output() valueChanges = new EventEmitter<any[]>();

  selectedIds$!: Observable<number[]>;
  readonly separatorKeysCodes = [ENTER, COMMA] as const;

  ngOnInit(): void {
    this.selectedIds$ = this.numericControl.valueChanges.pipe(
      map(() =>
        this.ids,
      )
    );
    this.selectedIds$.subscribe((loaddefinitionIds) => {
      this.valueChanges.emit(loaddefinitionIds);
    });
  }

  removeAllIds(){
    this.ids.splice(0,this.ids.length);
    this.numericControl.reset();
  }

  addChips(event: MatChipInputEvent): void {
    const value = (event.value || '').trim();
    if (Number(value) !== 0) {
      if (!Number(value) || Number(value) < 0) {
        return;
      }
    }

    if (value && this.ids.indexOf(Number(value)) < 0) {
      this.ids.push(Number.parseInt(value, 10)); //Push if valid

    }
    event.chipInput?.clear();
  }

  removeChips(item: number): void {
    const index = this.ids.indexOf(item);

    if (index >= 0) {
      this.ids.splice(index, 1);
    }
  }

  paste(event: ClipboardEvent): void {
    event.preventDefault(); //Prevents the default action
    event.clipboardData
      ?.getData('Text')
      .split(',')
      .forEach((value) => {
        if (value.trim()) {
          if (Number(value) && this.ids.indexOf(Number(value)) < 0) {
            this.ids.push(Number.parseInt(value, 10)); //Push if valid
          }
        }
      });
  }

  ngOnChanges(changes: SimpleChanges){
    this.ids=[];
    changes.ids.currentValue.forEach((i) => {
      this.ids.push(Number.parseInt(i, 10));
    });
  }
}
