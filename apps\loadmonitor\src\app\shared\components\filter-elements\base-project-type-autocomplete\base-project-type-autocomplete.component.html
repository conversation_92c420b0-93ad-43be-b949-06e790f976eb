<mat-form-field class="chip-list" appearance="outline">
  <mat-label>{{fieldName}} </mat-label>
  <mat-chip-list class="chip-list-wrapper" #chipListBaseProjectType aria-label="BaseProjectType selection">
    <mat-chip *ngFor="let item of baseProjectTypeIds | async" [selectable]="true" [removable]="true"
      (removed)="removeBaseProjectTypeChips(item)">
      {{ item.name }}
      <mat-icon matChipRemove *ngIf="true">cancel</mat-icon>
    </mat-chip>
    <input placeholder="New ..." #baseProjectTypeIdsInput [formControl]="baseProjectTypeIdsCtrl"
      [matAutocomplete]="autoBaseProjectType" [matChipInputFor]="chipListBaseProjectType"
      [matChipInputSeparatorKeyCodes]="separatorKeysCodes" class="js-filter-inp-baseProjectType" />
  </mat-chip-list>
  <mat-autocomplete #autoBaseProjectType="matAutocomplete" (optionSelected)="selectedBaseProjectTypeChips($event)"
    [autoActiveFirstOption]="true">
    <mat-option *ngFor="let item of filteredBaseProjectTypeIds | async" [value]="item">
      <div (click)="optionClicked($event, item)">
        <mat-checkbox class="option-checkbox" [checked]="item.selected" (change)="toggleSelection(item)"
          (click)="$event.stopPropagation()">
          {{ item.name }}
        </mat-checkbox>
      </div>
    </mat-option>
  </mat-autocomplete>
</mat-form-field>
