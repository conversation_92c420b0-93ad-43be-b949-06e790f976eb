{"name": "lmx-bff", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/lmx-bff/src", "projectType": "application", "targets": {"build": {"executor": "@nx/webpack:webpack", "outputs": ["{options.outputPath}"], "defaultConfiguration": "production", "options": {"target": "node", "compiler": "tsc", "outputPath": "dist/apps/lmx-bff", "main": "apps/lmx-bff/src/main.ts", "tsConfig": "apps/lmx-bff/tsconfig.app.json", "assets": ["apps/lmx-bff/src/assets"], "webpackConfig": "apps/lmx-bff/webpack.config.js"}, "configurations": {"development": {}, "production": {"optimization": true, "extractLicenses": true, "inspect": false, "fileReplacements": [{"replace": "apps/lmx-bff/src/environments/environment.ts", "with": "apps/lmx-bff/src/environments/environment.prod.ts"}], "externalDependencies": "none"}}}, "serve": {"executor": "@nx/js:node", "defaultConfiguration": "development", "options": {"buildTarget": "lmx-bff:build"}, "configurations": {"development": {"buildTarget": "lmx-bff:build:development"}, "production": {"buildTarget": "lmx-bff:build:production"}}}, "lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"]}}, "tags": []}