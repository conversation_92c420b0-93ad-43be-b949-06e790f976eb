import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { HttpClient } from '@angular/common/http';
import { ConfigService } from './config.service';
import { Jobs } from '@loadmonitor/shared/interfaces/Jobs';
import { ShopMasterData } from '@loadmonitor/shared/interfaces/ShopMasterData';
import { ShopMasterDataPropertyPage } from '../interfaces/ShopMasterDataPropertPage';

@Injectable({
  providedIn: 'root',
})
export class ShopMasterDataService {

  private url!:string;

  constructor(private http: HttpClient, private config: ConfigService) {
    this.url = `${this.config.getApiUrl()}/ShopMasterData`;
  }

  getAsync(
    countryIds?: number[],
    categoryIds?: number[],
    sectorIds?: number[],
    domainProductGroupIds?: number[],
    channelIds?: number[],
    periodIds?: number[],
    periodicityIds?: number[],
    startDate?: Date,
    endDate?: any,
    userNames?: string[],
    statusIds?: number[],
    unitIds?: number[]
  ): Observable<Jobs<ShopMasterData>> {
    const body = {
      countryIds,
      categoryIds,
      sectorIds,
      domainProductGroupIds,
      channelIds,
      periodIds,
      periodicityIds,
      startDate,
      endDate,
      userNames,
      statusIds,
      unitIds
    };
    return this.http.post<Jobs<ShopMasterData>>(this.url, body);
  }

  getDetailAsync(
    unitId?: number
  ): Observable<Jobs<ShopMasterDataPropertyPage>> {

    return this.http.get<Jobs<ShopMasterDataPropertyPage>>(this.url + '/' + unitId);
  }
}
