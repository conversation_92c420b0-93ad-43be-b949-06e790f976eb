import { ComponentFixture, TestBed } from '@angular/core/testing';

import { ChipSelectComponent } from './chip-select.component';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';

describe('ChipSelectComponent', () => {
	let component: ChipSelectComponent;
	let fixture: ComponentFixture<ChipSelectComponent>;

	beforeEach(async () => {
		await TestBed.configureTestingModule({
			imports: [
				NoopAnimationsModule,
				ChipSelectComponent
			],
		}).compileComponents();

		fixture = TestBed.createComponent(ChipSelectComponent);
		component = fixture.componentInstance;
		fixture.detectChanges();
	});

	it('should create', () => {
		expect(component).toBeTruthy();
	});
});
