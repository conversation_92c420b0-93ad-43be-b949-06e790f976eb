import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { HttpClient } from '@angular/common/http';
import { ConfigService } from './config.service';
import { Jobs } from '@loadmonitor/shared/interfaces/Jobs';
import { CsvImports } from '@loadmonitor/shared/interfaces/CsvImports';
import { CsvImportPropertyPage } from '@loadmonitor/shared/interfaces/CsvImportPropertyPage';

@Injectable({
  providedIn: 'root',
})
export class CsvImportService {

  private url!:string;
  constructor(private http: HttpClient, private config: ConfigService) {
    this.url = `${this.config.getApiUrl()}/csvImports`;
  }

  getAsync(
    countryIds?: number[],
    categoryIds?: number[],
    sectorIds?: number[],
    domainProductGroupIds?: number[],
    periodIds?: number[],
    periodicityIds?: number[],
    startDate?: Date,
    endDate?: any,
    userNames?: string[],
    statusIds?: number[],
    unitTypeIds?: number[],
    baseProjectIds?: number[],
    baseProjectName?: string,
    jeejobIds?: number[],
    fileType?: string[]
  ): Observable<Jobs<CsvImports>> {
    const body = {
      countryIds,
      categoryIds,
      sectorIds,
      domainProductGroupIds,
      periodIds,
      periodicityIds,
      startDate,
      endDate,
      userNames,
      statusIds,
      unitTypeIds,
      baseProjectIds,
      baseProjectName,
      jeejobIds,
      fileType
    };
    return this.http.post<Jobs<CsvImports>>(this.url, body);
  }

  getDetailAsync(
    id?: number,
    unitType?: string
  ): Observable<Jobs<CsvImportPropertyPage>> {

    return this.http.get<Jobs<CsvImportPropertyPage>>(this.url + '/' + id + '/' + unitType);
  }
}

