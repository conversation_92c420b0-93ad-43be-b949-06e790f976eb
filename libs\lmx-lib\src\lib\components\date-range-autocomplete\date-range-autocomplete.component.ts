import {
	Component,
	ElementRef,
	EventEmitter,
	forwardRef,
	Input,
	OnChanges,
	OnDestroy,
	OnInit,
	Output,
	SimpleChanges,
	ViewChild,
} from '@angular/core';
import { ControlValueAccessor, FormControl, FormGroup, NG_VALUE_ACCESSOR, ReactiveFormsModule } from '@angular/forms';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { CommonModule } from '@angular/common';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatOptionModule } from '@angular/material/core';
import { MatChipsModule } from '@angular/material/chips';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatSelectModule } from '@angular/material/select';
import { DateAdapter, MAT_DATE_FORMATS, MAT_DATE_LOCALE } from '@angular/material/core';
import { MomentDateAdapter } from '@angular/material-moment-adapter';
import * as moment from 'moment';
import { Subject } from 'rxjs';

const materialComponents = [MatCheckboxModule, MatOptionModule, MatAutocompleteModule, MatChipsModule, MatFormFieldModule, MatIconModule, MatSelectModule, MatDatepickerModule];

export const DATE_RANGE_AUTOCOMPLETE_VALUE_ACCESSOR: any = {
	provide: NG_VALUE_ACCESSOR,
	useExisting: forwardRef(() => DateRangeAutocompleteComponent),
	multi: true,
};

export const MY_FORMATS = {
  parse: {
    dateInput: 'YYYY-MM-DD',
  },
  display: {
    dateInput: 'YYYY-MM-DD',
    monthYearLabel: 'YYYY',
    dateA11yLabel: 'LL',
    monthYearA11yLabel: 'YYYY',
  },
};

@Component({
	selector: 'lmx-date-range-autocomplete',
	templateUrl: './date-range-autocomplete.component.html',
	styleUrls: ['./date-range-autocomplete.component.scss'],
	standalone: true,
	imports: [CommonModule, ReactiveFormsModule, ...materialComponents],
  providers: [
    DATE_RANGE_AUTOCOMPLETE_VALUE_ACCESSOR,
    { provide: DateAdapter, useClass: MomentDateAdapter, deps: [MAT_DATE_LOCALE] },

    { provide: MAT_DATE_FORMATS, useValue: MY_FORMATS },
  ],
})

export class DateRangeAutocompleteComponent implements OnInit, OnChanges, OnDestroy, ControlValueAccessor {

  @Input() selected?: any;
  @Input() startDate?: any;
  @Input() endDate?: any;
  @Input() isInfoIconShow?: boolean =false;
  @Output() selectedDate = new EventEmitter();
  @Input() resetDate!: Subject<boolean>;

  @Input() icon = 'help-outline';
  @ViewChild('dateRangeStart') dateRangeStart!: ElementRef<HTMLInputElement>;

  @ViewChild('dateRangeEnd')
  dateRangeEnd!: ElementRef<HTMLInputElement>;

  dateRange!: FormGroup;

  @Output() valueChanges = new EventEmitter<any[]>();

  private destroy$ = new Subject<void>();

  IsDateRangeDisabled = false;

  // These properties are required to implement ControlValueAccessor
  onChange = () => {
    // This is intentionally left empty
  };
  onTouched = () => {
    // This is intentionally left empty
  };

	ngOnInit(): void {
    setTimeout(()=>{
      const element = document.getElementById('mat-date-range-input-0') as HTMLElement
      element.id = 'startDate-input';
    },100)
    if(this.selected == undefined || this.selected == null){
      this.selected = '1';
    }
    //Removing offset, setting to zulu
    if (this.endDate != null) {
      this.endDate = new Date(this.endDate);
      this.endDate?.setMinutes(this.endDate.getMinutes() + this.endDate.getTimezoneOffset());
    }

    this.dateRange = new FormGroup({
      start: this.startDate ? new FormControl(moment(this.startDate)) : new FormControl(moment(null)),
      end: this.endDate ? new FormControl(moment(this.endDate)) : new FormControl(moment(null)),
    });
    this.changeHistory(this.selected, this.startDate, this.endDate);

    this.dateRange.valueChanges.subscribe((historydates) => {
      this.valueChanges.emit(historydates);
    });
	}

	ngOnDestroy(): void {
		this.destroy$.next();
		this.destroy$.complete();
	}

  ngOnChanges(): void {
    setTimeout(() => {
      if(this.selected == undefined || this.selected == null){
        this.selected = '1';
      }  
      this.dateRange.get('start')?.setValue(this.startDate ? moment(this.startDate) : null);
      this.dateRange.get('end')?.setValue(this.endDate ? moment(this.endDate) : null);
      this.IsDateRangeDisabled = this.selected == 1 ? false : true;
    }, 100)
  }

  writeValue(): void {
    // This is intentionally left empty
  }

  registerOnChange(fn: any) {
    this.onChange = fn;
  }

  registerOnTouched(fn: any) {
    this.onTouched = fn;
  }

  changeHistory(historyId: any, startDate?: any, endDate?: any) {
    // Just for information:
    // we use Dat.UTC here, but the result is not in UTC format if you debug it
    // but in the request we have the UTC time and that's what we want
    this.selected = historyId;
    if (historyId == '1') {
      this.startDate = startDate ? startDate : null;
      this.endDate = endDate ? endDate : null;
      this.setRange(this.startDate, this.endDate);
      this.dateRange.setValue({ start: new FormControl(this.startDate ? moment(this.startDate) : null), end: new FormControl(this.startDate ? moment(this.endDate) : null) });
      this.IsDateRangeDisabled = false;
    }
    if (historyId == '2') {
      const date = new Date();
      this.startDate = new Date(Date.UTC(date.getFullYear(), date.getMonth(), date.getDate(), 0, 0, 0, 0));
      this.endDate = new Date(Date.UTC(date.getFullYear(), date.getMonth(), date.getDate(), 0, 0, 0, 0));
      this.IsDateRangeDisabled = true;
      this.convertEndDatetoZeroTZForPicker();
    }
    if (historyId == '3') {
      const date = new Date();
      this.startDate = new Date(Date.UTC(date.getFullYear(), date.getMonth(), date.getDate() - 2, 0, 0, 0, 0));
      this.endDate = new Date(Date.UTC(date.getFullYear(), date.getMonth(), date.getDate(), 0, 0, 0, 0));
      this.IsDateRangeDisabled = true;
      this.convertEndDatetoZeroTZForPicker();
    }
    this.selectedDate.emit({ selectedDays: this.selected, startDate: this.startDate,  endDate: this.endDate});
  }

  private convertEndDatetoZeroTZForPicker() {
    const tempEndDate = new Date(this.endDate);
    tempEndDate.setMinutes(tempEndDate.getMinutes() + tempEndDate.getTimezoneOffset());
    this.dateRange.setValue({ start: moment(this.startDate), end: moment(tempEndDate) });
  }

  dateChange() {
    this.setRange(
      this.dateRange.value.start ? new Date(this.dateRange.value.start) : undefined,
      this.dateRange.value.end ? new Date(this.dateRange.value.end) : undefined,
    );
    this.selectedDate.emit({ selectedDays: this.selected, startDate: this.startDate,  endDate: this.endDate});
  }

  setRange(start: any, end: any) {
    if (typeof start == 'string') {
      start = new Date(start);
    }

    //Removing offset, setting to zulu
    if (typeof end == 'string') { //exec below only when input coming from cache; cache stores string not date
      end = new Date(end);
      end.setMinutes(end.getMinutes() + end.getTimezoneOffset());
    }
    //
    this.startDate = start != null && start != undefined ? new Date(Date.UTC(start.getFullYear(), start.getMonth(), start.getDate(), 0, 0, 0, 0)) : undefined;
    this.endDate = end != null && end != undefined ? new Date(Date.UTC(end.getFullYear(), end.getMonth(), end.getDate(), 0, 0, 0, 0)) : undefined;
    if(this.startDate && this.endDate){
      this.convertEndDatetoZeroTZForPicker();
    }
  }
}
