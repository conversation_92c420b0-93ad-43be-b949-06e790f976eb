import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { HttpClient } from '@angular/common/http';
import { ConfigService } from './config.service';
import { TagItem } from '@loadmonitor/shared/interfaces/TagItem';
import { shareReplay } from 'rxjs/operators';

@Injectable({
  providedIn: 'root',
})
export class SectorService {
  private url!:string;

  private cachedSector! : Observable<TagItem[]>;
  constructor(private http: HttpClient, private config: ConfigService) {
    this.url = `${this.config.getApiUrl()}/Sectors`;
  }

  getAsync(): Observable<TagItem[]> {
    if(!this.cachedSector)
    {
      this.cachedSector = this.http
        .get<TagItem[]>(this.url)
        .pipe(shareReplay(1));
    }
    return this.cachedSector;
  }
}
