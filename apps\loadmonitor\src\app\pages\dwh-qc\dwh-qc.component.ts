import { TagItem } from '@loadmonitor/shared/interfaces/TagItem';
import { MatSort } from '@angular/material/sort';
import { Component, ElementRef, OnInit, ViewChild, AfterContentInit, Input, OnDestroy, HostListener, ChangeDetectorRef } from '@angular/core';
import { MatTable, MatTableDataSource } from '@angular/material/table';
import { DWHQC } from '@loadmonitor/shared/interfaces/dwh-qc';
import { MatPaginator } from '@angular/material/paginator';
import { RangedWeightingImportFilter } from '@loadmonitor/shared/interfaces/RangedWeightingImportFilter';
import { COMMA, ENTER } from '@angular/cdk/keycodes';
import { FormBuilder, FormControl, FormGroup } from '@angular/forms';
import { BehaviorSubject, Observable, Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { map, startWith } from 'rxjs/operators';
import { MatSnackBar } from '@angular/material/snack-bar';
import { DefaultSnackbarComponent } from '@loadmonitor/shared/components/default-snackbar/default-snackbar.component';
import { DWHQCService } from '@loadmonitor/shared/services/dwh-qc.service';
import { SettingsConstants } from '@loadmonitor/shared/settings.constants';
import { DWHQcCache } from '@loadmonitor/shared/interfaces/caching/DWHQcCache';
import { Filter, FilterRecords, FilterService } from '@dwh/lmx-lib/src/lib/services/filter.service';
import { environment } from 'apps/loadmonitor/src/environments/environment';
import { DatePipe } from '@angular/common';
import { ToggleButtonComponent } from '@gfk/ng-lib';
import { FilterTrayComponent } from '@dwh/lmx-lib/src';
import * as moment from 'moment';
import { FilterTrayCacheService } from '@loadmonitor/shared/services/filter-tray-cache.service';

@Component({
	selector: 'lm-dwh-qc',
	templateUrl: './dwh-qc.component.html',
	styleUrls: ['./dwh-qc.component.scss'],
})
export class DwhQcComponent implements OnInit, AfterContentInit, OnDestroy {


	/* *********
	/* *********
	* ViewChild
	********* */
	eventsSubject: Subject<void> = new Subject<void>();
	@Input() icon = 'delete';
	@ViewChild(MatSort)
	sort: MatSort = new MatSort();

	@ViewChild(MatTable)
	table!: MatTable<DWHQC>;

	@ViewChild(MatPaginator)
	paginator!: MatPaginator;

	@ViewChild('statusIdsInput')
	statusIdsInput!: ElementRef<HTMLInputElement>;


	@ViewChild(FilterTrayComponent) filterTrayComponent: FilterTrayComponent | undefined;

	@ViewChild(ToggleButtonComponent) togglebuttonComponent: ToggleButtonComponent | undefined;


	/* *********************
	 * Components variables
	 ********************* */
	baseProjectLabel = 'Baseproject Type';
	baseProjectTypeId!: number;
	filterCount = '';
	IsEnabled = false;
	IsDisabled = false;
	FilterTrayOpenFlag = true;
	environments = environment.environments;
	cacheInterval!: any;
	toggleInterval!: any;
	timer = 60;
	// Progress Spinner visibility
	isLoading = false;
	public DwhQcForm!: FormGroup;
	public cacheObj: DWHQcCache = {} as DWHQcCache;
	cacheName = 'cache::DWHQC';
	defaultfilter = JSON.parse(localStorage.getItem(this.cacheName + '-DefaultFilter') || '[]');
	defaultoption = localStorage.getItem(this.cacheName + '-LastFilter') || 'Default';
	EnableRefresh = true;
	modalshow = false;
	currentfilter = JSON.parse(localStorage.getItem('QuickFilter') || '[]').filter(
		(x) => x.user == localStorage.getItem('username') && x.slice == window.location.href.split('/')[3]
	);
	/* *********************
	 * Components parameters
	 ********************* */

	//Mat-Table
	dataSource = new MatTableDataSource<DWHQC>();
	filter: RangedWeightingImportFilter | undefined;
	displayedColumns: string[] = [
		'baseProjectId',
		'qcProjectId',
		'status',
		'baseProjectName',
		'baseProjectType',
		'periodDescription',
		'createdBy',
		'created',
		'changedBy',
		'changed',
	];

	//Mat-Expansion-Panel
	panelOpenState = false;

	//Tag-Chip-List
	readonly separatorKeysCodes = [ENTER, COMMA] as const;

	// Mat-Chips
	statusIdsCtrl = new FormControl();
	filteredStatusIds: Observable<TagItem[]>;
	allStatusIds: TagItem[] = [];

	// Mat-Chips - BaseProject Type

	readonly date_with_time = SettingsConstants.DATE_FORMAT_WITH_TIME;

	refreshedDateTime!: boolean;

	selectedFilters: any;
	cacheValue: BehaviorSubject<any> = new BehaviorSubject<any>({});
	resetForm: Subject<boolean> = new Subject();
	resetPeriodicityFilter: Subject<boolean> = new Subject();
	isPeriodFilterHidden = false;
	dwhQCData!: any[];

	@HostListener('document:visibilitychange', ['$event'])
	appVisibility() {
		if (document.hidden) {
			if (this.cacheInterval) {
				clearInterval(this.cacheInterval);
			}
			if (this.toggleInterval) {
				clearInterval(this.toggleInterval);
			}
		} else {
			if (this.cacheObj.isAutoRefresh === true) {
				this.cacheInterval = setInterval(() => {
					if (this.defaultoption != 'Default') {
						this.apply();
					}
				}, this.timer * 1000);
			} else {
				if (this.cacheInterval !== undefined) clearInterval(this.cacheInterval);
				if (this.toggleInterval !== undefined) clearInterval(this.toggleInterval);
			}
		}
	}
	private destroy$ = new Subject<void>();

	/* **************
	 * Public Methods
	 ************** */
	constructor(
		private dwhqcService: DWHQCService,
		private snackBar: MatSnackBar,
		private formBuilder: FormBuilder,
		public filterService: FilterService,
		public datepipe: DatePipe,
		private cdRef: ChangeDetectorRef,
		private filterTrayCacheService: FilterTrayCacheService
	) {
		this.filteredStatusIds = this.statusIdsCtrl.valueChanges.pipe(
			startWith(null),
			map((item: TagItem | null) => (item ? this.tagItemsFilter(item.name) : this.allStatusIds.slice()))
		);
	}

	private updateFC(): void {
		if(this.cacheObj){
			this.filterTrayCacheService.getCacheValueData(this.cacheObj);
		}
  	}

	ngOnInit(): void {
		if (this.environments.filter((x) => x.location == window.location.origin)[0]) {
			this.IsEnabled = this.environments.filter((x) => x.location == window.location.origin)[0].enabled;
		} else {
			this.IsEnabled = false;
		}

		this.DwhQcForm = this.formBuilder.group({
			isAutoRefresh: this.togglebuttonComponent?.checked,
		});
		this.updateFC();


	}

	ngOnDestroy() {
		if (this.cacheInterval) {
			clearInterval(this.cacheInterval);
		}

		if (this.toggleInterval) {
			clearInterval(this.toggleInterval);
		}
		this.destroy$.next();
	    this.destroy$.complete();
	}

	ngAfterContentInit() {
		this.afterChildComponentsLoaded();
		this.CreateFilterSummaryChips();
		this.SetFiltersCount();
		this.updateFC();
		if (this.cacheObj.isAutoRefresh === true) {
			if (this.cacheInterval === undefined) {
				this.cacheInterval = setInterval(() => {
					if (this.defaultoption != 'Default') {
						this.apply();
					}
				}, this.timer * 1000);
			}
		} else {
			if (this.cacheInterval !== undefined) clearInterval(this.cacheInterval);
			if (this.toggleInterval !== undefined) clearInterval(this.toggleInterval);
		}
	}

	ngAfterViewInit() {
		this.cdRef.detectChanges();
	}

	afterChildComponentsLoaded() {
		this.cacheObj = JSON.parse(localStorage.getItem(this.cacheName) || '{}');
	}

	public isShownFilterTrayEventHandler($event: boolean) {
		this.filterTrayComponent?.toggleShowTray($event);
		this.FilterTrayOpenFlag = $event;
	}

	getWidth() {
		this.FilterTrayOpenFlag = !this.FilterTrayOpenFlag;
		return '100%';
	}

	toggleChecked(check: any) {
		if (check === true) {
			this.saveInCache(check, true);
			this.toggleInterval = setInterval(() => {
				if (this.defaultoption != 'Default') {
					this.apply();
				}
			}, this.timer * 1000);
		} else {
			if (this.toggleInterval !== undefined) clearInterval(this.toggleInterval);
			if (this.cacheInterval !== undefined) clearInterval(this.cacheInterval);
			this.saveInCache(check, true);
		}
	}

	applyfilter(selectedvalue) {
		this.resetForm.next(true);
		const oldautotoggle = this.cacheObj.isAutoRefresh;
		selectedvalue = selectedvalue.value ? selectedvalue.value : selectedvalue;
		this.defaultoption = selectedvalue;
		localStorage.setItem(this.cacheName + '-LastFilter', this.defaultoption || 'Default');
		if (selectedvalue == 'Default') {
			if (
				JSON.parse(localStorage.getItem(this.cacheName + '-DefaultFilter') || '[]').filter(
					(x) => x.user == localStorage.getItem('username') && x.slice == window.location.href.split('/')[3]
				)[0]
			) {
				this.cacheObj = JSON.parse(localStorage.getItem(this.cacheName + '-DefaultFilter') || '[]').filter(
					(x) => x.user == localStorage.getItem('username') && x.slice == window.location.href.split('/')[3]
				)[0]['data'];
			}

			localStorage.setItem(this.cacheName, JSON.stringify(this.cacheObj));
		} else {
			this.cacheObj = JSON.parse(localStorage.getItem('QuickFilter') || '[]')
				.filter((x) => x.user == localStorage.getItem('username') && x.slice == window.location.href.split('/')[3])
				.filter((x) => x.filter == selectedvalue)[0]['data'];
			localStorage.setItem(this.cacheName, JSON.stringify(this.cacheObj));
		}
		this.cacheObj.isAutoRefresh = oldautotoggle;
		this.CreateFilterSummaryChips();
		this.selectedFilters = this.cacheObj;
    if (this.cacheObj.selectedDays == '3') {
			const date = new Date();
			this.cacheObj.startDate = new Date(Date.UTC(date.getFullYear(), date.getMonth(), date.getDate() - 2, 0, 0, 0, 0));
			this.cacheObj.endDate = new Date(Date.UTC(date.getFullYear(), date.getMonth(), date.getDate(), 0, 0, 0, 0));
		} else if (this.cacheObj.selectedDays == '2') {
			const date = new Date();
			this.cacheObj.startDate = new Date(Date.UTC(date.getFullYear(), date.getMonth(), date.getDate(), 0, 0, 0, 0));
			this.cacheObj.endDate = new Date(Date.UTC(date.getFullYear(), date.getMonth(), date.getDate(), 0, 0, 0, 0));
		}
		setTimeout(() => {
			this.updateFC();
			this.apply();
		}, 200);
		localStorage.setItem(this.cacheName, JSON.stringify(this.cacheObj));
	}

	openmodal() {
		this.eventsSubject.next();
	}

	disablesave(status) {
		this.IsDisabled = status;
	}

	changeBaseProjectType(id: any) {
		this.baseProjectTypeId = id;
	}

	ConfirmApply(autoRefreshStatus) {
		this.saveInCache(autoRefreshStatus, false);
		this.CreateFilterSummaryChips();

		let count;
		this.filterService.filtersSelected$.subscribe((selectedFilters: FilterRecords) => {
			count = Number(Object.keys(selectedFilters).length);
		});

		if (count > 0) {
			this.apply();
		} else {
			if (confirm('Loading results without any filters selected will take some time to load. Are you sure you want to proceed?')) {
				this.apply();
			}
		}
	}

	apply(): void {
		this.refreshedDateTime = false;
		this.isLoading = true;
		this.destroy$.next();
		this.filterTrayComponent?.showSpinner(true);
		this.dwhqcService
			.getAsync(
				this.selectedFilters.users?.map((users) => users.userName),
				this.selectedFilters.qcProjectIds,
				this.selectedFilters.baseProjectIds,
				this.selectedFilters.countries?.map((country) => country),
				this.selectedFilters.domainProductGroup?.map((domainProductGroup) => domainProductGroup.id),
				this.selectedFilters.category?.map((category) => category.id),
				this.selectedFilters.sector?.map((sector) => sector),
				this.selectedFilters.periodIds?.map((period) => period),
				this.selectedFilters.periodicityIds ? [this.selectedFilters.periodicityIds] : [],
				this.selectedFilters.baseProjectName,
				this.selectedFilters.baseProjectType?.map((base) => base),
				this.selectedFilters.qcProjectflag ? (this.selectedFilters.qcProjectflag == '1' ? true : false) : null,
				this.selectedFilters.startDate ? this.selectedFilters.startDate : null,
				this.selectedFilters.endDate ? moment(this.selectedFilters.endDate).format().split('T')[0] + 'T23:59:59.999Z' : null
			)
			.pipe(
				takeUntil(this.destroy$)
			)
			.subscribe((result) => {
				this.refreshedDateTime = true;
				if (result.moreRecordsAvailable) {
					this.notify('More than 1000 results exist!');
				}

				if (result.count === 0) {
					this.notify('No result found!');
					this.dwhQCData = [];
				}
				this.filterTrayComponent?.showSpinner(false);
				this.isLoading = false;
				this.dwhQCData = result.records;
				result.records.forEach((element: any) => {
					element.detailMenuConfig = [
						{
							menuText: 'Fact Tool',
							menuIcon: 'open_in_new',
							service: 'openfacttool',
							params: { loaddefinitionid: element.loadDefinitionId },
						},
						{
							menuText: 'Properties',
							menuIcon: 'open_in_new',
							service: 'showldproperties',
							params: { loaddefinitionid: element.loadDefinitionId },
						},
						{
							menuText: 'Messages',
							menuIcon: 'open_in_new',
							isMessageLink: true,
							params: { loaddefinitionid: element.loadDefinitionId },
						},
					];
				});

				this.dataSource = new MatTableDataSource<DWHQC>(result.records);
				this.dataSource.sort = this.sort;
				this.paginator.pageIndex = 0;
				this.dataSource.paginator = this.paginator;
				this.table.dataSource = this.dataSource;
			},
			(error) => {
				if (error.status === 504) {
					this.notify('Gateway Timeout. The server took too long to respond. Please try again later.');
				} 
				else {
					this.notify(`An error occurred: ${error.message}`);
				}
				this.isLoading = false;
				this.filterTrayComponent?.showSpinner(false);
			});
		this.SetFiltersCount();
	}
	private tagItemsFilter(value: string): TagItem[] {
		const filterValue = value.toLowerCase();
		return this.allStatusIds.filter((item) => item.name.toLowerCase().indexOf(filterValue) === 0);
	}

	private notify(message: string) {
		this.snackBar.openFromComponent(DefaultSnackbarComponent, {
			duration: 10 * 1000,
			data: message,
		});
	}

	saveInCache(autoRefreshStatus, AutoToggle) {
		this.setFormValue(autoRefreshStatus);
		localStorage.setItem(this.cacheName, JSON.stringify(this.selectedFilters));
		this.cacheObj = JSON.parse(localStorage.getItem(this.cacheName) || '{}');
		if(this.cacheObj.baseProjectName == null){
			this.cacheObj.baseProjectName = ''
		}
		if(this.cacheObj.periodIds && this.cacheObj.periodIds.length == 0){
			this.cacheObj.periodIds = null
		}
		if (!AutoToggle) {
			this.defaultfilter = [];
			this.defaultfilter.push({
				data: this.selectedFilters,
				slice: window.location.href.split('/')[3],
				user: localStorage.getItem('username'),
			});
			localStorage.setItem(this.cacheName + '-DefaultFilter', JSON.stringify(this.defaultfilter));
			let nofilter = 0;
			let selectedfilter = '';
			this.currentfilter.forEach((i) => {
				// if (i.filter == this.defaultoption) {
				if(JSON.stringify(i.data) == JSON.stringify(this.cacheObj)){
					nofilter++;
					selectedfilter = i.filter;
				}
			});
			if (nofilter == 0) {
				this.defaultoption = 'Default';
			} else {
				this.defaultoption = selectedfilter;
			}
		}
		localStorage.setItem(this.cacheName + '-LastFilter', this.defaultoption);
		this.updateFC();
	}

	SetFiltersCount() {
		let count;
		this.filterService.filtersSelected$.subscribe((selectedFilters: FilterRecords) => {
			count = Number(Object.keys(selectedFilters).length);
		});
		if (count > 0) {
			this.filterCount = '(' + count.toString() + ')';
		} else {
			this.filterCount = '';
		}
	}

	setFormValue(autoRefreshStatus) {
		this.DwhQcForm?.patchValue({
		 	isAutoRefresh: autoRefreshStatus === undefined || autoRefreshStatus === false ? false : true,
		});
	}

	saveInQuickFilter(autoRefreshStatus, filterName) {
		this.setFormValue(autoRefreshStatus);
		this.currentfilter = JSON.parse(localStorage.getItem('QuickFilter') || '[]');
		this.currentfilter.push({
			data: this.selectedFilters,
			filter: filterName,
			slice: window.location.href.split('/')[3],
			user: localStorage.getItem('username'),
		});
		this.defaultoption = filterName;
		const oldcache = this.cacheObj;
		const olddefault = localStorage.getItem(this.cacheName + '-DefaultFilter');
		this.ConfirmApply(autoRefreshStatus);
		this.cacheObj = oldcache;
		localStorage.setItem(this.cacheName + '-DefaultFilter', olddefault || '[]');
		localStorage.setItem('QuickFilter', JSON.stringify(this.currentfilter));
		localStorage.setItem(this.cacheName + '-LastFilter', filterName);
		this.SetFiltersCount();
	}

	datesFilterSummary(dateStart: any, dateEnd: any) {
		if (dateStart != null || dateEnd != null) {
			const start = dateStart == 'Invalid Date' || dateStart == null ? '' : this.datepipe.transform(dateStart, 'yyyy-MM-dd');
			const end = dateEnd == 'Invalid Date' || dateEnd == null ? '' : this.datepipe.transform(dateEnd, 'yyyy-MM-dd');
			this.filterService.setFilters({
				dates: {
					name: 'Date',
					values: (start !== undefined && end !== undefined) ? [start, end] : null,
					slice: window.location.href.split('/')[3],
				} as Filter,
			});
		} else {
			this.filterService.setFilters({
				dates: {
					name: 'Date',
					values: null,
					slice: window.location.href.split('/')[3],
				} as unknown as Filter,
			});
		}
	}

	countryFilterSummary() {
		this.filterService.setFilters({
			countryIds: {
				name: 'Country',
				values: this.cacheObj.countries,
				slice: window.location.href.split('/')[3],
			} as Filter,
		});
	}

	periodicityFilterSummary() {
		this.filterService.setFilters({
			periodicityIds: {
				name: 'Periodicity',
				values: this.cacheObj.periodicityIds? [{ value: this.cacheObj.periodicityIds, label: this.cacheObj.periodicityIds }] : null,
				slice: window.location.href.split('/')[3],
			} as Filter,
		});
	}

	periodFilterSummary() {
		this.filterService.setFilters({
			periodIds: {
				name: 'Period',
				values: this.cacheObj.periodIds,
				slice: window.location.href.split('/')[3],
			} as Filter,
		});
	}

	usersFilterSummary() {
		this.filterService.setFilters({
			userIds: {
				name: 'User',
				values: this.cacheObj.users?.map((i) => i.name),
				slice: window.location.href.split('/')[3],
			} as Filter,
		});
	}

	sectorFilterSummary() {
		this.filterService.setFilters({
			sectorids: {
				name: 'Sector',
				values: this.cacheObj.sector,
				slice: window.location.href.split('/')[3],
			} as Filter,
		});
	}

	categoryFilterSummary() {
		this.filterService.setFilters({
			categoryIds: {
				name: 'Category',
				values: this.cacheObj.category,
				slice: window.location.href.split('/')[3],
			} as Filter,
		});
	}

	qcProjectsIdsFilterSummary() {
		this.filterService.setFilters({
			qcProjectsIds: {
				name: 'QC Projects IDs',
				values: this.cacheObj.qcProjectIds,
				slice: window.location.href.split('/')[3],
			} as Filter,
		});
	}

	baseProjectsIdsFilterSummary() {
		this.filterService.setFilters({
			basePRojectsIds: {
				name: 'Base Projects IDs',
				values: this.cacheObj.baseProjectIds,
				slice: window.location.href.split('/')[3],
			} as Filter,
		});
	}

	domainFilterSummary() {
		this.filterService.setFilters({
			domainProductGroupIds: {
				name: 'Domain',
				values: this.cacheObj.domainProductGroup,
				slice: window.location.href.split('/')[3],
			} as Filter,
		});
	}

	baseProjectTypeFilterSummary() {
		this.filterService.setFilters({
			baseProjectTypes: {
				name: 'Base Project Types',
				values: this.cacheObj.baseProjectType,
				slice: window.location.href.split('/')[3],
			} as Filter,
		});
	}

	baseProjectNameFilterSummary() {
		this.filterService.setFilters({
			baseProjectName: {
				name: 'Base Project Name',
				values: this.cacheObj.baseProjectName ? [{ value: this.cacheObj.baseProjectName, label: this.cacheObj.baseProjectName }] : '',
				slice: window.location.href.split('/')[3],
			} as unknown as Filter,
		});
	}

	QCProjectTyeFlagFilterSummary() {
		this.filterService.setFilters({
			qcProjectTypeFlag: {
				name: 'QC Project Type Flag',
				values: this.cacheObj.qcProjectflag != null ? [{ value: this.cacheObj.qcProjectflag, label: this.cacheObj.qcProjectflag }] : null,
				slice: window.location.href.split('/')[3],
			} as Filter,
		});
	}

	CreateFilterSummaryChips() {
		if (this.cacheObj && Object.keys(this.cacheObj).length) {
			this.usersFilterSummary();
			this.qcProjectsIdsFilterSummary();
			this.baseProjectsIdsFilterSummary();
			this.countryFilterSummary();
			this.sectorFilterSummary();
			this.categoryFilterSummary();
			this.domainFilterSummary();
			this.periodicityFilterSummary();
			this.periodFilterSummary();
			this.baseProjectTypeFilterSummary();
			this.QCProjectTyeFlagFilterSummary();
			this.baseProjectNameFilterSummary();
			this.datesFilterSummary(this.cacheObj.startDate, this.cacheObj.endDate);
		}
	}

	filterSummaryChipClear(filteredItems) {
		if (filteredItems == 'ALL') {
			if (confirm('This will remove all of your selected filters and could not be reversed. Are you sure you want to do that?')) {
				this.resetForm.next(true);
				this.filterTrayCacheService.getCacheNameData(this.cacheName);
				this.resetPeriodicityFilter.next(true);
				this.filterCount = '';
			}
		}
		this.saveInCache(this.cacheObj.isAutoRefresh, false);
		this.CreateFilterSummaryChips();
		this.SetFiltersCount();
	}

	getSelectedFilters(selectedFilters: any) {
		this.cacheObj = selectedFilters;
		this.selectedFilters = selectedFilters;
	}
}
