import { Component } from '@angular/core';
import { ConfigService } from '@loadmonitor/shared/services/config.service';

@Component({
  selector: 'lm-version',
  templateUrl: './version.component.html',
  styleUrls: ['./version.component.scss']
})
export class VersionComponent {
  
	version: string;

	constructor(private configService: ConfigService) {
		const packageVersion = this.configService.getVersion();
		this.version = packageVersion.substring(packageVersion.indexOf('_') + 1);
	}

}
