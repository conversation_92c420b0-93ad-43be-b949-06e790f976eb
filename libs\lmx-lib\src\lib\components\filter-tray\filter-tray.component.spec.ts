import { ComponentFixture, TestBed } from '@angular/core/testing';

import { FilterTrayComponent } from './filter-tray.component';

describe('FilterTrayComponent', () => {
	let component: FilterTrayComponent;
	let fixture: ComponentFixture<FilterTrayComponent>;

	beforeEach(async () => {
		await TestBed.configureTestingModule({
			declarations: [FilterTrayComponent],
		}).compileComponents();
	});

	beforeEach(() => {
		fixture = TestBed.createComponent(FilterTrayComponent);
		component = fixture.componentInstance;
		fixture.detectChanges();
	});

	it('should create', () => {
		expect(component).toBeTruthy();
	});
});
