import { HttpClientModule } from '@angular/common/http';
import { CUSTOM_ELEMENTS_SCHEMA, Error<PERSON><PERSON><PERSON>, NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatBottomSheetModule } from '@angular/material/bottom-sheet';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatToolbarModule } from '@angular/material/toolbar';
import { BrowserModule } from '@angular/platform-browser';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { NgLibModule } from '@gfk/ng-lib';
import { AppRoutingModule } from '@loadmonitor/app-routing.module';
import { AppComponent } from '@loadmonitor/app.component';
import { CoreModule } from '@loadmonitor/core/core.module';
import { SharedModule } from '@loadmonitor/shared/shared.module';
import { HomePageComponent } from './pages/home-page/home-page.component';

// Import MSAL and MSAL browser libraries.
import { MatExpansionModule } from '@angular/material/expansion';
import { LmxLibModule } from '@dwh/lmx-lib';
import { RedirectWebComponent } from '@dwh/lmx-lib/src/lib/components/redirect-web/redirect-web.component';
import { initApm } from '@gfk/ng-lib';
import { AuthFailedComponent } from './pages/auth-failed/auth-failed.component';
import { LoggedOutComponent } from './pages/logged-out/logged-out.component';
import { CardComponent } from './shared/card/card.component';

@NgModule({
	declarations: [AppComponent, HomePageComponent, AuthFailedComponent, LoggedOutComponent, CardComponent],
	imports: [
		NgLibModule,
		BrowserModule,
		AppRoutingModule,
		CoreModule,
		BrowserAnimationsModule,
		HttpClientModule,
		SharedModule,
		FormsModule,
		ReactiveFormsModule,
		MatToolbarModule,
		MatIconModule,
		MatButtonModule,
		MatCardModule,
		MatFormFieldModule,
		MatBottomSheetModule,
		MatExpansionModule,
		RedirectWebComponent,
		LmxLibModule.forRoot({ API_ROUTES: { BFF: '/api' } }),
	],
	schemas: [
		CUSTOM_ELEMENTS_SCHEMA
	],
	providers: [
		// ApmService,
		// {
		// 	provide: ErrorHandler,
		// 	useClass: ApmErrorHandler,
		// },
		// {
		// 	provide: HTTP_INTERCEPTORS,
		// 	useClass: GlobalHttpInterceptorService,
		// 	multi: true,
		// }
	],
	bootstrap: [AppComponent],
})
export class AppModule {}
