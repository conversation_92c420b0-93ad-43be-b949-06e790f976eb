import { Component, OnInit, ViewChild, AfterContentInit, Input, HostListener, OnDestroy } from '@angular/core';
import { MatSort } from '@angular/material/sort';
import { MatTable, MatTableDataSource } from '@angular/material/table';
import { MatPaginator } from '@angular/material/paginator';
import { FormBuilder, FormControl, FormGroup } from '@angular/forms';
import { BehaviorSubject, Observable, Subject, Subscription } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { map, startWith } from 'rxjs/operators';
import { TagItem } from '@loadmonitor/shared/interfaces/TagItem';
import { SettingsConstants } from '@loadmonitor/shared/settings.constants';
import { JobTypes } from '@loadmonitor/shared/jobTypes';
import { CsvImportService } from '@loadmonitor/shared/services/csvImport.service';
import { CsvImports } from '@loadmonitor/shared/interfaces/CsvImports';
import { DefaultSnackbarComponent } from '@loadmonitor/shared/components/default-snackbar/default-snackbar.component';
import { MatSnackBar } from '@angular/material/snack-bar';
import { HelperService } from '@loadmonitor/shared/services/helper.service';
import { PropertyPage } from '@loadmonitor/shared/interfaces/PropertyPage';
import { DetailMenuConfiguration } from '../../shared/interfaces/DetailMenuConfiguration';
import { animate, state, style, transition, trigger } from '@angular/animations';
import { CSVImportCache } from '@loadmonitor/shared/interfaces/caching/CSVImportCache';
import { CreateTicketDialogComponent } from '@loadmonitor/shared/components/create-ticket-dialog/create-ticket-dialog.component';
import { MatDialog } from '@angular/material/dialog';
import { SelectionModel } from '@angular/cdk/collections';
import { Filter, FilterRecords, FilterService, FilterTrayComponent } from '@dwh/lmx-lib/src';
import { ToggleButtonComponent } from '@gfk/ng-lib';
import { environment } from 'apps/loadmonitor/src/environments/environment';
import { DatePipe } from '@angular/common';
import * as moment from 'moment';
import { FilterTrayCacheService } from '@loadmonitor/shared/services/filter-tray-cache.service';

@Component({
	selector: 'lm-csv-imports',
	templateUrl: './csv-imports.component.html',
	styleUrls: ['./csv-imports.component.scss'],
	animations: [
		trigger('detailExpand', [
			state('collapsed', style({ height: '0px', minHeight: '0' })),
			state('expanded', style({ height: '*' })),
			transition('expanded <=> collapsed', animate('225ms cubic-bezier(0.4, 0.0, 0.2, 1)')),
		]),
	],
})
export class CsvImportsComponent implements OnInit, AfterContentInit, OnDestroy {

	/* *********
	 * ViewChild
	 ********* */
	@Input() icon = 'delete';
	@ViewChild(MatSort)
	sort: MatSort = new MatSort();

	@ViewChild(MatTable)
	table!: MatTable<CsvImports>;

	@ViewChild(MatPaginator)
	paginator!: MatPaginator;


	@ViewChild(FilterTrayComponent) filterTrayComponent: FilterTrayComponent | undefined;
	@ViewChild(ToggleButtonComponent) togglebuttonComponent: ToggleButtonComponent | undefined;

	/* *********************
	 * Components variables
	 ********************* */
	jobType: JobTypes = JobTypes.CsvImport;
	isPeriodFilterHidden = false;
	detailMenuConfiguration!: DetailMenuConfiguration;
	selectedRow: any;

	// Progress Spinner visibility
	isLoading = false;
	public CsvImportForm!: FormGroup;
	public cacheObj: CSVImportCache = {} as CSVImportCache;

  selectedFilters: any;
	cacheValue: BehaviorSubject<any> = new BehaviorSubject<any>({});
	resetForm: Subject<boolean> = new Subject();
  	resetPeriodicityFilter: Subject<boolean> = new Subject();

	cacheName = 'cache::CSVImport';
	/* *********************
	 * Components parameters
	 ********************* */

	//Mat-Table
	dataSource = new MatTableDataSource<CsvImports>();
	selection = new SelectionModel<CsvImports>(true, []);
	IsSelectAll = false;
	displayedColumns: string[] = [
		'details',
		'menu',
		'id',
		'unitType',
		'status',
		'fileName',
		'fileType',
		'project',
		'productGroup',
		'productGroupId',
		'period',
		'createdBy',
		'created',
		'started',
		'finished',
		'runtime',
		'jeeJobId',
		'jeeJobInfo',
		'message',
	];

	//Mat-Expansion-Panel
	panelOpenState = false;

	readonly date_with_time = SettingsConstants.DATE_FORMAT_WITH_TIME;
	readonly only_time = SettingsConstants.FORMAT_ONLY_TIME;

	// Mat-Chips
	statusIdsCtrl = new FormControl();
	filteredStatusIds: Observable<TagItem[]>;
	allStatusIds: TagItem[] = [];

	// Property Pages
	expandedElement!: PropertyPage | null;
	csvImportDetail!: PropertyPage[];

	/* **************
	 * Public Methods
	 ************** */

	/* *********************
	 * Components variables
	 ********************* */
	eventsSubject: Subject<void> = new Subject<void>();
	IsDisabled = false;
	environments = environment.environments;
	IsEnabled = false;
	FilterTrayOpenFlag = true;
	modalshow = false;
	filterCount = '';
	toggleInterval!: any;
	timer = 60;
	EnableRefresh = true;
	cacheInterval!: any;
	defaultoption = localStorage.getItem(this.cacheName + '-LastFilter') || 'Default';
	defaultfilter = JSON.parse(localStorage.getItem(this.cacheName + '-DefaultFilter') || '[]');
	currentfilter = JSON.parse(localStorage.getItem('QuickFilter') || '[]').filter(
		(x) => x.user == localStorage.getItem('username') && x.slice == window.location.href.split('/')[3]
	);
	private subscription!: Subscription | undefined;
	refreshedDateTime!: boolean;
	fileTypeList: any;
	csvImportsData!: any[];


  @HostListener('document:visibilitychange', ['$event'])
	appVisibility() {
		if (document.hidden) {
			if (this.cacheInterval) {
				clearInterval(this.cacheInterval);
			}
			if (this.toggleInterval) {
				clearInterval(this.toggleInterval);
			}
		} else {
			if (this.cacheObj.isAutoRefresh === true) {
				this.cacheInterval = setInterval(() => {
					if (this.defaultoption != 'Default') {
						this.refresh();
					}
				}, this.timer * 1000);
			} else {
				if (this.cacheInterval !== undefined) clearInterval(this.cacheInterval);
				if (this.toggleInterval !== undefined) clearInterval(this.toggleInterval);
			}
		}
	}
	private destroy$ = new Subject<void>();

	constructor(
		private csvImportService: CsvImportService,
		private formBuilder: FormBuilder,
		private snackBar: MatSnackBar,
		public dialogbox: MatDialog,
		private helperService: HelperService,
		public filterService: FilterService,
		public datepipe: DatePipe,
		private filterTrayCacheService: FilterTrayCacheService
	) {
		this.filteredStatusIds = this.statusIdsCtrl.valueChanges.pipe(
			startWith(null),
			map((item: TagItem | null) => (item ? this.tagItemsFilter(item.name) : this.allStatusIds.slice()))
		);
	}

	private updateFC(): void {
		if(this.cacheObj){
			this.filterTrayCacheService.getCacheValueData(this.cacheObj);
		}
	}

	ngOnInit(): void {
		if (this.environments.filter((x) => x.location == window.location.origin)[0]) {
			this.IsEnabled = this.environments.filter((x) => x.location == window.location.origin)[0].enabled;
		} else {
			this.IsEnabled = false;
		}
		this.CsvImportForm = this.formBuilder.group({
			isAutoRefresh: this.togglebuttonComponent?.checked,
		});
		this.updateFC();
	}

	ngAfterContentInit() {
		this.afterChildComponentsLoaded();
		this.CreateFilterSummaryChips();
		this.SetFiltersCount();
		if (this.cacheObj.isAutoRefresh === true) {
			if (this.cacheInterval === undefined) {
				this.cacheInterval = setInterval(() => {
					if (this.defaultoption != 'Default') {
						this.refresh();
					}
				}, this.timer * 1000);
			}
		} else {
			if (this.cacheInterval !== undefined) clearInterval(this.cacheInterval);
			if (this.toggleInterval !== undefined) clearInterval(this.toggleInterval);
		}
	}

	afterChildComponentsLoaded() {
		this.cacheObj = JSON.parse(localStorage.getItem(this.cacheName) || '{}');
		this.updateFC();
	}

	saveInCache(autoRefreshStatus, AutoToggle) {
		this.setFormValue(autoRefreshStatus);
		localStorage.setItem(this.cacheName, JSON.stringify(this.selectedFilters));

		this.cacheObj = JSON.parse(localStorage.getItem(this.cacheName) || '{}');
		if(this.cacheObj.baseProjectName == null){
			this.cacheObj.baseProjectName = '';
		}
		if (!AutoToggle) {
			this.defaultfilter = [];
			this.defaultfilter.push({
				data: this.selectedFilters,
				slice: window.location.href.split('/')[3],
				user: localStorage.getItem('username'),
			});
			localStorage.setItem(this.cacheName + '-DefaultFilter', JSON.stringify(this.defaultfilter));
			let nofilter = 0;
			let selectedfilter = '';
			this.currentfilter.forEach((i) => {
				if(JSON.stringify(i.data) == JSON.stringify(this.cacheObj)){
					nofilter++;
					selectedfilter = i.filter;
				}
			});
			if (nofilter == 0) {
				this.defaultoption = 'Default';
			} else {
				this.defaultoption = selectedfilter;
			}

			localStorage.setItem(this.cacheName + '-LastFilter', this.defaultoption);
		}
		this.updateFC();
	}

	getFileTypeList(fileTypeData: any){
		this.fileTypeList = fileTypeData;
	}

	refresh(): void {
		this.refreshedDateTime = false;
		this.filterTrayComponent?.showSpinner(true);
		this.isLoading = true;
		this.destroy$.next();
		const selectedFileTypes: any = [];
		this.fileTypeList.forEach((fileType: any) => {
			this.selectedFilters.fileType.forEach((item: any) => {
				if(fileType.value == item){
					selectedFileTypes.push(fileType.label.toLowerCase());
				}
			})
		});
		this.csvImportService
			.getAsync(
				this.selectedFilters.countries?.map((country) => country),
				this.selectedFilters.category?.map((category) => category.id),
				this.selectedFilters.sector?.map((sector) => sector),
				this.selectedFilters.domainProductGroup?.map((domainProductGroup) => domainProductGroup.id),
				this.selectedFilters.periodIds?.map((period) => period),
				this.selectedFilters.periodicityIds ? [this.selectedFilters.periodicityIds] : [],
				this.selectedFilters.startDate ? this.selectedFilters.startDate : null,
				this.selectedFilters.endDate ? moment(this.selectedFilters.endDate).format().split('T')[0] + 'T23:59:59.999Z' : null,
        		this.selectedFilters.users?.map((users) => users.userName),
				this.selectedFilters.status?.map((status) => status),
				this.selectedFilters.unitType?.map((unit) => unit),
				this.selectedFilters.baseProjectIds,
				this.selectedFilters.baseProjectName,
				this.selectedFilters.jobIds,
				selectedFileTypes
         	)
			.pipe(
				takeUntil(this.destroy$)
			)
			.subscribe((result) => {
				this.refreshedDateTime = true;
				if (result.moreRecordsAvailable) {
					this.notify('More than 1000 results exist!');
				}

				if (result.count === 0) {
					this.notify('No result found!');
					this.csvImportsData = [];
				}
				this.isLoading = false;
				this.csvImportsData = result.records;
				// set runtime column
				result.records.forEach((element) => {
					if(element.unitType == 'LOADUNIT' || element.unitType == 'DELIVERY'){
						element.started = '';
						element.finished = '';
						element.runtime = '';
					}
					else{
						element.runtime = this.helperService.calculateDuration(element.started, element.finished, true);
					}
					element.detailMenuConfig = [];
					element.detailMenuConfig.push({
						isShowDetails: true,
						menuText: 'ShowDetails',
						menuIcon: 'open_in_new',
						service: 'showpublishlog',
						params: { loadid: element.id },
					});
				});
				this.dataSource = new MatTableDataSource<CsvImports>(result.records);
				this.dataSource.sort = this.sort;
				this.paginator.pageIndex = 0;
				this.dataSource.paginator = this.paginator;
				this.table.dataSource = this.dataSource;
				this.filterTrayComponent?.showSpinner(false);
			},
			(error) => {
				if (error.status === 504) {
					this.notify('Gateway Timeout. The server took too long to respond. Please try again later.');
				} 
				else {
					this.notify(`An error occurred: ${error.message}`);
				}
				this.isLoading = false;
				this.filterTrayComponent?.showSpinner(false);
			});
		this.SetFiltersCount();
	}

	private tagItemsFilter(value: string): TagItem[] {
		const filterValue = value.toLowerCase();

		return this.allStatusIds.filter((item) => item.name.toLowerCase().indexOf(filterValue) === 0);
	}

	private notify(message: string) {
		this.snackBar.openFromComponent(DefaultSnackbarComponent, {
			duration: 10 * 1000,
			data: message,
		});
	}

	selectRow(row: CsvImports) {
		this.csvImportDetail = [];
		row.isSelected = true;
		this.dataSource.data.forEach((element) => {
			if (row.id != element.id) {
				element.isExpanded = false;
				element.isSelected = false;
			}
		});

		this.selectedRow = row;
		this.csvImportService.getDetailAsync(row.id, row.unitType).subscribe((result) => {
			this.helperService.floatingPanelData$.next({ data: result.records, jobType: JobTypes.CsvImport });
		});
	}

	clickNextButton() {
		if(this.selectedRow){
			this.selectedRow.isSelected = false;
		}
	}
	checkAtleastOneRowChecked() {
		return this.dataSource.data.filter((i) => i.isChecked).length > 0;
	}
	rowToggleSelection(ev, item) {
		item.isChecked = ev.checked;
	}
	openJiraTicket(): void {
		const checkedList = this.dataSource.data.filter((i) => i.isChecked);
		const dialogRef = this.dialogbox.open(CreateTicketDialogComponent, {
			width: '1000px',
			height: '487px',
			data: {
				ids: checkedList.map((i) => {
					return i.id;
				}),
				jobType: this.jobType,
				title: 'CsvImport',
			},
		});

		dialogRef.afterClosed().subscribe((resSubmit) => {
			if (resSubmit === true) {
				this.dataSource.data.forEach((i) => (i.isChecked = false));
				this.selection.clear();
				this.IsSelectAll = false;
			}
		});
	}

	selectRows(selectrow) {
		this.IsSelectAll = !this.IsSelectAll;
		let endIndex: number;
		const page = this.dataSource.paginator !== null ? this.dataSource.paginator.pageSize : 0;
		const pageindex = this.dataSource.paginator !== null ? this.dataSource.paginator.pageIndex : 0;

		if (!selectrow.checked) {
			this.selection.clear();
			if (this.dataSource.data.length > (pageindex + 1) * page) {
				endIndex = (pageindex + 1) * page;
			} else {
				endIndex = this.dataSource.data.length;
			}

			for (let index = pageindex * page; index < endIndex; index++) {
				const row = this.dataSource.data[index];
				this.selection.deselect(row);
				this.dataSource.data[index].isChecked = false;
			}
		} else {
			if (this.dataSource.data.length > (pageindex + 1) * page) {
				endIndex = (pageindex + 1) * page;
			} else {
				endIndex = this.dataSource.data.length;
			}
			for (let index = pageindex * page; index < endIndex; index++) {
				const row = this.dataSource.sortData(this.dataSource.data, this.sort)[index];
				this.selection.select(row);
				this.dataSource.sortData(this.dataSource.data, this.sort)[index].isChecked = true;
			}
		}
	}

	isSelectedPage() {
		const numSelected = this.selection.selected.length;
		const page = this.dataSource.paginator !== null ? this.dataSource.paginator.pageSize : 0;
		const pageindex = this.dataSource.paginator !== null ? this.dataSource.paginator.pageIndex : 0;
		let endIndex: number;
		if (this.dataSource.data.length > (pageindex + 1) * page) {
			endIndex = (pageindex + 1) * page;
		} else {
			endIndex = this.dataSource.data.length - pageindex * page;
		}
		return numSelected === endIndex;
	}

	//Quick Filter Methods//
	openmodal() {
		this.eventsSubject.next();
	}

	disablesave(status) {
		this.IsDisabled = status;
	}

	setFormValue(autoRefreshStatus) {
		this.CsvImportForm.patchValue({
			isAutoRefresh: autoRefreshStatus === undefined || autoRefreshStatus === false ? false : true,
		});
	}

	saveInQuickFilter(autoRefreshStatus, filterName) {
		this.setFormValue(autoRefreshStatus);
		this.currentfilter = JSON.parse(localStorage.getItem('QuickFilter') || '[]');
		this.currentfilter.push({
			data: this.selectedFilters,
			filter: filterName,
			slice: window.location.href.split('/')[3],
			user: localStorage.getItem('username'),
		});
		this.defaultoption = filterName;
		const oldcache = this.cacheObj;
		const olddefault = localStorage.getItem(this.cacheName + '-DefaultFilter');
		this.ConfirmApply(autoRefreshStatus);
		this.cacheObj = oldcache;
		localStorage.setItem(this.cacheName + '-DefaultFilter', olddefault || '[]');
		localStorage.setItem('QuickFilter', JSON.stringify(this.currentfilter));
		localStorage.setItem(this.cacheName + '-LastFilter', filterName);
		this.SetFiltersCount();
	}

	applyfilter(selectedvalue) {
		this.resetForm.next(true);
		const oldautotoggle = this.cacheObj.isAutoRefresh;
		selectedvalue = selectedvalue.value ? selectedvalue.value : selectedvalue;
		this.defaultoption = selectedvalue;
		localStorage.setItem(this.cacheName + '-LastFilter', this.defaultoption || 'Default');
		if (selectedvalue == 'Default') {
			if (
				JSON.parse(localStorage.getItem(this.cacheName + '-DefaultFilter') || '[]').filter(
					(x) => x.user == localStorage.getItem('username') && x.slice == window.location.href.split('/')[3]
				)[0]
			) {
				this.cacheObj = JSON.parse(localStorage.getItem(this.cacheName + '-DefaultFilter') || '[]').filter(
					(x) => x.user == localStorage.getItem('username') && x.slice == window.location.href.split('/')[3]
				)[0]['data'];
			}

			localStorage.setItem(this.cacheName, JSON.stringify(this.cacheObj));
		} else {
			this.cacheObj = JSON.parse(localStorage.getItem('QuickFilter') || '[]')
				.filter((x) => x.user == localStorage.getItem('username') && x.slice == window.location.href.split('/')[3])
				.filter((x) => x.filter == selectedvalue)[0]['data'];
			localStorage.setItem(this.cacheName, JSON.stringify(this.cacheObj));
		}
		this.cacheObj.isAutoRefresh = oldautotoggle;
		this.CreateFilterSummaryChips();
    this.selectedFilters = this.cacheObj;
    if (this.cacheObj.selectedDays == '3') {
			const date = new Date();
			this.cacheObj.startDate = new Date(Date.UTC(date.getFullYear(), date.getMonth(), date.getDate() - 2, 0, 0, 0, 0));
			this.cacheObj.endDate = new Date(Date.UTC(date.getFullYear(), date.getMonth(), date.getDate(), 0, 0, 0, 0));
		} else if (this.cacheObj.selectedDays == '2') {
			const date = new Date();
			this.cacheObj.startDate = new Date(Date.UTC(date.getFullYear(), date.getMonth(), date.getDate(), 0, 0, 0, 0));
			this.cacheObj.endDate = new Date(Date.UTC(date.getFullYear(), date.getMonth(), date.getDate(), 0, 0, 0, 0));
		}
		setTimeout(() => {
			this.updateFC();
			this.refresh();
		}, 200);
		localStorage.setItem(this.cacheName, JSON.stringify(this.cacheObj));
	}

	CreateFilterSummaryChips() {
		if (this.cacheObj && Object.keys(this.cacheObj).length) {
			this.usersFilterSummary();
			this.statusFilterSummary();
			this.countryFilterSummary();
			this.domainProductGroupIdsFilterSummary();
			this.sectorIdsFilterSummary();
			this.categoryIdsFilterSummary();
			this.periodicityIdsFilterSummary();
			this.periodIdsFilterSummary();
			this.baseProjectIdsFilterSummary();
			this.baseProjectNameFilterSummary();
			this.unitTypeIdsFilterSummary();
			this.jobIdsFilterSummary();
			this.fileTypeIdsFilterSummary();
			this.datesFilterSummary(this.cacheObj.startDate, this.cacheObj.endDate);
		}
	}

	ConfirmApply(autoRefreshStatus) {
		this.saveInCache(autoRefreshStatus, false);
		this.CreateFilterSummaryChips();
		this.getSelectedFilters(this.cacheObj);
		let count;
		this.filterService.filtersSelected$.subscribe((selectedFilters: FilterRecords) => {
			count = Number(Object.keys(selectedFilters).length);
		});

		if (count > 0) {
			this.refresh();
		} else {
			if (confirm('Loading results without any filters selected will take some time to load. Are you sure you want to proceed?')) {
				this.refresh();
			}
		}
	}

	public isShownFilterTrayEventHandler($event: boolean) {
		this.filterTrayComponent?.toggleShowTray($event);
		this.FilterTrayOpenFlag = $event;
	}

	getWidth() {
		this.FilterTrayOpenFlag = !this.FilterTrayOpenFlag;
		return '100%';
	}

	filterSummaryChipClear(filteredItems) {
		if (filteredItems == 'ALL') {
			if (confirm('This will remove all of your selected filters and could not be reversed. Are you sure you want to do that?')) {
        		this.resetForm.next(true);
				this.filterTrayCacheService.getCacheNameData(this.cacheName);
				this.filterCount = '';
			}
		}
		this.saveInCache(this.cacheObj.isAutoRefresh, false);
		this.CreateFilterSummaryChips();
	}

	toggleChecked(check: any) {
		if (check === true) {
			this.saveInCache(check, true);
			this.toggleInterval = setInterval(() => {
				if (this.defaultoption != 'Default') {
					this.refresh();
				}
			}, this.timer * 1000);
		} else {
			if (this.toggleInterval !== undefined) clearInterval(this.toggleInterval);
			if (this.cacheInterval !== undefined) clearInterval(this.cacheInterval);
			this.saveInCache(check, true);
		}
	}

	SetFiltersCount() {
		let count;
		this.filterService.filtersSelected$.subscribe((selectedFilters: FilterRecords) => {
			count = Number(Object.keys(selectedFilters).length);
		});
		if (count > 0) {
			this.filterCount = '(' + count.toString() + ')';
		} else {
			this.filterCount = '';
		}
	}

	ngOnDestroy() {
		if (this.cacheInterval) {
			clearInterval(this.cacheInterval);
		}

		if (this.toggleInterval) {
			clearInterval(this.toggleInterval);
		}
		this.destroy$.next();
	    this.destroy$.complete();
	}

	countryFilterSummary() {
		this.filterService.setFilters({
			countryIds: {
				name: 'Country',
				values: this.cacheObj.countries,
				slice: window.location.href.split('/')[3],
			} as Filter,
		});
	}

	usersFilterSummary() {
		this.filterService.setFilters({
			userIds: {
				name: 'User',
				values: this.cacheObj.users?.map((i) => i.name),
				slice: window.location.href.split('/')[3],
			} as Filter,
		});
	}

	statusFilterSummary() {
		this.filterService.setFilters({
			statusIds: {
				name: 'Status',
				values: this.cacheObj.status,
				slice: window.location.href.split('/')[3],
			} as Filter,
		});
	}

	domainProductGroupIdsFilterSummary() {
		this.filterService.setFilters({
			domainProductGroupIds: {
				name: 'Domain',
				values: this.cacheObj.domainProductGroup,
				slice: window.location.href.split('/')[3],
			} as Filter,
		});
	}

	sectorIdsFilterSummary() {
		this.filterService.setFilters({
			sectorIds: {
				name: 'Sector',
				values: this.cacheObj.sector,
				slice: window.location.href.split('/')[3],
			} as unknown as Filter,
		});
	}

	categoryIdsFilterSummary() {
		this.filterService.setFilters({
			categoryIds: {
				name: 'Category',
				values: this.cacheObj.category,
				slice: window.location.href.split('/')[3],
			} as Filter,
		});
	}

	periodicityIdsFilterSummary() {
		this.filterService.setFilters({
			reportingProjectName: {
				name: 'Periodcity',
				values: this.cacheObj.periodicityIds? [{ value: this.cacheObj.periodicityIds, label: this.cacheObj.periodicityIds }] : null,
				slice: window.location.href.split('/')[3],
			} as unknown as Filter,
		});
	}

	periodIdsFilterSummary() {
		this.filterService.setFilters({
			periodIds: {
				name: 'Period',
				values: this.cacheObj.periodIds,
				slice: window.location.href.split('/')[3],
			} as Filter,
		});
	}

	baseProjectIdsFilterSummary() {
		this.filterService.setFilters({
			baseProjectIds: {
				name: 'BaseProject',
				values: this.cacheObj.baseProjectIds,
				slice: window.location.href.split('/')[3],
			} as Filter,
		});
	}

	baseProjectNameFilterSummary() {
		this.filterService.setFilters({
			baseProjectName: {
				name: 'BaseProjectName',
				values: this.cacheObj.baseProjectName? [{ value: this.cacheObj.baseProjectName, label: this.cacheObj.baseProjectName }] : '',
				slice: window.location.href.split('/')[3],
			} as unknown as Filter,
		});
	}

	unitTypeIdsFilterSummary() {
		this.filterService.setFilters({
			unitTypeIds: {
				name: 'Unit Type',
				values: this.cacheObj.unitType,
				slice: window.location.href.split('/')[3],
			} as Filter,
		});
	}

	jobIdsFilterSummary() {
		this.filterService.setFilters({
			jobIds: {
				name: 'Job',
				values: this.cacheObj.jobIds,
				slice: window.location.href.split('/')[3],
			} as Filter,
		});
	}

	fileTypeIdsFilterSummary() {
		this.filterService.setFilters({
			fileType: {
				name: 'File Type',
				values: this.cacheObj.fileType,
				slice: window.location.href.split('/')[3],
			} as Filter,
		});
	}

	datesFilterSummary(dateStart: any, dateEnd: any) {
		if (dateStart != null || dateEnd != null) {
			const start = dateStart == 'Invalid Date' || dateStart == null ? '' : this.datepipe.transform(dateStart, 'yyyy-MM-dd');
			const end = dateEnd == 'Invalid Date' || dateEnd == null ? '' : this.datepipe.transform(dateEnd, 'yyyy-MM-dd');
			this.filterService.setFilters({
				dates: {
					name: 'Date',
					values: (start !== undefined && end !== undefined) ? [start, end] : null,
					slice: window.location.href.split('/')[3],
				} as Filter,
			});
		} else {
			this.filterService.setFilters({
				dates: {
					name: 'Date',
					values: null,
					slice: window.location.href.split('/')[3],
				} as unknown as Filter,
			});
		}
	}

	//Quick Filter Methods End//

  	getSelectedFilters(selectedFilters: any){
		this.cacheObj = selectedFilters;
		this.selectedFilters = selectedFilters;
	}
}
