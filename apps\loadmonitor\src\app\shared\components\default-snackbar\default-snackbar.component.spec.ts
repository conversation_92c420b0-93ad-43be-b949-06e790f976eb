import {
  MatSnackBarModule,
  MAT_SNACK_BAR_DATA,
} from '@angular/material/snack-bar';
import { SharedModule } from '@loadmonitor/shared/shared.module';
import { ComponentFixture, TestBed } from '@angular/core/testing';

import { DefaultSnackbarComponent } from './default-snackbar.component';

describe('DefaultSnackbarComponent', () => {
  let component: DefaultSnackbarComponent;
  let fixture: ComponentFixture<DefaultSnackbarComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [DefaultSnackbarComponent],
      imports: [SharedModule, MatSnackBarModule],
      providers: [{ provide: MAT_SNACK_BAR_DATA, useValue: {} }],
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(DefaultSnackbarComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
