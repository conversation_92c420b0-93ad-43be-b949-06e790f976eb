import { DWHRELEASE } from '../interfaces/dwhrelease';
import { PropertyPage } from '../interfaces/PropertyPage';
import { Injectable } from '@angular/core';
import { Observable, Subject } from 'rxjs';
import { HttpClient } from '@angular/common/http';
import { ConfigService } from './config.service';
import { Jobs } from '@loadmonitor/shared/interfaces/Jobs';

@Injectable({
	providedIn: 'root',
})
export class DWHReleaseService {
	private url!:string;
	public floatingPanelData$ = new Subject<PropertyPage[]>();
	public floatingPanelDestroy$ = new Subject<boolean>();

	constructor(private http: HttpClient, private config: ConfigService) {
		this.url = `${this.config.getApiUrl()}/DwhReleases`;
	}

	getAsync(filterParams: DwhReleaseFilterParams): Observable<Jobs<DWHRELEASE>> {
		return this.http.post<Jobs<DWHRELEASE>>(this.url, filterParams);
	}

	getDetailAsync(loadId?: number): Observable<Jobs<PropertyPage>> {
		return this.http.get<Jobs<PropertyPage>>(this.url + '/' + loadId);
	}
}

export interface DwhReleaseFilterParams {
	loadIds?: number[];
	countryIds?: number[];
	periodIds?: number[];
	periodicityIds?: number[];
	baseProjectIds?: number[];
	qcProjectIds?: number[];
	baseProjectTypeIds?: number[];
	jobTypes?: string[];
	qcProjectName?: string;
	userNames?: string[];
	statusIds?: number[];
	startDate?: Date;
	endDate?: any;
	categoryIds?: number[];
	sectorIds?: number[];
	domainProductGroupIds?: number[];
	jeejobIds?: number[];
}
