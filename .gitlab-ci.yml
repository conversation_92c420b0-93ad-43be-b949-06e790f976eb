include:
    - project: 'dp/de/shared/pipeline/generic-pipelines'
      file: 'node/native/_pipeline.yml'

variables:
    UI_NAME: 'loadmonitor'
    BFF_NAME: 'lmx-bff'
    CONTAINER_REGISTRY: 'nexus.gfk.com:8471'
    SONAR_PRODUCT_NAME: DWH.LoadMonitor.UI
    SONAR_PROJECT_KEY: DWH.LoadMonitor.UI
    GITLAB_CONTAINER_REGISTRY: "$CI_REGISTRY/dp/de/products/load-monitor/loadmonitor-ui/${APP_NAME}:${CI_GFK_SEMVER}"