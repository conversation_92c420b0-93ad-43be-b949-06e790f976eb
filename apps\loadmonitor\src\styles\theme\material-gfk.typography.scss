@use 'gfk.typography' as gfk;
@use 'sass:map' as map;
@use 'node_modules/@angular/material/index' as mat;

$typography: mat.define-typography-config(
    $font-family:   map.get(gfk.$typography, 'font-family'),
    $headline:      mat.define-typography-level(map.get(gfk.$typography, 'h1-font-size'), 1.5, 400), //.mat-h1, .mat-headline, <h1>
    $title:         mat.define-typography-level(map.get(gfk.$typography, 'h2-font-size'), 1.5, 400), //.mat-h2, .mat-title, <h2>
    $subheading-2:  mat.define-typography-level(map.get(gfk.$typography, 'h3-font-size'), 1.5, 400), //.mat-h3, .mat-subheading-2, <h3>
    $subheading-1:  mat.define-typography-level(map.get(gfk.$typography, 'h4-font-size'), 1.5, 400), //.mat-h4, .mat-subheading-1, <h4>
    $body-1:        mat.define-typography-level(map.get(gfk.$typography, 'body-font-size'), 1.5, 400), //.mat-body, .mat-body-1, <body>
    $caption:       mat.define-typography-level(map.get(gfk.$typography, 'caption-font-size'), 1.5, 400), //.mat-small, .mat-caption
    //$body-2:        define-typography-level(14px, 24px, 500),
    //$display-4:     define-typography-level(112px, 112px, 300, $letter-spacing: -0.05em),
    //$display-3:     define-typography-level(56px, 56px, 400, $letter-spacing: -0.02em),
    //$display-2:     define-typography-level(45px, 48px, 400, $letter-spacing: -0.005em),
    //$display-1:     define-typography-level(34px, 40px, 400),
    //$button:        define-typography-level(14px, 14px, 500),
    // Line-height must be unit-less fraction of the font-size.
    //$input:         define-typography-level(inherit, 1.125, 400)
);
