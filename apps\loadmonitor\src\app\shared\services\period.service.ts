import { Period } from './../interfaces/Period';
import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { ConfigService } from './config.service';
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class PeriodService {
  private url!:string;

  constructor(private http: HttpClient, private config: ConfigService) {
    this.url = `${this.config.getPeriodicityApiUrl()}/Periods/current`;
  }
  getAsync(periodicityIds?: number, query?: string): Observable<Period[]> {

    const customURL = this.url + '?periodicityId=' + periodicityIds + (query?.length ? '&query=' + query : '');

    return this.http.get<Period[]>(customURL);
  }
}
