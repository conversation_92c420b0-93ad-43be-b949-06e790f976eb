import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ItemMasterDataComponent } from './item-master-data.component';
import { ReactiveFormsModule } from '@angular/forms';
import { MatSnackBar } from '@angular/material/snack-bar';
import { MatDialog } from '@angular/material/dialog';
import { ItemMasterDataService } from '@loadmonitor/shared/services/item-master-data.service';
import { FilterService } from '@dwh/lmx-lib/src';
import { BehaviorSubject } from 'rxjs';
import { NO_ERRORS_SCHEMA } from '@angular/core';
import { DatePipe } from '@angular/common';

describe('ItemMasterDataComponent', () => {
  let component: ItemMasterDataComponent;
  let fixture: ComponentFixture<ItemMasterDataComponent>;
  let itemMasterDataServiceMock: any;
  let snackBarMock: any;
  let dialogMock: any;
  let filterServiceMock: any;

  beforeEach(async () => {
    itemMasterDataServiceMock = {
      getAsync: jest.fn().mockReturnValue(new BehaviorSubject({ records: [], count: 0 })),
    };

    snackBarMock = {
      openFromComponent: jest.fn(),
    };

    dialogMock = {
      open: jest.fn().mockReturnValue({
        afterClosed: jest.fn().mockReturnValue(new BehaviorSubject(true)),
      }),
    };

    filterServiceMock = {
      filtersSelected$: new BehaviorSubject({}),
    };

    await TestBed.configureTestingModule({
      imports: [ReactiveFormsModule],
      declarations: [ItemMasterDataComponent],
      providers: [
        { provide: ItemMasterDataService, useValue: itemMasterDataServiceMock },
        { provide: MatSnackBar, useValue: snackBarMock },
        { provide: MatDialog, useValue: dialogMock },
        { provide: FilterService, useValue: filterServiceMock },
        DatePipe
      ],
      schemas: [NO_ERRORS_SCHEMA],
    }).compileComponents();

    fixture = TestBed.createComponent(ItemMasterDataComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

});
