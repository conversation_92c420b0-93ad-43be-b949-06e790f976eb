export interface BcrReworks {
  loadId: number;
  bcrRearrangeId: number;
  status: string;
  country: string;
  domainProductGroup: string;
  channel: string;
  feature?: string;
  featureValue?: string;
  fromPeriod?: Date;
  toPeriod?: Date;
  message?: string;
  targetBaseChannel: string;
  createdBy: string;
  created: Date;
  started: Date;
  finished: Date;
  jobId: number;
  jobPriority: number;
  addTargetRbbp: boolean;
  isChecked?: boolean;
}
