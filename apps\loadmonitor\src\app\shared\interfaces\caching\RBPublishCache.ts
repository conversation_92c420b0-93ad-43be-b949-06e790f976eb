import { UserCache } from '@loadmonitor/shared/interfaces/caching/UserCache';
import { BaseProjectType } from '../BaseProjectType';
import { Category } from '../Category';
import { Country } from '../Country';
import { DomainProductGroups } from '../DomainProductGroups';
import { Sector } from '../Sector';

export interface RBPublishCache
{
    sectorIds: number[];
    categoryIds: number[];
    domainProductGroupIds: number[];
    domainProductGroup: DomainProductGroups[];
    countryIds: number[];
    countries:Country[];
    status: number[];
    selectedDays: string;
    startDate?: Date;
    endDate?: Date;
    users?: UserCache[];

    periodicityIds: number[];
    periodIds: any;
    project: string;
    bpTypeIds: number[];
    baseProjectType: BaseProjectType[];

    rbProjectIds: number[],
    jobIds: number[],
    loadIds: number[],
    operationIds: number[],
    isAutoRefresh?: boolean;
    category:Category[];
    sector:Sector[];
    rbProjectName: string;
}
