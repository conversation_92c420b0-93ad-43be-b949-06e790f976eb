import { Component, EventEmitter, Input, Output } from '@angular/core';

@Component({
	selector: 'lmx-filter-tray',
	templateUrl: './filter-tray.component.html',
	styleUrls: ['./filter-tray.component.scss'],
})
export class FilterTrayComponent {
	@Input() IsEnabled=false;
	@Input() IsDisabled=false;
	isShown?: any = true;
	isLoading?: any = false;
	@Output() clickApplyButton = new EventEmitter<any>();
	@Output() clickSaveButton = new EventEmitter<any>();


  // eslint-disable-next-line @angular-eslint/no-output-rename
  @Output("ButtonShowHideEvent") ButtonShowHideEvent: EventEmitter<any> = new EventEmitter();

	toggleShowTray(isTrayShown: any): void
	{
		this.isShown = isTrayShown;
    this.ButtonShowHideEvent.emit();
	}

	showSpinner(isSpinnerShown: any): void
	{
		this.isLoading = isSpinnerShown;
	}

	clickApplyFilterTrayButton()
	{
		this.clickApplyButton.emit();
	}
	clickSaveFilterTrayButton()
	{
		this.clickSaveButton.emit();
	}
}
