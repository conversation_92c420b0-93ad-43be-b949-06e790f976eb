import { Injectable } from "@angular/core";
import { ConfigService } from './config.service';
import { Observable } from 'rxjs';
import { HttpClient } from '@angular/common/http';
import { Jobs } from '@loadmonitor/shared/interfaces/Jobs';
import { MigrationRuleRuns } from "../interfaces/MigrationRuleRuns";
import { MigrationRuleRunPropertyPage } from "../interfaces/MigrationRuleRunPropertyPage";

@Injectable({
  providedIn: 'root',
})
export class MigrationRuleRunsService {
  private url!:string;

  constructor(private http: HttpClient, private config: ConfigService) {
    this.url = `${this.config.getApiUrl()}/MigrationRuleRuns`;
  }

  getAsync(
    startDate?: Date,
    endDate?: any,
    userNames?: string[],
    statusIds?: number[],
    ruleSetName?: string,
    scopeTypeId?: number | null,
    scope?: string,
    sourceComponentIds?: number[],
    targetComponentIds?: number[],
    runIds?: number[],
    ruleSetIds?: number[]
  ): Observable<Jobs<MigrationRuleRuns>> {
    const body = {
      startDate,
      endDate,
      userNames,
      statusIds,
      ruleSetName,
      scopeTypeId,
      scope,
      sourceComponentIds,
      targetComponentIds,
      runIds,
      ruleSetIds
    };

    return this.http.post<Jobs<MigrationRuleRuns>>(this.url, body);
    //return this.http.get<Jobs<MigrationRuleRuns>>('/assets/response/migrationRuleRun.json');
  }

  getDetailAsync(runId?: number): Observable<MigrationRuleRunPropertyPage[]> {
    return this.http.get<MigrationRuleRunPropertyPage[]>(this.url + '/' + runId);
  }
}
