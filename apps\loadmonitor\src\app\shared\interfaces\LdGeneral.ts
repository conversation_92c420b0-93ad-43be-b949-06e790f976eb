import { DetailMenuConfiguration } from "./DetailMenuConfiguration";

export interface LdGeneral {
  loadDefinitionId: number;
  status: string;
  epoLoadMode: string;
  isAutoload: boolean;
  productionProjectName: string;
  productionProjectId: number;
  productionProjectType: number;
  period: string;
  dataOrderId: number;
  createdBy: string;
  created: Date;
  changedBy?: string;
  changed?: Date;
  sentBy?: string;
  sent?: string;
  detailMenuConfig?: DetailMenuConfiguration[];
  isSelected?: boolean;
  isExpanded?: boolean;
  isChecked?: boolean;
}
