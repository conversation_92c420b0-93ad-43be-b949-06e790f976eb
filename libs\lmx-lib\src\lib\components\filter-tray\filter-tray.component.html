<div *ngIf="isShown">
	<div id="mySidenav" class="sidenav">
		<gfk-tray [title]="'Filters'" [show]="true" [hideFooter]="false" (onClose)="toggleShowTray(false)">
			<ng-container contentSection>
				<div *ngIf="isLoading" class="dot-flashing apply"></div>
				<ng-content></ng-content>
			</ng-container>
			<ng-container actionButtons>
				<button 
					testId="lmx-button"
					class="mr-2 alignright apply filtertraywidth gfk-button filter-btn"
					[ngClass]="isLoading ? 'disabled-btn' : ''"
					(click)="clickSaveFilterTrayButton()"
					*ngIf="IsEnabled"
					[disabled]="isLoading || IsDisabled">
					Save filter
				</button>
				<button 
					testId="lmx-button" 
					class="apply w-2/4 gfk-button primary"
					[ngClass]="isLoading ? 'disabled-btn' : ''"
					[disabled]="isLoading" 
					(click)="clickApplyFilterTrayButton()">
					Apply
				</button>
			</ng-container>
		</gfk-tray>
	</div>
</div>
