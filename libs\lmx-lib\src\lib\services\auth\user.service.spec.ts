import { HttpClient, Http<PERSON>andler } from '@angular/common/http';
import { delay, of } from 'rxjs';
import { AuthenticationService, SignOutResponse } from './authentication.service';
import { User, UserService } from './user.service';

describe('UserService', () => {
	let userService: UserService;
	let http: HttpClient = new HttpClient({} as HttpHandler);
	let authService = new AuthenticationService(http, {} as any, {} as any, {} as any);

	describe('user', () => {
		it(`should return user when user is logged in`, (done) => {
			const user = { userId: 1, userName: 'JohnDoe' } as User;
			jest.spyOn(http, 'get').mockImplementation(() => of(user));

			userService = new UserService(http, {} as any, {} as any, {} as any);
			userService.user$.subscribe((user) => {
				expect(user).toEqual({ userId: 1, userName: 'JohnDoe' });
				done();
			});
			userService.signInUser();
		});

		it(`should return no user when user signed out again`, (done) => {
			const user = { userId: 1, userName: 'JohnDoe' } as User;
			let counter = 0;
			jest.spyOn(http, 'get').mockImplementation(() => of(user));
			jest.spyOn(authService, 'signOut').mockImplementation(() => of({ links: {} }).pipe(delay(40)));

			userService = new UserService(http, authService, {} as any, {} as any);
			userService.user$.subscribe((user) => {
				if (counter === 0) {
					expect(user).toEqual({ userId: 1, userName: 'JohnDoe' });
					counter += 1;

					userService.signOut().subscribe();
				} else if (counter === 1) {
					expect(user).toEqual(undefined);
					done();
				}
			});

			userService.signInUser();
		});
	});

});
