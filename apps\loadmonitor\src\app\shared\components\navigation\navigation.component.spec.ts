import { ComponentFixture, TestBed } from '@angular/core/testing';
import { NavigationComponent } from './navigation.component';
import { ChangeDetectorRef } from '@angular/core';
import { AuthFacade } from '@dwh/lmx-lib/src/lib/services/auth';
import { of } from 'rxjs';

describe('NavigationComponent', () => {
  let component: NavigationComponent;
  let fixture: ComponentFixture<NavigationComponent>;
  let authFacadeMock: any;
  let changeDetectorRefMock: any;

  beforeEach(async () => {
    authFacadeMock = {
      user$: of({ email: '<EMAIL>' }),
      signOut: jest.fn().mockReturnValue(of(null)),
      signIn: jest.fn(),
    };

    changeDetectorRefMock = {
      detectChanges: jest.fn(),
    };

    await TestBed.configureTestingModule({
      declarations: [NavigationComponent],
      providers: [
        { provide: AuthFacade, useValue: authFacadeMock },
        { provide: ChangeDetectorRef, useValue: changeDetectorRefMock },
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(NavigationComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create the component', () => {
    expect(component).toBeTruthy();
  });
  
});
