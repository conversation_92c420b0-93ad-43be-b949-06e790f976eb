<div class="card">
    <div class="card-body">
    
        <h3 class="card-title title_is_{{ disableColor }}">{{card ? card.title:''}}</h3>
            <div class="description" data-title="{{card ?card.tooltip:''}}">
                {{card ?card.description:''}}
            </div>
            <div class="tooltip" *ngIf="card ?card.tooltip:''">...
                <span class="tooltiptext">{{card ? card.tooltip:''}}</span>
              </div>
        <div class="card-footer">
            <div class="action-div">
                 <a href="{{disableEnableButton}}" target="{{target_blank}}" class="is_{{ disableColor }}">Access</a>
            </div>
        </div>
    </div>  
</div>

