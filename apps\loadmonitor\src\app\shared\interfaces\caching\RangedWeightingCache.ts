import { UserCache } from '@loadmonitor/shared/interfaces/caching/UserCache';
import { Country } from '../Country';
export interface RangedWeightingCache {
    importIds: number[];
    jobIds: number[];
    status: number[];
    selectedDays: string;
    startDate?: Date;
    endDate?: Date;
    users?: UserCache[];
    qcProjectIds: number[];
    qcProjectName: string;
    isAutoRefresh?: boolean;
    countries:Country[];
  }
