import { ComponentFixture, TestBed } from '@angular/core/testing';
import { RangedWeightingImportComponent } from './ranged-weighting-import.component';
import { MatTableDataSource } from '@angular/material/table';
import { RangedWeightingImportService } from '@loadmonitor/shared/services/ranged-weighting-import.service';
import { MatSnackBar } from '@angular/material/snack-bar';
import { FormBuilder } from '@angular/forms';
import { of } from 'rxjs';
import { DatePipe } from '@angular/common';
import { FilterService } from '@dwh/lmx-lib/src';

describe('RangedWeightingImportComponent', () => {
  let component: RangedWeightingImportComponent;
  let fixture: ComponentFixture<RangedWeightingImportComponent>;
  let rangedWeightingImportService: jest.Mocked<RangedWeightingImportService>;
  let snackBar: jest.Mocked<MatSnackBar>;
  let filterService: jest.Mocked<FilterService>;

  beforeEach(async () => {
    const rangedWeightingImportServiceMock = {
      getAsync: jest.fn(),
    };

    const snackBarMock = {
      openFromComponent: jest.fn(),
    };

    const filterServiceMock = {
      setFilters: jest.fn(),
      filtersSelected$: {
        subscribe: jest.fn(),
      },
    };

    await TestBed.configureTestingModule({
      declarations: [RangedWeightingImportComponent],
      providers: [
        { provide: RangedWeightingImportService, useValue: rangedWeightingImportServiceMock },
        { provide: MatSnackBar, useValue: snackBarMock },
        { provide: FormBuilder, useClass: FormBuilder },
        { provide: FilterService, useValue: filterServiceMock },
        DatePipe,
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(RangedWeightingImportComponent);
    component = fixture.componentInstance;
    rangedWeightingImportService = TestBed.inject(RangedWeightingImportService) as jest.Mocked<RangedWeightingImportService>;
    snackBar = TestBed.inject(MatSnackBar) as jest.Mocked<MatSnackBar>;
    filterService = TestBed.inject(FilterService) as jest.Mocked<FilterService>;
  });

  beforeEach(() => {
    component.selectedFilters = { status: [], importIds: [], jobIds: [], qcProjectIds: [], users: [] };
  });

  it('should create the component', () => {
    expect(component).toBeTruthy();
  });

  // Additional tests can be added for other methods and functionalities
});
