{"name": "loadmonitor", "$schema": "../../node_modules/nx/schemas/project-schema.json", "projectType": "application", "sourceRoot": "apps/loadmonitor/src", "prefix": "lm", "tags": [], "implicitDependencies": ["lmx-lib"], "targets": {"build": {"executor": "@nx/angular:webpack-browser", "outputs": ["{options.outputPath}"], "options": {"allowedCommonJsDependencies": ["opentracing/lib/tracer", "opentracing/lib/span", "opentracing/lib/constants", "error-stack-parser", "lodash", "moment", "classnames"], "outputPath": "dist/apps/loadmonitor", "index": "apps/loadmonitor/src/index.html", "main": "apps/loadmonitor/src/main.ts", "polyfills": "apps/loadmonitor/src/polyfills.ts", "tsConfig": "apps/loadmonitor/tsconfig.app.json", "inlineStyleLanguage": "scss", "assets": ["apps/loadmonitor/src/favicon.ico", "apps/loadmonitor/src/assets", "apps/loadmonitor/src/unsupported_browser.html"], "styles": ["apps/loadmonitor/src/styles.scss"], "scripts": []}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "1mb", "maximumError": "3mb"}, {"type": "anyComponentStyle", "maximumWarning": "2kb", "maximumError": "4kb"}], "fileReplacements": [{"replace": "apps/loadmonitor/src/environments/environment.ts", "with": "apps/loadmonitor/src/environments/environment.prod.ts"}], "outputHashing": "all"}, "development": {"buildOptimizer": false, "optimization": false, "vendorChunk": true, "extractLicenses": false, "sourceMap": true, "namedChunks": true}}, "defaultConfiguration": "production"}, "serve": {"executor": "@angular-devkit/build-angular:dev-server", "options": {"proxyConfig": "apps/loadmonitor/src/proxy.conf.json"}, "configurations": {"production": {"buildTarget": "loadmonitor:build:production"}, "development": {"buildTarget": "loadmonitor:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"executor": "@angular-devkit/build-angular:extract-i18n", "options": {"buildTarget": "loadmonitor:build"}}, "lint": {"executor": "@nx/eslint:lint"}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/apps/loadmonitor"], "options": {"jestConfig": "apps/loadmonitor/jest.config.ts"}}}}