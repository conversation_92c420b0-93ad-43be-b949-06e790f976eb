import { UserCache } from '@loadmonitor/shared/interfaces/caching/UserCache';
import { Country } from '../Country';
import { DomainProductGroups } from '../DomainProductGroups';
import { Category } from '../Category';
import { BaseProjectType } from '../BaseProjectType';
import { Sector } from '../Sector';

export interface DWHQcCache {
    qcProjectIds: number[];
    baseProjectIds: number[];
    countryIds: number[];
    countryNames: string[];
    sectorIds: number[];
    categoryIds: number[];
    domainProductGroupIds: number[];
    periodicityIds: number[];
    periodicityNames: string[];
    periodIds: any;
    bpTypeIds: number[];
    bpTypeNames: string[];
    baseProjectType: BaseProjectType[];
    statusIds: number[];
    selectedDays: string;
    startDate?: Date;
    endDate?: Date;
    users?: UserCache[];
    qcProjectflag:any;
    qcProjectName:string;
    baseProjectName:string;
    isAutoRefresh?: boolean;
    countries:Country[];
    domainProductGroup: DomainProductGroups[];
    category:Category[];
    sector:Sector[];
  }
