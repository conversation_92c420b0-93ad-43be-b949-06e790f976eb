@use 'gfk-light.palette' as gfk;
@use 'sass:map' as map;

//https://developer.mozilla.org/en-US/docs/Web/CSS/color-scheme
:root {
  color-scheme: light;
}
//===========================================/CUSTOM GLOBAL LIGHT THEME CSS=============================================
body {
  background-color: map.get(gfk.$palette, 'bg');
  color: map.get(gfk.$palette, 'text');
}

@import "material-gfk-light.theme";


//===========================================CUSTOM GLOBAL LIGHT THEME CSS/=============================================
