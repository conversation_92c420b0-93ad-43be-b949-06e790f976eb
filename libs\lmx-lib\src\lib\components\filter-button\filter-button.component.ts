import { Component, EventEmitter, Input, Output } from '@angular/core';

@Component({
	selector: 'lmx-filter-button',
	templateUrl: './filter-button.component.html',
	styleUrls: ['./filter-button.component.scss'],
})
export class FilterButtonComponent  {
	@Input() icon = 'filter';
	@Input() showFilterCount!: string;
	@Output()  isShownFilterTray = new EventEmitter<any>();

	showFilterTray()
	{
		this.isShownFilterTray.emit(true);
	}
}
