<lm-navigation></lm-navigation>

<div class="each-slice-top-headbar" [ngClass]="FilterTrayOpenFlag ? 'filter-tray-open' : 'filter-tray-close'">
	<h3 class="alignleft">DWH/QC</h3>
	<div class="flexdiv-buttons">
		<lmx-filter-button showFilterCount="{{ filterCount }}" (isShownFilterTray)="isShownFilterTrayEventHandler($event)"> </lmx-filter-button>
	</div>
</div>

<lmx-filter-tray
	(ButtonShowHideEvent)="this.getWidth()"
	(clickApplyButton)="ConfirmApply(cacheObj.isAutoRefresh)"
	(clickSaveButton)="openmodal()"
	[IsEnabled]="IsEnabled"
	[IsDisabled]="IsDisabled"
>
	<lm-filter-tray-form
		[sliceName]="'DWHQC'"
		[resetForm]="resetForm"
		(selectedFilters)="getSelectedFilters($event)"
		[isPeriodHidden]="isPeriodFilterHidden"
	>
	</lm-filter-tray-form>
</lmx-filter-tray>

<div class="filter-area" [ngClass]="FilterTrayOpenFlag ? 'filter-tray-open' : 'filter-tray-close'">
	<div class="toggle-button">
		<div>
			<lm-quick-filter
				[selectedfilter]="defaultoption"
				class="p-3.5"
				[events]="eventsSubject.asObservable()"
				*ngIf="IsEnabled"
				[modalshow]="modalshow"
				(saveInQuickFilter)="saveInQuickFilter(cacheObj.isAutoRefresh, $event)"
				(applyquickfilter)="applyfilter($event)"
				(disabledbutton)="disablesave($event)"
			>
			</lm-quick-filter>

			<lm-filter-summary (resetFilters)="filterSummaryChipClear($event)"></lm-filter-summary>
		</div>
		<div class="refreshedTime">
			<lm-refreshed-date-time *ngIf="refreshedDateTime" class="pr-3 d-inline-block"></lm-refreshed-date-time>
			<gfk-toggle-button
				*ngIf="EnableRefresh"
				[checked]="cacheObj.isAutoRefresh || false"
				title="Auto Refresh"
				class="autoRefresh d-inline-block"
				[disabled]="defaultoption === 'Default'"
				(onChange)="toggleChecked($event)"
			>
			</gfk-toggle-button>
		</div>
	</div>
</div>

<article class="table-grid">
	<div class="mat-elevation-z8 tab-container" [ngClass]="(dwhQCData?.length) ? 'table-height' : ''">
		<table mat-table matSort class="full-width-table js-tbl-source-result">
			<ng-container matColumnDef="baseProjectId">
				<th mat-header-cell *matHeaderCellDef mat-sort-header>Base Project ID</th>
				<td mat-cell *matCellDef="let element">{{ element.baseProjectId }}</td>
			</ng-container>

			<ng-container matColumnDef="qcProjectId">
				<th mat-header-cell *matHeaderCellDef mat-sort-header>QC Project ID</th>
				<td mat-cell *matCellDef="let element">{{ element.qcProjectId }}</td>
			</ng-container>

			<ng-container matColumnDef="status">
				<th mat-header-cell *matHeaderCellDef mat-sort-header>Status</th>
				<td mat-cell *matCellDef="let element">{{ element.status }}</td>
			</ng-container>

			<ng-container matColumnDef="baseProjectName">
				<th mat-header-cell *matHeaderCellDef mat-sort-header>Base Project</th>
				<td mat-cell *matCellDef="let element">{{ element.baseProjectName }}</td>
			</ng-container>

			<ng-container matColumnDef="baseProjectType">
				<th mat-header-cell *matHeaderCellDef mat-sort-header>Base Project Type</th>
				<td mat-cell *matCellDef="let element">{{ element.baseProjectType }}</td>
			</ng-container>

			<ng-container matColumnDef="periodDescription">
				<th mat-header-cell *matHeaderCellDef mat-sort-header>Period</th>
				<td mat-cell *matCellDef="let element">
					{{ element.periodDescription }}
				</td>
			</ng-container>

			<ng-container matColumnDef="createdBy">
				<th mat-header-cell *matHeaderCellDef mat-sort-header>Created By</th>
				<td mat-cell *matCellDef="let element">{{ element.createdBy }}</td>
			</ng-container>

			<ng-container matColumnDef="created">
				<th mat-header-cell *matHeaderCellDef mat-sort-header>Created On</th>
				<td mat-cell *matCellDef="let element">{{ element.created | date: date_with_time }}</td>
			</ng-container>

			<ng-container matColumnDef="changedBy">
				<th mat-header-cell *matHeaderCellDef mat-sort-header>Changed By</th>
				<td mat-cell *matCellDef="let element">{{ element.changedBy }}</td>
			</ng-container>

			<ng-container matColumnDef="changed">
				<th mat-header-cell *matHeaderCellDef mat-sort-header>Changed On</th>
				<td mat-cell *matCellDef="let element">{{ element.changed | date: date_with_time }}</td>
			</ng-container>

			<tr mat-header-row *matHeaderRowDef="displayedColumns; sticky: true" class="columnHeader"></tr>
			<tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
		</table>
	</div>
	<mat-paginator [pageSizeOptions]="[10, 20, 50, 100]" showFirstLastButtons></mat-paginator>
</article>
<lm-version></lm-version>