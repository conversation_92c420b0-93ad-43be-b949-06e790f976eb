import { Component, ElementRef, Input, OnChanges, OnInit, SimpleChanges, ViewChild } from '@angular/core';
import { JobType } from '@loadmonitor/shared/interfaces/JobType';
import { COMMA, ENTER } from '@angular/cdk/keycodes';
import { FormControl } from '@angular/forms';
import { Observable } from 'rxjs/internal/Observable';
import { map, startWith } from 'rxjs/operators';
import { get } from 'lodash';
import { JobTypes } from '@loadmonitor/shared/jobTypes';

const EMPTY = null;

@Component({
  selector: 'lm-job-type-autocomplete',
  templateUrl: './job-type-autocomplete.component.html',
  styleUrls: ['./job-type-autocomplete.component.scss']
})
export class JobTypeAutocompleteComponent implements OnInit, OnChanges {
  @Input() jobType: JobTypes = JobTypes.none;
  @Input() cachedJobTypeIds:number[]=[];

  @ViewChild('jobTypeIdsInput')
  jobTypeIdsInput!: ElementRef<HTMLInputElement>;

  selectedJobTypeIds !: Observable<JobType[]>;

  //Tag-Chip-List
  readonly separatorKeysCodes = [ENTER, COMMA] as const;

  // Mat-Chips - JobType
  jobTypeIdsCtrl = new FormControl();
  filteredJobTypeIds: Observable<JobType[]> | undefined;
  allJobTypeIds: JobType[] = [];

  ngOnInit() {
    this.fillJobTypes(this.jobType);
    this.selectedJobTypeIds = this.jobTypeIdsCtrl.valueChanges.pipe(
      startWith(EMPTY),
      map(() =>
        this.filterSelectedJobTypeIds(),
      ),
    );
    this.filteredJobTypeIds = this.jobTypeIdsCtrl.valueChanges.pipe(
      startWith(null),
      map((jobType) => this.setJobTypeFilter(get(jobType, 'name', jobType)))
    );
    this.setSelectedJobTypes(this.cachedJobTypeIds);
    this.refreshSuggestionsAndEmptyInput();
  }

  public setSelectedJobTypes(periodicityIds: number[]): void {
    periodicityIds?.forEach((PPTid: number) => {
      const found = this.allJobTypeIds.find((PPT: JobType) => PPT.id === PPTid);
      if (found) {
        found.selected = true;
      } else {
        this.allJobTypeIds.push({id: PPTid, name: 'Loading...', selected: true})
      }
    });
    this.refreshSuggestionsAndEmptyInput();
  }

  public getSelectedJobTypeNames() {
    return this.filterSelectedJobTypeIds().map((status) => status.name);
  }

  public getSelectedJobTypeIds() {
    return this.filterSelectedJobTypeIds().map((status) => status.id);
  }

  filterSelectedJobTypeIds(): JobType[] {
    return this.allJobTypeIds.filter(
      (bpType) => bpType.selected,
    );
  }

  public removeJobTypes(): void {
    this.filterSelectedJobTypeIds().map((x) => x.selected = false);
    this.refreshSuggestionsAndEmptyInput();
  }

  fillJobTypes(jobType: JobTypes): void {

    switch (jobType) {
      case JobTypes.DWHRelease:
        this.allJobTypeIds = [
          { id: 1, name: 'CreateShop Execute', selected: false },
          { id: 2, name: 'CreateShop Template', selected: false },
          { id: 3, name: 'Dwh2Dwh', selected: false },
          { id: 4, name: 'Release', selected: false },
          { id: 5, name: 'Unrelease', selected: false }
        ];
        break;

      case JobTypes.itemMasterData:
        this.allJobTypeIds = [
          { id: 1, name: 'BasePG Encoding', selected: false },
          { id: 2, name: 'Prepare Item Masterdata', selected: false },
          { id: 3, name: 'Load Item Masterdata', selected: false },
          { id: 4, name: 'Rework Exclusivity', selected: false }
        ];
        break;

    }

    this.allJobTypeIds = this.allJobTypeIds.sort((firstService: JobType, otherService: JobType) => firstService.name.localeCompare(otherService.name));

  }


  // JobType Chips
  removeJobTypeChips(item: JobType): void {
    this.toggleSelection(item);
  }

  private setJobTypeFilter(value: string): JobType[] {
    if (value === '' || value === null) {
      return this.allJobTypeIds;
    }else {
      const filterValue = value.toLowerCase();
      return this.allJobTypeIds.filter(
        (PPT) => PPT.name.toLowerCase().indexOf(filterValue) === 0,
      );
    }
  }

  optionClicked(event: Event, item: JobType) {
    event.stopPropagation();
    this.toggleSelection(item);
  }

  toggleSelection(item: JobType) {
    item.selected = !item.selected;
    this.refreshSuggestionsAndEmptyInput();

  }

  refreshSuggestionsAndEmptyInput() {
    if(this.jobTypeIdsInput!=undefined)
      this.jobTypeIdsInput.nativeElement.value = '';
    this.jobTypeIdsCtrl.setValue(null);
  }

  ngOnChanges(changes: SimpleChanges){
    this.allJobTypeIds.forEach(function(i){ i.selected=false })
    this.setSelectedJobTypes(changes.cachedJobTypeIds.currentValue);
  }
}
