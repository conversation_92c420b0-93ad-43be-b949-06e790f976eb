/* eslint-disable @typescript-eslint/no-empty-function */
import { Component, Input, OnInit, forwardRef, EventEmitter, Output } from '@angular/core';
import { ControlValueAccessor, FormControl, NG_VALUE_ACCESSOR, ReactiveFormsModule } from '@angular/forms';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { CommonModule } from '@angular/common';
const materialComponents = [MatInputModule, MatFormFieldModule, CommonModule];
@Component({
	selector: 'lmx-chip-text',
	templateUrl: './chip-text.component.html',
	styleUrls: ['./chip-text.component.scss'],
	providers: [
		{
			provide: NG_VALUE_ACCESSOR,
			useExisting: forwardRef(() => ChipTextComponent),
			multi: true,
		},
	],
	standalone: true,
	imports: [ReactiveFormsModule, ...materialComponents],
})
export class ChipTextComponent implements ControlValueAccessor, OnInit {
	@Input() label!: string;
	value = '';
	inputclass = '';
	inputFC = new FormControl();
	@Output() val = new EventEmitter<string | null>();

	// These properties are required to implement ControlValueAccessor
	onChange = (_: any) => {};
	onTouched = () => {};

	constructor() {}

	ngOnInit(): void {
		if (this.label) {
			const formattedLabel = this.label.replace(/ /g, '');
			this.inputclass = formattedLabel.charAt(0).toLowerCase() + formattedLabel.slice(1);
		} 
		else {
			// Handle the case when this.label is undefined
			this.inputclass = ''; // or some default value
		}
		this.inputFC.valueChanges.subscribe((value) => {
			this.onChange(value);
		});
	}
	// Implement the ControlValueAccessor methods
	writeValue(value: any) {
    this.inputFC.setValue(value);
    this.value=value;
		if (!this.value) {
			this.clearInput();
		}
	}

	registerOnChange(fn: any) {
		this.onChange = fn;
	}

	registerOnTouched(fn: any) {
		this.onTouched = fn;
	}

	private clearInput(): void {
		this.inputFC.reset();
	}
}
