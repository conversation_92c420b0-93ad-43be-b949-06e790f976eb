import { Injectable } from '@angular/core';
import { BehaviorSubject } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class FilterTrayCacheService {
    cacheValue: BehaviorSubject<any> = new BehaviorSubject<any>({});
    cacheValueData = this.cacheValue.asObservable();
    cacheName: BehaviorSubject<any> = new BehaviorSubject<any>({});
    cacheNameData = this.cacheName.asObservable();
    operationList: any;
    exportFormatList: any;
    statusList: any;

    getCacheValueData(obj: any){
      this.cacheValue.next(obj)
    }

    getCacheNameData(obj: any){
      this.cacheValue.next(obj)
  }
}
