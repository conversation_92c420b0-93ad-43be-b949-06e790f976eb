import { BcrReworks } from '../interfaces/BcrReworks';
import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { HttpClient } from '@angular/common/http';
import { ConfigService } from './config.service';
import { Jobs } from '@loadmonitor/shared/interfaces/Jobs';

@Injectable({
  providedIn: 'root',
})
export class BcrReworksService {
  private url!:string;

  constructor(private http: HttpClient, private config: ConfigService) {
    this.url = `${this.config.getApiUrl()}/bcrReworks`;
  }

  // TODO: test and maybe rework
  getAsync(
    countryIds?: number[],
    sectorIds?: number[],
    regionIds?: number[],
    domainProductGroupIds?: number[],
    channelIds?: number[],
    userNames?: string[],
    statusIds?: number[],
    targetBaseChannelIds?: number[],
    jobIds?: number[],
    loadIds?: number[],
    bcrRearrangeIds?: number[],
    feature?: string,
    startDate?: Date,
    endDate?: any
  ): Observable<Jobs<BcrReworks>> {
    const body = {
      countryIds,
      sectorIds,
      regionIds,
      domainProductGroupIds,
      channelIds,
      userNames,
      statusIds,
      targetBaseChannelIds,
      jobIds,
      loadIds,
      bcrRearrangeIds,
      feature,
      startDate,
      endDate,
    };
    return this.http.post<Jobs<BcrReworks>>(this.url, body);
  }

}
