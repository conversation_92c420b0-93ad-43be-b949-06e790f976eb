import { ComponentFixture, TestBed } from '@angular/core/testing';
import { PeriodicityFilterComponent } from './periodicity-filter.component';
import { ReactiveFormsModule } from '@angular/forms';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { PeriodicityService } from '@loadmonitor/shared/services/periodicity.service';
import { PeriodService } from '@loadmonitor/shared/services/period.service';
import { of, Subject } from 'rxjs';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { NO_ERRORS_SCHEMA } from '@angular/core';

describe('PeriodicityFilterComponent', () => {
  let component: PeriodicityFilterComponent;
  let fixture: ComponentFixture<PeriodicityFilterComponent>;
  let resetPeriodicitySubject: Subject<boolean>;

  beforeEach(async () => {
    
    resetPeriodicitySubject = new Subject<boolean>(); // Initialize the subject

    await TestBed.configureTestingModule({
      imports: [
        ReactiveFormsModule,
        MatAutocompleteModule,
        HttpClientTestingModule,
      ],
      declarations: [PeriodicityFilterComponent],
      providers: [],
      schemas: [NO_ERRORS_SCHEMA],
    }).compileComponents();

    fixture = TestBed.createComponent(PeriodicityFilterComponent);
    component = fixture.componentInstance;

    // Set the resetPeriodicity input with the mock Subject
    component.resetPeriodicity = resetPeriodicitySubject;

    fixture.detectChanges();
  });

  // Example test case
  it('should create', () => {
    expect(component).toBeTruthy();
  });

  // Additional tests can go here
});