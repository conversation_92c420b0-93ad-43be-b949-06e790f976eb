import { SharedModule } from '@loadmonitor/shared/shared.module';
import { HttpClientModule } from '@angular/common/http';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ChannelChipAutocompleteComponent } from './channel-chip-autocomplete.component';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';

describe('ChannelChipAutocompleteComponent', () => {
  let component: ChannelChipAutocompleteComponent;
  let fixture: ComponentFixture<ChannelChipAutocompleteComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ChannelChipAutocompleteComponent],
      imports: [HttpClientModule, SharedModule, NoopAnimationsModule],
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(ChannelChipAutocompleteComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
