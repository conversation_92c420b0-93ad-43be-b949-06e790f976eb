<lm-navigation></lm-navigation>

<div class="each-slice-top-headbar" [ngClass]="FilterTrayOpenFlag ? 'filter-tray-open' : 'filter-tray-close'">
	<h3 class="alignleft">Data Order</h3>
	<div class="flexdiv-buttons">
		<button testId="lmx-button"
			gfk-button
			type="primary"
			class="jira-button-margin-right btn-secondary gfk-btn"
			(click)="openJiraTicket()"
			[disabled]="!checkAtleastOneRowChecked()"
		>
			Create JIRA Ticket
		</button>
		<lmx-filter-button showFilterCount="{{ filterCount }}" (isShownFilterTray)="isShownFilterTrayEventHandler($event)"></lmx-filter-button>
	</div>
</div>


<lmx-filter-tray
	(ButtonShowHideEvent)="this.getWidth()"
	(clickApplyButton)="ConfirmApply(true)"
	(clickSaveButton)="openmodal()"
	[IsEnabled]="IsEnabled"
	[IsDisabled]="IsDisabled"
>
<lm-filter-tray-form
	    [sliceName]="'DataOrder'"
		[resetForm]="resetForm"
		(selectedFilters)="getSelectedFilters($event)"
    [isPeriodHidden]="isPeriodFilterHidden"
	>
	</lm-filter-tray-form>
</lmx-filter-tray>

<div class="filter-area" [ngClass]="FilterTrayOpenFlag ? 'filter-tray-open' : 'filter-tray-close'">
	<div class="toggle-button">
		<div>
			<lm-quick-filter
				[selectedfilter]="defaultoption"
				class="p-3.5"
				[events]="eventsSubject.asObservable()"
				*ngIf="IsEnabled"
				[modalshow]="modalshow"
				(saveInQuickFilter)="saveInQuickFilter(cacheObj.isAutoRefresh, $event)"
				(applyquickfilter)="applyfilter($event)"
				(disabledbutton)="disablesave($event)"
			>
			</lm-quick-filter>
			<lm-filter-summary (resetFilters)="filterSummaryChipClear($event)"></lm-filter-summary>
		</div>
		<div class="refreshedTime">
			<lm-refreshed-date-time *ngIf="refreshedDateTime" class="pr-3 d-inline-block"></lm-refreshed-date-time>
			<gfk-toggle-button
				*ngIf="EnableRefresh && IsEnabled"
				[checked]="cacheObj.isAutoRefresh || false"
				title="Auto Refresh"
				class="autoRefresh d-inline-block"
				[disabled]="defaultoption === 'Default'"
				(onChange)="toggleChecked($event)"
			>
			</gfk-toggle-button>
		</div>
	</div>
</div>

<article class="table-grid">
	<div class="mat-elevation-z8 tab-container" [ngClass]="(dataOrderData?.length) ? 'table-height' : ''">
		<table mat-table [dataSource]="dataSource" matSort class="full-width-table js-tbl-data-order-result" multiTemplateDataRows>
			<ng-container matColumnDef="details">
				<th mat-header-cell *matHeaderCellDef></th>
				<td mat-cell *matCellDef="let element">
					<mat-icon (click)="element.isExpanded = element.isExpanded; element.isExpanded = !element.isExpanded" *ngIf="element.isExpanded"
						>keyboard_arrow_down</mat-icon
					>
					<mat-icon (click)="element.isExpanded = !element.isExpanded; element.isExpanded = element.isExpanded" *ngIf="!element.isExpanded"
						>chevron_right</mat-icon
					>
				</td>
			</ng-container>

			<ng-container matColumnDef="menu">
				<th mat-header-cell *matHeaderCellDef class="selectAllPad">
					<mat-checkbox (change)="selectRows($event)" [checked]="this.IsSelectAll" [indeterminate]="this.IsSelectAll"></mat-checkbox>
				</th>
				<td mat-cell *matCellDef="let element">
					<div class="flexdiv">
						<mat-checkbox lass="matcheckbox" [checked]="element.isChecked" (change)="rowToggleSelection($event, element)"></mat-checkbox>
						<lm-detail-menu [configuration]="element.detailMenuConfig"></lm-detail-menu>
					</div>
				</td>
			</ng-container>

			<ng-container matColumnDef="dataOrderId">
				<th mat-header-cell *matHeaderCellDef mat-sort-header>Data Order ID</th>
				<td mat-cell *matCellDef="let element" class="js-result-data-order-id">{{ element.dataOrderId }}</td>
			</ng-container>

			<ng-container matColumnDef="status">
				<th mat-header-cell *matHeaderCellDef mat-sort-header>Status</th>
				<td mat-cell *matCellDef="let element" class="js-result-status">{{ element.status }}</td>
			</ng-container>

			<ng-container matColumnDef="productionProjectId">
				<th mat-header-cell *matHeaderCellDef mat-sort-header>Production Project ID</th>
				<td mat-cell *matCellDef="let element">{{ element.productionProjectId }}</td>
			</ng-container>

			<ng-container matColumnDef="productionProjectName">
				<th mat-header-cell *matHeaderCellDef mat-sort-header>Production Project Name</th>
				<td mat-cell *matCellDef="let element" class="js-result-production-project-name">{{ element.productionProjectName }}</td>
			</ng-container>

			<ng-container matColumnDef="projectType">
				<th mat-header-cell *matHeaderCellDef mat-sort-header>Production Project Type</th>
				<td mat-cell *matCellDef="let element">
					{{ element.projectType }}
				</td>
			</ng-container>

			<ng-container matColumnDef="dataOrderType">
				<th mat-header-cell *matHeaderCellDef mat-sort-header>Data Order Type</th>
				<td mat-cell *matCellDef="let element" class="js-result-data-order-type">{{ element.dataOrderType }}</td>
			</ng-container>

			<ng-container matColumnDef="period">
				<th mat-header-cell *matHeaderCellDef mat-sort-header>Period</th>
				<td mat-cell *matCellDef="let element">{{ element.period }}</td>
			</ng-container>

			<ng-container matColumnDef="deadline">
				<th mat-header-cell *matHeaderCellDef mat-sort-header>Deadline On</th>
				<td mat-cell *matCellDef="let element">
					{{ element.deadline | date: date_with_time }}
				</td>
			</ng-container>

			<ng-container matColumnDef="createdBy">
				<th mat-header-cell *matHeaderCellDef mat-sort-header>Created By</th>
				<td mat-cell *matCellDef="let element" class="js-result-created-by">{{ element.createdBy }}</td>
			</ng-container>

			<ng-container matColumnDef="created">
				<th mat-header-cell *matHeaderCellDef mat-sort-header>Created On</th>
				<td mat-cell *matCellDef="let element" class="js-result-created">
					{{ element.created | date: date_with_time }}
				</td>
			</ng-container>

			<ng-container matColumnDef="sentBy">
				<th mat-header-cell *matHeaderCellDef mat-sort-header>Sent By</th>
				<td mat-cell *matCellDef="let element" class="js-result-sent-by">{{ element.sentBy }}</td>
			</ng-container>

			<ng-container matColumnDef="sent">
				<th mat-header-cell *matHeaderCellDef mat-sort-header>Sent On</th>
				<td mat-cell *matCellDef="let element">
					{{ element.sent | date: date_with_time }}
				</td>
			</ng-container>

			<ng-container matColumnDef="expandedDetail">
				<td mat-cell *matCellDef="let element" [attr.colspan]="displayedColumns.length">
					<div class="expanded-element-detail" [@detailExpand]="element.isExpanded ? 'expanded' : 'collapsed'">
						<lm-floating-panel *ngIf="element.isExpanded"></lm-floating-panel>
					</div>
				</td>
			</ng-container>

			<tr mat-header-row *matHeaderRowDef="displayedColumns; sticky: true" class="columnHeader"></tr>
			<tr
				mat-row
				*matRowDef="let element; columns: displayedColumns"
				[class.selected-row-background]="element.isSelected"
				[class.expanded-row]="expandedElement === element"
				(click)="selectRow(element); expandedElement = expandedElement === element ? null : element"
			></tr>

			<tr mat-row *matRowDef="let row; columns: ['expandedDetail']" class="expanded-detail-row"></tr>
		</table>
	</div>
	<mat-paginator [pageSizeOptions]="[10, 20, 50, 100]" (page)="clickNextButton()" showFirstLastButtons></mat-paginator>
</article>
<lm-version></lm-version>