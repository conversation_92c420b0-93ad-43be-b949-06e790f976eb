<mat-form-field class="chip-list" appearance="outline">
  <mat-label>User </mat-label>
  <mat-chip-list class="chip-list-wrapper" #chipListUser aria-label="User selection">
    <mat-chip *ngFor="let item of selected" [selectable]="true" [removable]="true" (removed)="removeUserChips(item)">
      {{ item.userName }}
      <mat-icon matChipRemove *ngIf="true">cancel</mat-icon>
    </mat-chip>
    <input placeholder="New ..." #userIdsInput [formControl]="userFormCtrl" [matAutocomplete]="autoUser"
      [matChipInputFor]="chipListUser" [matChipInputSeparatorKeyCodes]="separatorKeysCodes"
      class="js-filter-inp-user" />
  </mat-chip-list>
  <mat-autocomplete #autoUser="matAutocomplete" [autoActiveFirstOption]="true">
    <mat-option *ngFor="let item of filteredUsers | async" [value]="item">
      <div (click)="optionClicked($event, item)">
        <mat-checkbox class="option-checkbox" [checked]="item.selected" (change)="toggleSelection(item)"
          (click)="$event.stopPropagation()">
          {{ item.lastName }}, {{ item.firstName }} ({{ item.userName }})
        </mat-checkbox>
      </div>
    </mat-option>
  </mat-autocomplete>
</mat-form-field>