import { enableProdMode } from '@angular/core';
import { platformBrowserDynamic } from '@angular/platform-browser-dynamic';

import { AppModule } from './app/app.module';
import { environment } from './environments/environment';
import { defineCustomElements } from '@gfk/stencil-core/loader';

if (environment.production) {
  enableProdMode();
}

defineCustomElements();

platformBrowserDynamic()
  .bootstrapModule(AppModule)
  .catch((err) => console.error(err));

// syntax sugar for TS and the list of extra things we add to window
declare global {
  interface Window {
    CONFIG: {
      get: (propName: string) => string;
      set: (propName: string, value: any) => void;
    };
  }
}
