import { Component, OnInit } from '@angular/core';
import { ParentPropertyPage } from '@loadmonitor/shared/interfaces/ParentPropertyPage';
import { MatTableDataSource } from '@angular/material/table';
import { SettingsConstants } from '@loadmonitor/shared/settings.constants';
import { FloatingModel, HelperService } from '@loadmonitor/shared/services/helper.service';
import { JobTypes } from '@loadmonitor/shared/jobTypes';

@Component({
	selector: 'lm-floating-panel',
	templateUrl: 'floating-panel.component.html',
	styleUrls: ['floating-panel.component.scss'],
})
export class FloatingPanelComponent implements OnInit {
	readonly date_with_time = SettingsConstants.DATE_FORMAT_WITH_TIME;

	displayedColumns: string[] = [];

	// data sources
	dataSource = new MatTableDataSource<ParentPropertyPage>();
	dataSourceCsv = new MatTableDataSource<ParentPropertyPage>();
	dataSourceShopMaster = new MatTableDataSource<ParentPropertyPage>();
	dataSourceLdGeneral = new MatTableDataSource<ParentPropertyPage>();
	dataSourceMigrationRuleRun = new MatTableDataSource<ParentPropertyPage>();
	dataSourceFeatureMove = new MatTableDataSource<ParentPropertyPage>();
	dataSourcePublishRepublish = new MatTableDataSource<ParentPropertyPage>();
	dataSourceExports = new MatTableDataSource<ParentPropertyPage>();

	// show empty message
	showEmptyMessage = false;
	showEmptyMessageForCsvImport = false;
	showEmptyMessageForShopMasterData = false;
	showEmptyMessageForLdGeneral = false;
	showEmptyMessageForMigrationRuleRun = false;
	showEmptyMessageForPublishRepublish = false;
	showEmptyMessageForExport = false;
	showEmptyMessageForFeatureMove = false;

	// loading
	showLoading = true;

	constructor(private helperService: HelperService) {}

	ngOnInit(): void {
		this.helperService.floatingPanelData$.subscribe((model: FloatingModel) => {
			this.showLoading = true;
			const { data, jobType } = model;
			if (data != undefined && data.length > 0) {
				if (jobType == JobTypes.DataOrder || jobType == JobTypes.DWHRelease) {
					this.displayedColumns = ['message'];
					this.dataSource = new MatTableDataSource<ParentPropertyPage>(data);
				}
				if (jobType == JobTypes.export) {
					console.log(data);
					this.displayedColumns = [
						'deliveryId',
						'deliveryType',
						'emailaddress',
						'ftpHost',
						'ftpRemoteDir',
						'ftpUsername',
						'message',
						'status',
						'countryGroup',
						'instrument',
						'productGroup',
						'client',
					];
					this.dataSourceExports = new MatTableDataSource<ParentPropertyPage>(data);
				} else if (jobType == JobTypes.CsvImport) {
					this.displayedColumns = ['logEntry', 'createdBy', 'created'];
					this.dataSourceCsv = new MatTableDataSource<ParentPropertyPage>(data);
				} else if (jobType == JobTypes.shopMasterData) {
					this.displayedColumns = [
						'dwhUnitId',
						'rowsUpdatedDwhMasterData',
						'rowsUpdatedQcMasterData',
						'rowsUpdatedDwhRearrange',
						'rowsUpdatedQcRearrange',
						'rowsUpdatedDwhComposedFeat',
						'rowsUpdatedQcComposedFeat',
					];
					this.dataSourceShopMaster = new MatTableDataSource<ParentPropertyPage>(data);
				} else if (jobType == JobTypes.migrationRuleRuns) {
					this.displayedColumns = ['messageType', 'message'];
					this.dataSourceMigrationRuleRun = new MatTableDataSource<ParentPropertyPage>(data);
				} else if (jobType == JobTypes.ldGeneral) {
					this.displayedColumns = [
						'loadId',
						'step',
						'status',
						'qcProjectId',
						'baseProject',
						'period',
						'jobId',
						'jeeJobInfo',
						'createdBy',
						'created',
						'started',
						'finished',
						'runtime',
						'message',
					];
					this.dataSourceLdGeneral = new MatTableDataSource<ParentPropertyPage>(data);
				} else if (jobType == JobTypes.featureMove) {
					this.displayedColumns = [
						'featureId',
						'featureName',
						'productGroupId',
						'productGroupName',
						'oldPosition',
						'newPosition',
						'started',
						'finished',
					];
					this.dataSourceFeatureMove = new MatTableDataSource<ParentPropertyPage>(data);
				} else if (jobType == JobTypes.rbPublishUnpublish) {
					this.displayedColumns = [
						'jobId',
						'jeeJobInfo',
						'status',
						'message',
						'operation',
						'serverName',
						'specification',
						'created',
						'started',
						'finished',
					];
					this.dataSourcePublishRepublish = new MatTableDataSource<ParentPropertyPage>(data);
				}
				this.showEmptyMessage = false;
			} else {
				switch (jobType) {
					case JobTypes.DataOrder:
					case JobTypes.DWHRelease:
						this.showEmptyMessage = true;
						break;
					case JobTypes.CsvImport:
						this.showEmptyMessageForCsvImport = true;
						break;
					case JobTypes.export:
						this.showEmptyMessageForExport = true;
						break;
					case JobTypes.shopMasterData:
						this.showEmptyMessageForShopMasterData = true;
						break;
					case JobTypes.migrationRuleRuns:
						this.showEmptyMessageForMigrationRuleRun = true;
						break;
					case JobTypes.ldGeneral:
						this.showEmptyMessageForLdGeneral = true;
						break;
					case JobTypes.featureMove:
						this.showEmptyMessageForFeatureMove = true;
						break;
					case JobTypes.rbPublishUnpublish:
						this.showEmptyMessageForPublishRepublish = true;
						break;
				}
			}
			this.showLoading = false;
		});
	}
}
