@use "@gfk/style/index" as gfk-style;

.header {
  display: flex;
  padding-left: 20px;
  flex-direction: row;
  align-items: flex-start;
  align-content: space-around;
  padding: 15.5px 16px;
  border-bottom: 1px solid #bbb;
  h3 {
    font: gfk-style.$h3;
    line-height: normal;
    color: gfk-style.$grey-500;
    margin: 0;
    width: 100%;
  }
}

.footer {
  display: inline-block;
  width: 100%;
  flex-direction: column;
  background-color: #EBECF0;
  border-top: 1px solid #bbb;
  padding: 16px;
  .gfk-btn {
    cursor: pointer;
    text-align: center;

  }
  button{
    padding: 13px 26px;
  }

}

.sidenav {
  width: 21vw;
  position: fixed;
  z-index: 1000;
  top: 46px;
  bottom: 0px;
  left: 79%;
  background-color: #f0f0f0;
  overflow-x: block;
  overflow-y: block;
  transition: 0.5s;
  margin-top: 2px;
}

.sidenav .closebtn {
  font-size: 24px;
  float: right;
  color: gfk-style.$brand;
  cursor: pointer;
}

div.content {
  overflow-y: auto;
  height: calc(100vh - 170px);
  padding-left: 20px;
  padding-right: 20px;
  padding-top: 20px;
}

.gfk-btn {
  cursor: pointer;
  border: 1px solid;
  text-align: center;
}

.filter-btn {
  background-color: transparent;
  color: #e55a00;
  border-color: #e55a00;
  gap: 8px;
  column-gap: 8px;
  row-gap: 8px;
  padding: 6px 16px;
  height: 36px;
}

.disabled-btn {
  background-color: #dadde3 !important;
  color: #9499a3 !important;
  border-color: #dadde3 !important;
  cursor: not-allowed !important;
}

.dot-flashing {
  position: relative;
  width: 10px;
  height: 10px;
  border-radius: 5px;
  background-color: #1e3a98;
  color: #1e3a98;
  animation: dotFlashing 1s infinite linear alternate;
  animation-delay: .5s;
  margin-left: 25px;
  margin-top: 10px;
  display: inline-block;
}

.dot-flashing::before, .dot-flashing::after {
  content: '';
  display: inline-block;
  position: absolute;
  top: 0;
}

.dot-flashing::before {
  left: -15px;
  width: 10px;
  height: 10px;
  border-radius: 5px;
  background-color: #1e3a98;
  color: #1e3a98;
  animation: dotFlashing 1s infinite alternate;
  animation-delay: 0s;
}

.dot-flashing::after {
  left: 15px;
  width: 10px;
  height: 10px;
  border-radius: 5px;
  background-color: #1e3a98;
  color: #1e3a98;
  animation: dotFlashing 1s infinite alternate;
  animation-delay: 1s;
}

@keyframes dotFlashing {
  0% {
    background-color: #1e3a98;
  }
  50%,
  100% {
    background-color: #fff1e6;
  }
}
