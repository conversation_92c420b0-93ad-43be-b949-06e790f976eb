import { DetailMenuConfiguration } from "@loadmonitor/shared/interfaces/DetailMenuConfiguration";

export interface CsvImports {
    id: number;
    unitType: string;
    status: string;
    fileName: string;
    project: string;
    productGroup: string;
    productGroupId: number;
    period: number;
    createdBy: string;
    created: Date | string;
    finished: Date | string;
    started: Date | string;
    jeeJobId: number;
    message: string;
    runtime: string | null;
    detailMenuConfig?: DetailMenuConfiguration[];
    isSelected?: boolean;
    isExpanded?: boolean;
    isChecked?: boolean;
  }
