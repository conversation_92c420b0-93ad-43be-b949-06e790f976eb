import { Component, Input, Output,EventEmitter  } from '@angular/core';
import { MatSnackBar } from '@angular/material/snack-bar';
import { DetailMenuConfiguration } from '@loadmonitor/shared/interfaces/DetailMenuConfiguration';
import { HelperService } from '@loadmonitor/shared/services/helper.service';
import { DefaultSnackbarComponent } from '../default-snackbar/default-snackbar.component';
import { DWHReleaseService } from '@loadmonitor/shared/services/dwhrelease.service';
import { MatBottomSheet, MatBottomSheetConfig, MatBottomSheetRef } from '@angular/material/bottom-sheet';
import { FloatingPanelComponent } from '@loadmonitor/shared/components/floating-panel/floating-panel.component';
import { LdGeneralService } from '@loadmonitor/shared/services/ld-general.service';
import { DWHMessagePropertyPage } from '@loadmonitor/shared/interfaces/DWHMessagePropertyPage';
import { SettingsConstants } from '@loadmonitor/shared/settings.constants';

@Component({
  selector: 'lm-detail-menu',
  templateUrl: './detail-menu.component.html',
  styleUrls: ['./detail-menu.component.scss']
})
export class DetailMenuComponent {

  @Input() configuration?: DetailMenuConfiguration[];
  @Input() selectedLoadId?: number;
  @Input() isSelected?: boolean;
  @Input() bottomSheetBaseObject?: MatBottomSheetRef;
	readonly date = SettingsConstants.DATE_FORMAT_WITHOUT_TIME;
  readonly dateTime = SettingsConstants.DATE_FORMAT_WITH_TIME;
  @Output() detailsButtonClicked = new EventEmitter<boolean>();
  @Output() bottomSheetObject = new EventEmitter<MatBottomSheetRef>();
  records : DWHMessagePropertyPage[] = [];

  productGroupId = 0;
  firstRun = true;
  modalShow = false;

  constructor(
    private helperService: HelperService,
    private snackBar: MatSnackBar,
    private dwhReleaseService: DWHReleaseService,
    private bottomSheet: MatBottomSheet,
    private ldGeneralService: LdGeneralService,

  ) {
      this.firstRun = true;
    }

  openContext(service: string, parameters: any, isShowDetails: any, isMessageLink:any, isFTXLink: any, link: any) {
    if(isFTXLink)
    {
      window.open(link, '_blank');
    }
    else if(!isShowDetails && isMessageLink)
    { this.ldGeneralService
      .getMessageAsync(parameters.loaddefinitionid)
      .subscribe((result) => {
        this.modalShow = true;
        this.records = result.records;
      });
    }
    else if(!isShowDetails)
    {
      this.helperService.createLink(service, parameters);
      this.notify('Deep link sucessfully copied to clipboard!');
    }
    else
    {
      this.openDetails(service,parameters);
    }
  }

  closeModal(): void {
    this.modalShow = false;
	}

  openDetails(service: string, parameters: any): void {
    this.detailsButtonClicked.emit(true);
    this.openDetailsByLoadId(parameters.loadid);
  }

  openDetailsByLoadId(loadId: any): void {
    this.openFloatingPanel(loadId);
  }


  openFloatingPanel(loadId: any) {
    const config = new MatBottomSheetConfig();
    config.disableClose = true;
    config.hasBackdrop = false;
    config.autoFocus = true;

    this.dwhReleaseService
    .getDetailAsync(loadId)
    .subscribe((result) => {
      this.dwhReleaseService.floatingPanelData$.next(result.records)
    });

    const bottomSheetObj =  this.bottomSheet.open(FloatingPanelComponent, config);
    this.bottomSheetObject.emit(bottomSheetObj);
  }

  private notify(message: string) {
    this.snackBar.openFromComponent(DefaultSnackbarComponent, {
      duration: 10 * 1000,
      data: message,
    });
  }

}
