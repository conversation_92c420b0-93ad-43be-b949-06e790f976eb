import { TestBed, waitForAsync } from '@angular/core/testing';
import { ConfigService } from './config.service';
import { BcrReworksService } from './bcr-reworks.service';
import { HttpClientTestingModule, HttpTestingController, TestRequest } from '@angular/common/http/testing';
import { HttpClient } from '@angular/common/http';
import { BcrReworks } from '../interfaces/BcrReworks';
import { of } from 'rxjs';

describe('BcrReworksService', () => {
  const mockApiUrl = '/api/v1/bcrReworks';
  const mockRequestBody = {
    countryIds: [15, 17],
    startDate: new Date()
  };
  const mockBcrReworkData: BcrReworks[] = [
    {
      loadId: 123,
      bcrRearrangeId: 456,
      status: 'ERROR',
      country: 'DE',
      domainProductGroup: 'Das ist ein Test',
      channel: 'test',
      feature: 'test',
      featureValue: 'test',
      targetBaseChannel: 'test',
      createdBy: 'seschoe',
      created: new Date(),
      started: new Date(),
      finished: new Date(),
      jobId: 123,
      jobPriority: 1,
      addTargetRbbp: false
    }
  ];

  let service: BcrReworksService;
  let configService: ConfigService;
  let httpTestingController: HttpTestingController;
  let httpClient: HttpClient;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [ConfigService]
    });

    service = TestBed.inject(BcrReworksService);
    configService = TestBed.inject(ConfigService);

    httpTestingController = TestBed.inject(HttpTestingController);
    httpClient = TestBed.inject(HttpClient);
  });

  afterEach(() => {
    httpTestingController.verify();
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('getAsync() [Method]', () => {
    it('should exist', () => {
      jest.spyOn(service, 'getAsync');

      expect(service.getAsync).toBeTruthy();
    });

    it('should be called with POST method', waitForAsync(() => {
        jest.spyOn(httpClient, 'post');
        httpClient.post(mockApiUrl, mockBcrReworkData).subscribe();

        const request: TestRequest = httpTestingController.expectOne(mockApiUrl);

        expect(request.request.method).toEqual('POST');

        request.flush(null);
      })
    );

    it('should return bcr rework data', waitForAsync(() => {
      jest.spyOn(httpClient, 'post').mockReturnValue(of(mockBcrReworkData));

      httpClient.post(mockApiUrl, mockRequestBody).subscribe((bcrReworks) => {
        expect(bcrReworks).toEqual(mockBcrReworkData);
      });
    }));
  });

});
