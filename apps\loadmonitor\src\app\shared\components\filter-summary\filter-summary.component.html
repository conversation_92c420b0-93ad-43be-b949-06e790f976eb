<!-- Filtered By: -->
<!-- <gfk-badge class="badge-margine" *ngFor="let filterItem of selectedFilters" [disabled]="false" buttonIconSize="md"
    variant="chip" status="default" label="{{filterItem.name}}" (action)="removeFilter(filterItem)">
    {{filterItem.values.join(', ')}}
</gfk-badge> -->
<p class='clear-all-filters' (click)="removeFilter('ALL')">
  <gfk-icon icon='{{icon}}' size="sm"></gfk-icon>
  <span>Clear all filters </span>
</p>
