import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { TagItem } from '../interfaces/TagItem';
import { ConfigService } from './config.service';

@Injectable({
  providedIn: 'root',
})
export class ProductionProjectTypeService {

  private url!:string;
  
  constructor(private http: HttpClient, private config: ConfigService) {
    this.url = `${this.config.getApiUrl()}/ProductionProjectTypes`;
  }

  getAsync(jobType: string): Observable<TagItem[]> {
    return this.http.get<TagItem[]>(`${this.url}/${jobType}`);
  }

}
