import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { HttpClient } from '@angular/common/http';
import { ConfigService } from './config.service';
import { Status } from '@loadmonitor/shared/interfaces/Status';

@Injectable({
  providedIn: 'root',
})
export class StatusService {
  private url!:string;

  constructor(private http: HttpClient, private config: ConfigService) {
    this.url = `${this.config.getApiUrl()}/status`;
  }

  getAsync(sliceName: string): Observable<Status[]> {
    const finalUrl = `${this.url}/${sliceName}`;
    return this.http.get<Status[]>(finalUrl);

  }
}
