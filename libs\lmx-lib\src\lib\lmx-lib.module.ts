import { ModuleWithProviders, NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FilterButtonComponent } from './components/filter-button/filter-button.component';

import { FilterTrayComponent } from './components/filter-tray/filter-tray.component';
import { NgLibModule } from '@gfk/ng-lib';
import { ChipAutocompleteComponent } from './components/chip-autocomplete/chip-autocomplete.component';
import { ChipTextComponent } from './components/chip-text/chip-text.component';
import { ChipSelectComponent } from './components/chip-select/chip-select.component';
import { DateRangeAutocompleteComponent } from './components/date-range-autocomplete/date-range-autocomplete.component';
import { ChipNumericComponent } from './components/chip-numeric/chip-numeric.component';
import {
	AuthenticationInterceptor,
	AuthenticationService,
	AuthFacade,
	AUTH_INTERCEPTORS,
	AuthenticationTabCommunicationService,
	UserService,
	API_ROUTES,
	AUTH_DI_TOKENS,
} from './services/auth';
import { RedirectWebComponent } from './components/redirect-web/redirect-web.component';


const components = [
	FilterButtonComponent, 
	FilterTrayComponent
];
const standaloneComponents = [
	ChipAutocompleteComponent, 
	ChipTextComponent, 
	DateRangeAutocompleteComponent,
	ChipSelectComponent, 
	ChipNumericComponent, 
	RedirectWebComponent
];
const services = [
	AuthenticationService,
	UserService,
	AuthenticationInterceptor,
	AuthenticationTabCommunicationService,
	AuthFacade,
	...AUTH_INTERCEPTORS,
];

@NgModule({
	imports: [CommonModule, NgLibModule, ...standaloneComponents],
	declarations: [...components],
	exports: [...components, ...standaloneComponents]
})
export class LmxLibModule {
	static forRoot({ API_ROUTES: ROUTES_DI_VALUE }: AUTH_DI_TOKENS): ModuleWithProviders<LmxLibModule> {
		return {
			ngModule: LmxLibModule,
			providers: [
				{ provide: API_ROUTES, useValue: { BFF: ROUTES_DI_VALUE.BFF } },
				...services,
			],
		};
	}

}
