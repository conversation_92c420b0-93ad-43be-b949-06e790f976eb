import { HttpClient } from "@angular/common/http";
import { Injectable } from "@angular/core";
import { Observable } from "rxjs";
import { FeatureMove } from "@loadmonitor/shared/interfaces/FeatureMove";
import { Jobs } from '@loadmonitor/shared/interfaces/Jobs';
import { ConfigService } from './config.service';
import { FeatureMovePropertyPage } from "@loadmonitor/shared/interfaces/FeatureMovePropertyPage";

@Injectable({
  providedIn: 'root',
})

export class FeatureMoveService {
  private url: string;

  constructor(
    private http: HttpClient,
    private config: ConfigService
  ){
    this.url = `${this.config.getApiUrl()}/FeatureMoves`;
  }

  getAsync(
    userNames?: string[],
    statusIds?: number[],
    loadIds?: number[],
    jobIds?: number[],
    startDate?: Date,
    endDate?: any
  ): Observable<Jobs<FeatureMove>> {
    const body = {
      userNames,
      statusIds,
      loadIds,
      jobIds,
      startDate,
      endDate
    };

    return this.http.post<Jobs<FeatureMove>>(this.url, body);
  }

  getDetailAsync(loadId: number): Observable<FeatureMovePropertyPage[]>{
    return this.http.get<FeatureMovePropertyPage[]>(this.url + '/' + loadId);
  }
}
