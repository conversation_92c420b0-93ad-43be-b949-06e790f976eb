import { Injectable } from '@angular/core';
import { Observable} from 'rxjs';
import { HttpClient } from '@angular/common/http';
import { ConfigService } from './config.service';
import { BaseChannel } from '@loadmonitor/shared/interfaces/BaseChannel';
import {shareReplay} from 'rxjs/operators'

@Injectable({
  providedIn: 'root',
})
export class BasechannelService {
  private url!:string;

  private cachedBaseChannel! : Observable<BaseChannel[]>;
  constructor(private http: HttpClient, private config: ConfigService) {
    this.url = `${this.config.getApiUrl()}/BaseChannels`;
  }

  getAsync(): Observable<BaseChannel[]> {
    if(!this.cachedBaseChannel)
    {
      this.cachedBaseChannel = this.http
        .get<BaseChannel[]>(this.url )
        .pipe(shareReplay(1));
    }
    return this.cachedBaseChannel;
  }
}
