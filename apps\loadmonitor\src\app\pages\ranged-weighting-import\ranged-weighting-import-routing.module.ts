import { RangedWeightingImportComponent } from '@loadmonitor/pages/ranged-weighting-import/ranged-weighting-import.component';
import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { MatTableModule } from '@angular/material/table';

const routes: Routes = [
  {
    path: '',
    component: RangedWeightingImportComponent,
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes), MatTableModule],
  exports: [RouterModule],
})
export class RangedWeightingImportRoutingModule {}
