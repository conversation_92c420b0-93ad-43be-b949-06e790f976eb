<gfk-modal [triggerModal]="modalshow" modalTitle="Please add a name to your filter selection" width="500px"
   cancelTitle="Cancel" confirmTitle="Save and Apply" [cancelDisabled]="false" [confirmDisabled]="Error"
  (onAction)="closeModal($event); modalshow=false">

  <mat-form-field appearance="outline" class="widthinput min-height-quickdialog">

    <mat-label>Name</mat-label>

    <input matInput [(ngModel)]="FilterName" (keypress)="keyPressAlphaNumbers($event)" (input)="UpdateButtons()" />

  </mat-form-field>

</gfk-modal>


<mat-form-field appearance="standard">
  <mat-label>{{ defaultValue[0] }}</mat-label>
  <mat-select (selectionChange)="applyfilter($event)" [(value)]="selectedfilter" #mySelect>
    <mat-select-trigger>
      {{ selectedfilter }}
    </mat-select-trigger>
    <mat-option value="Default">
      <div style="display: flex; justify-content: space-between">
        <span>Default</span>
        <span></span>
      </div>
    </mat-option>
    <mat-option *ngFor="let filters of currentfilter" value="{{ filters.filter }}">
      <div style="display: flex; justify-content: space-between">
        <span>{{ filters.filter }}</span>
        <span></span>
        <span class="deleteYear" (click)="delete(filters.filter)">
          <mat-icon>clear</mat-icon>
        </span>
      </div>
    </mat-option>
  </mat-select>
</mat-form-field>

<!-- <gfk-simple-dropdown [options]="currentfilter" [defaultValue]="defaultValue[0]" (selectionEvent)="applyfilter($event)" *ngIf="currentfilter.length>0"></gfk-simple-dropdown> -->

