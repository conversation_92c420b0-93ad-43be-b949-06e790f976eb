{"$schema": "./node_modules/nx/schemas/nx-schema.json", "cli": {"packageManager": "yarn"}, "defaultProject": "loadmonitor", "generators": {"@nx/angular:application": {"style": "scss", "linter": "eslint", "unitTestRunner": "jest", "e2eTestRunner": "none"}, "@nx/angular:library": {"linter": "eslint", "unitTestRunner": "jest"}, "@nx/angular:component": {"style": "scss"}}, "namedInputs": {"default": ["{projectRoot}/**/*", "sharedGlobals"], "sharedGlobals": [], "production": ["default", "!{projectRoot}/**/?(*.)+(spec|test).[jt]s?(x)?(.snap)", "!{projectRoot}/tsconfig.spec.json", "!{projectRoot}/jest.config.[jt]s", "!{projectRoot}/.eslintrc.json", "!{projectRoot}/src/test-setup.[jt]s", "!{projectRoot}/test-setup.[jt]s"]}, "targetDefaults": {"build": {"inputs": ["production", "^production"], "cache": true}, "@nx/jest:jest": {"cache": true, "inputs": ["default", "^production", "{workspaceRoot}/jest.preset.js"], "options": {"passWithNoTests": true}, "configurations": {"ci": {"ci": true, "codeCoverage": true}}}, "@nx/eslint:lint": {"inputs": ["default", "{workspaceRoot}/.eslintrc.json"], "cache": true}}, "useInferencePlugins": false, "defaultBase": "main", "useLegacyCache": true}