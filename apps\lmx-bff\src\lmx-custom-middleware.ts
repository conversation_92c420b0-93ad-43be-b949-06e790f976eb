// import { log } from '@gfk/bff';
import { Application } from 'express';

export const lmxCustomGfkMiddleware = (app: Application) => {
  app.use('/', async (req: any, res, next) => {
    //Do something for every request
    // console.log(req.method, ':', req.url);

    // log.debug({ metaData: { class: 'lmxCustomGfkMiddleware', method: 'request' }, message: req.method });

    next();
    
  });

};
