import { HttpClient } from '@angular/common/http';
import { HttpClientTestingModule, HttpTestingController, TestRequest } from '@angular/common/http/testing';
import { TestBed, waitForAsync } from '@angular/core/testing';
import { ConfigService } from './config.service';
import { FeatureMoveService } from './feature-move.service';
import { FeatureMove } from '../interfaces/FeatureMove';
import { of } from 'rxjs';

describe('FeatureMoveService', () => {
  const mockApiUrl = 'api/v1/FeatureMoves'; // version is missing! important?
  const mockFeatureMoves: FeatureMove[] = [
    {
      moveId: 112623980,
      status: 'DONE',
      productGroupFeatureName: 'MAINFRAME ENV.(11865)',
      jobId: 222267467,
      createdBy: "mkshel",
      created: new Date(),
      started: new Date(),
      finished: new Date(),
      runtime: null,
      message: 'this is a test'
    }
  ];
  const mockRequestBody =  {
    countryIds: [15,17],
    startDate: new Date()
  };
  const mockLoadDefinitionId = 123;

  let service: FeatureMoveService;
  let configService: ConfigService;
  let httpTestingController: HttpTestingController;
  let httpClient: HttpClient;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [ConfigService]
    });

    service = TestBed.inject(FeatureMoveService);
    configService = TestBed.inject(ConfigService);

    httpTestingController = TestBed.inject(HttpTestingController);
    httpClient = TestBed.inject(HttpClient);
  });

  afterEach(() => {
    httpTestingController.verify();
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('getAsync() [Method]', () => {
    it('should exist', () => {
      jest.spyOn(service, 'getAsync');

      expect(service.getAsync).toBeTruthy();
    });

    it('should be called with POST method', waitForAsync(() => {
      jest.spyOn(httpClient, 'post');

      httpClient.post(mockApiUrl, mockRequestBody).subscribe();

      const testRequest: TestRequest = httpTestingController.expectOne(mockApiUrl);

      expect(testRequest.request.method).toEqual('POST');

      testRequest.flush(null);
    }));

    it('should return feature move data', waitForAsync(() => {
      jest.spyOn(httpClient, 'post').mockReturnValue(of(mockFeatureMoves));

      httpClient.post(mockApiUrl, mockRequestBody).subscribe((featureMoveData) => {
        expect(featureMoveData).toEqual(mockFeatureMoves);
      });
    }));

  });

  describe('getDetailAsync() [Method]', () => {
    it('should exist', () => {
      jest.spyOn(service, 'getDetailAsync');

      expect(service.getDetailAsync).toBeTruthy();
    });

    it('should be called with GET method', waitForAsync(() => {
      jest.spyOn(httpClient, 'get');

      httpClient.get(mockApiUrl + '/' + mockLoadDefinitionId).subscribe();

      const testRequest: TestRequest = httpTestingController.expectOne(mockApiUrl + '/' + mockLoadDefinitionId);

      expect(testRequest.request.method).toEqual('GET');

      testRequest.flush(null);
    }));
  });
});
