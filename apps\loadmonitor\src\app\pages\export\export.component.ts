import { Component,  OnInit, ViewChild, AfterContentInit, ChangeDetectorRef, AfterViewInit, Input, HostListener, OnDestroy } from '@angular/core';
import { COMMA, ENTER } from '@angular/cdk/keycodes';
import { MatTable, MatTableDataSource } from '@angular/material/table';
import { MatSort } from '@angular/material/sort';
import { MatPaginator } from '@angular/material/paginator';
import { FormBuilder, FormGroup } from '@angular/forms';
import { SettingsConstants } from '@loadmonitor/shared/settings.constants';
import { MatSnackBar } from '@angular/material/snack-bar';
import { DefaultSnackbarComponent } from '@loadmonitor/shared/components/default-snackbar/default-snackbar.component';
import { Exports } from '@loadmonitor/shared/interfaces/Export';
import { JobTypes } from '@loadmonitor/shared/jobTypes';
import { BehaviorSubject,  Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { ExportService } from '@loadmonitor/shared/services/export.service';
import { ExportFormatService } from '@loadmonitor/shared/services/export-format.service';
import { HelperService } from '@loadmonitor/shared/services/helper.service';
import { PropertyPage } from '@loadmonitor/shared/interfaces/PropertyPage';
import { animate, state, style, transition, trigger } from '@angular/animations';
import { ExportCache } from '@loadmonitor/shared/interfaces/caching/ExportCache';
import { MatDialog } from '@angular/material/dialog';
import { CreateTicketDialogComponent } from '@loadmonitor/shared/components/create-ticket-dialog/create-ticket-dialog.component';
import { SelectionModel } from '@angular/cdk/collections';
import { exportJiraProperties } from '@loadmonitor/shared/interfaces/ExportJiraProperties';
import { ToggleButtonComponent } from '@gfk/ng-lib';
import { environment } from 'apps/loadmonitor/src/environments/environment';
import { Filter, FilterRecords, FilterService, FilterTrayComponent } from '@dwh/lmx-lib';
import { DatePipe } from '@angular/common';
import { CountryService } from '@loadmonitor/shared/services/country.service';
import { UserService } from '@loadmonitor/shared/services/user.service';
import { CountrySelectService } from '@loadmonitor/shared/services/country-select.service';
import { StatusService } from '@loadmonitor/shared/services/status.service';
import { Status } from '@loadmonitor/shared/interfaces/Status';
import * as moment from 'moment';
import { FilterTrayCacheService } from '@loadmonitor/shared/services/filter-tray-cache.service';

@Component({
	selector: 'lm-export',
	templateUrl: './export.component.html',
	styleUrls: ['./export.component.scss'],
	animations: [
		trigger('detailExpand', [
			state('collapsed', style({ height: '0px', minHeight: '0' })),
			state('expanded', style({ height: '*' })),
			transition('expanded <=> collapsed', animate('225ms cubic-bezier(0.4, 0.0, 0.2, 1)')),
		]),
	],
})
export class ExportComponent implements OnInit, AfterContentInit, AfterViewInit, OnDestroy{

	@Input() icon = 'delete';

	@ViewChild(FilterTrayComponent) filterTrayComponent: FilterTrayComponent | undefined;
	@ViewChild(ToggleButtonComponent) togglebuttonComponent: ToggleButtonComponent | undefined;

	@ViewChild(MatSort)
	sort: MatSort = new MatSort();

	@ViewChild(MatTable)
	table!: MatTable<Exports>;

	@ViewChild(MatPaginator)
	paginator!: MatPaginator;

	//Mat-Expansion-Panel
	panelOpenState = false;

	readonly separatorKeysCodes = [ENTER, COMMA] as const;

	//Mat-Table
	dataSource = new MatTableDataSource<Exports>();
	selection = new SelectionModel<Exports>(true, []);
	IsSelectAll = false;
	displayedColumns: string[] = [
		'details',
		'menu',
		'exportId',
		'warningLevel',
		'exportName',
		'reportProjectName',
		'reportProjectId',
		'reportGroupId',
		'deliveryId',
		'deliveryTemplateId',
		'toolFormat',
		'status',
		'message',
		'expectedExportCount',
		'period',
		'jobId',
		'jobPriority',
		'favoriteId',
		'favoriteName',
		'scope',
		'profileId',
		'profileName',
		'createdBy',
		'created',
		'started',
		'finished',
		'estimatedRuntime',
		'runtime',
	];

	// Progress Spinner visibility
	isLoading = false;
	public ExportForm!: FormGroup;
	public cacheObj: ExportCache = {} as ExportCache;
	cacheName = 'cache::ExportCache';
	// Mat-Chips - Export Format

	// Property Pages
	expandedElement!: PropertyPage | null;
	dwhReleaseDetail!: PropertyPage[];

	jobType: JobTypes = JobTypes.export;
	selectedRow: any;

	readonly date_with_time = SettingsConstants.DATE_FORMAT_WITH_TIME;

	/* *********************
	 * Components variables
	 ********************* */
	eventsSubject: Subject<void> = new Subject<void>();
	IsDisabled = false;
	environments = environment.environments;
	IsEnabled = false;
	FilterTrayOpenFlag = true;
	currentfilter = JSON.parse(localStorage.getItem('QuickFilter') || '[]').filter(
		(x) => x.user == localStorage.getItem('username') && x.slice == window.location.href.split('/')[3]
	);
	modalshow = false;
	filterCount = '';
	toggleInterval!: any;
	timer = 60;
	EnableRefresh = true;
	cacheInterval!: any;
	defaultoption = localStorage.getItem(this.cacheName + '-LastFilter') || 'Default';
	defaultfilter = JSON.parse(localStorage.getItem(this.cacheName + '-DefaultFilter') || '[]');
	refreshedDateTime!: boolean;


	selectedFilters: any;
	cacheValue: BehaviorSubject<any> = new BehaviorSubject<any>({});
	resetForm: Subject<boolean> = new Subject();
	exportData!: any[];

  @HostListener('document:visibilitychange', ['$event'])
	appVisibility() {
		if (document.hidden) {
			if (this.cacheInterval) {
				clearInterval(this.cacheInterval);
			}
			if (this.toggleInterval) {
				clearInterval(this.toggleInterval);
			}
		} else {
			if (this.cacheObj.isAutoRefresh === true) {
				this.cacheInterval = setInterval(() => {
					if (this.defaultoption != 'Default') {
						this.refresh();
					}
				}, this.timer * 1000);
			} else {
				if (this.cacheInterval !== undefined) clearInterval(this.cacheInterval);
				if (this.toggleInterval !== undefined) clearInterval(this.toggleInterval);
			}
		}
	}
	private destroy$ = new Subject<void>();

	constructor(
		private exportService: ExportService,
		private exportFormatService: ExportFormatService,
		private snackBar: MatSnackBar,
		private formBuilder: FormBuilder,
		private helperService: HelperService,
		private cdRef: ChangeDetectorRef,
		public dialog: MatDialog,
		public filterService: FilterService,
		public datepipe: DatePipe,
		private filterTrayCacheService: FilterTrayCacheService
	) {

  }

	private updateFC(): void {
		if(this.cacheObj){
			this.filterTrayCacheService.getCacheValueData(this.cacheObj);
		}
  	}

	getSpecialStatusesForExport(exportId: number, sendingId: number): Status[] {
		const arrStatus: Status[] = new Array<Status>();
		const sentObj = { description: "Sent", processStageId: 1 };
		const sendingObj = { description: "Sending", processStageId: 2 };
		const errorObj = { description: "Error", processStageId: 4 };
		const exportedText = "Exported + ";

		// Sent
		arrStatus.push(<Status>({ description: `${exportedText}${sentObj.description}`, id: exportId, selected: false, processStageId: sentObj.processStageId }));
		// Sending
		arrStatus.push(<Status>({ description: `${exportedText}${sendingObj.description}`, id: exportId, selected: false, processStageId: sendingObj.processStageId }));
		//Error
		arrStatus.push(<Status>({ description: `${exportedText}${errorObj.description}`, id: exportId, selected: false, processStageId: errorObj.processStageId }));

		// Sending
		arrStatus.push(<Status>({ description: `Sending + ${sendingObj.description}`, id: sendingId, selected: false, processStageId: sendingObj.processStageId }));

		return arrStatus;
	}

	public setSelectedStatusIds(statusIds: number[], allStatusIds: Status[]): void {
		statusIds?.forEach((statusId: number) => {
		  const found = allStatusIds.find((status: Status) => status.id === statusId);
		  if (found) {
			found.selected = true;
		  } else {
			allStatusIds.push({
			  id: statusId, description: 'Loading...', selected: true,
			  processStageId: 0
			})
		  }
		});
	}


	ngOnInit(): void {
		if (this.environments.filter((x) => x.location == window.location.origin)[0]) {
			this.IsEnabled = this.environments.filter((x) => x.location == window.location.origin)[0].enabled;
		} else {
			this.IsEnabled = false;
		}
		this.ExportForm = this.formBuilder.group({
			isAutoRefresh: this.togglebuttonComponent?.checked,
		});
		this.updateFC();
	}

	ngAfterContentInit() {
		this.afterChildComponentsLoaded();
		this.CreateFilterSummaryChips();
		this.SetFiltersCount();
		this.updateFC();
		if (this.cacheObj.isAutoRefresh === true) {
			if (this.cacheInterval === undefined) {
				this.cacheInterval = window.setInterval(() => {
					if (this.defaultoption != 'Default') {
						this.refresh();
					}
				}, this.timer * 1000);
			}
		} else {
			if (this.cacheInterval !== undefined) clearInterval(this.cacheInterval);
			if (this.toggleInterval !== undefined) clearInterval(this.toggleInterval);
		}
	}

	ngAfterViewInit() {
		this.cdRef.detectChanges();
	}

	afterChildComponentsLoaded() {
		this.cacheObj = JSON.parse(localStorage.getItem(this.cacheName) || '{}');
	}
	saveInCache(autoRefreshStatus, AutoToggle) {
		this.setFormValue(autoRefreshStatus);
		localStorage.setItem(this.cacheName, JSON.stringify(this.selectedFilters));
		this.cacheObj = JSON.parse(localStorage.getItem(this.cacheName) || '{}');
		if(this.cacheObj.exportName == null){
			this.cacheObj.exportName = '';
		}
		if (!AutoToggle) {
			this.defaultfilter = [];
			this.defaultfilter.push({
				data: this.selectedFilters,
				slice: window.location.href.split('/')[3],
				user: localStorage.getItem('username'),
			});
			localStorage.setItem(this.cacheName + '-DefaultFilter', JSON.stringify(this.defaultfilter));
			let nofilter = 0;
			let selectedfilter = '';
			this.currentfilter.forEach((i) => {
				// if (i.filter == this.defaultoption) {
				if(JSON.stringify(i.data) == JSON.stringify(this.cacheObj)){
					nofilter++;
					selectedfilter = i.filter;
				}
			});
			if (nofilter == 0) {
				this.defaultoption = 'Default';
			} else {
				this.defaultoption = selectedfilter;
			}

			localStorage.setItem(this.cacheName + '-LastFilter', this.defaultoption);
		}
		this.updateFC();
	}
	selectRow(row: Exports) {
		this.dwhReleaseDetail = [];
		row.isSelected = true;

		this.dataSource.data.forEach((element) => {
			if (row.exportId != element.exportId) {
				element.isExpanded = false;
				element.isSelected = false;
			}
		});

		this.selectedRow = row;
		this.exportService.getDetailAsync(row.exportId, row.toolFormat).subscribe((result) => {
			this.helperService.floatingPanelData$.next({ data: result, jobType: JobTypes.export });
		});
	}

	refresh(): void {
		this.refreshedDateTime = false;
		this.filterTrayComponent?.showSpinner(true);
		this.isLoading = true;
		this.destroy$.next();
		const selectedExportFormat: any = [];
		this.filterTrayCacheService.exportFormatList.forEach((ef: any) => {
			this.selectedFilters.exportFormatIds.forEach((selectedEF: any) => {
				if(ef.value == selectedEF){
					selectedExportFormat.push(ef.label)
				}
			})
		})
		this.exportService
			.getAsync(
				this.selectedFilters.countries?.map((country) => country),
        		this.selectedFilters.users?.map((users) => users.id),
				this.selectedFilters.status?.map((status) => status),
        		this.selectedFilters.exportIds,
				this.selectedFilters.exportName,
        		this.selectedFilters.reportingProjectIds,
				this.selectedFilters.reportingProjectName,
        		this.selectedFilters.jobIds,
				selectedExportFormat,
				this.selectedFilters.statusProcessStageId,
        		this.selectedFilters.startDate ? this.selectedFilters.startDate : null,
				this.selectedFilters.endDate ? moment(this.selectedFilters.endDate).format().split('T')[0] + 'T23:59:59.999Z' : null,
				this.selectedFilters.reportingGroupIds,
				this.selectedFilters.cdmDeliveryIds
			)
			.pipe(
				takeUntil(this.destroy$)
			)
			.subscribe((result) => {
				this.refreshedDateTime = true;
				if (result.moreRecordsAvailable) {
					this.notify('More than 1000 results exist!');
				}

				if (result.count === 0) {
					this.notify('No result found!');
					this.exportData = [];
				}
				this.exportData = result.records;
				// add elements to detail menu
				result.records.forEach((element) => {
					if (element.toolFormat !== 'GENERIC') {
						element.detailMenuConfig = [
							{
								menuText: 'Resend',
								menuIcon: 'open_in_new',
								service: 'showexportproperties',
								params: {
									exportid: element.exportId,
									toolformat: element.toolFormat,
								},
							},
						];
					}
				});

				this.isLoading = false;
				// set runtime column
				result.records.forEach((element) => {
					element.runtime = this.helperService.calculateDuration(element.started, element.finished, true);
				});

				this.dataSource = new MatTableDataSource<Exports>(result.records);
				this.dataSource.sort = this.sort;
				this.paginator.pageIndex = 0;
				this.dataSource.paginator = this.paginator;
				this.table.dataSource = this.dataSource;
				this.filterTrayComponent?.showSpinner(false);
			},
			(error) => {
				if (error.status === 504) {
					this.notify('Gateway Timeout. The server took too long to respond. Please try again later.');
				} 
				else {
					this.notify(`An error occurred: ${error.message}`);
				}
				this.isLoading = false;
				this.filterTrayComponent?.showSpinner(false);
			}
		);
		this.SetFiltersCount();
	}


	private notify(message: string) {
		this.snackBar.openFromComponent(DefaultSnackbarComponent, {
			duration: 10 * 1000,
			data: message,
		});
	}
	clickNextButton() {
		this.selectedRow.isSelected = false;
	}

	rowToggleSelection(ev, item) {
		item.isChecked = ev.checked;
	}

	checkAtleastOneRowChecked() {
		return this.dataSource.data.filter((i) => i.isChecked).length > 0;
	}
	openJiraTicket(): void {
		const checkedList = this.dataSource.data.filter((i) => i.isChecked);
		const exportJiraProperties: Array<exportJiraProperties> = new Array<exportJiraProperties>();

		checkedList.map(function (value) {
			exportJiraProperties.push({
				exportId: value.exportId,
				jobId: value.jobId ? value.jobId : 0,
				status: value.status,
			});
		});

		const dialogRef = this.dialog.open(CreateTicketDialogComponent, {
			width: '1000px',
			height: '487px',
			data: { exportJiraProperties, jobType: this.jobType, title: 'Export' },
		});

		dialogRef.afterClosed().subscribe((resSubmit) => {
			if (resSubmit === true) {
				this.dataSource.data.forEach((i) => (i.isChecked = false));
				this.selection.clear();
				this.IsSelectAll = false;
			}
		});
	}

	selectRows(selectrow) {
		this.IsSelectAll = !this.IsSelectAll;
		let endIndex: number;
		const page = this.dataSource.paginator !== null ? this.dataSource.paginator.pageSize : 0;
		const pageindex = this.dataSource.paginator !== null ? this.dataSource.paginator.pageIndex : 0;

		if (!selectrow.checked) {
			this.selection.clear();
			if (this.dataSource.data.length > (pageindex + 1) * page) {
				endIndex = (pageindex + 1) * page;
			} else {
				endIndex = this.dataSource.data.length;
			}

			for (let index = pageindex * page; index < endIndex; index++) {
				const row = this.dataSource.data[index];
				this.selection.deselect(row);
				this.dataSource.data[index].isChecked = false;
			}
		} else {
			if (this.dataSource.data.length > (pageindex + 1) * page) {
				endIndex = (pageindex + 1) * page;
			} else {
				endIndex = this.dataSource.data.length;
			}
			for (let index = pageindex * page; index < endIndex; index++) {
				const row = this.dataSource.sortData(this.dataSource.data, this.sort)[index];
				this.selection.select(row);
				this.dataSource.sortData(this.dataSource.data, this.sort)[index].isChecked = true;
			}
		}
	}

	isSelectedPage() {
		const numSelected = this.selection.selected.length;
		const page = this.dataSource.paginator !== null ? this.dataSource.paginator.pageSize : 0;
		const pageindex = this.dataSource.paginator !== null ? this.dataSource.paginator.pageIndex : 0;
		let endIndex: number;
		if (this.dataSource.data.length > (pageindex + 1) * page) {
			endIndex = (pageindex + 1) * page;
		} else {
			endIndex = this.dataSource.data.length - pageindex * page;
		}

		return numSelected === endIndex;
	}

	//Quick Filter Methods//
	openmodal() {
		this.eventsSubject.next();
	}

	disablesave(status) {
		this.IsDisabled = status;
	}

	setFormValue(autoRefreshStatus) {
		this.ExportForm.setValue({
			isAutoRefresh: autoRefreshStatus === undefined || autoRefreshStatus === false ? false : true,
		});
	}

	saveInQuickFilter(autoRefreshStatus, filterName) {
		this.setFormValue(autoRefreshStatus);
		this.currentfilter = JSON.parse(localStorage.getItem('QuickFilter') || '[]');
		this.currentfilter.push({
			data: this.selectedFilters,
			filter: filterName,
			slice: window.location.href.split('/')[3],
			user: localStorage.getItem('username'),
		});
		this.defaultoption = filterName;
		const oldcache = this.cacheObj;
		const olddefault = localStorage.getItem(this.cacheName + '-DefaultFilter');
		this.ConfirmApply(autoRefreshStatus);
		this.cacheObj = oldcache;
		localStorage.setItem(this.cacheName + '-DefaultFilter', olddefault || '[]');
		localStorage.setItem('QuickFilter', JSON.stringify(this.currentfilter));
		localStorage.setItem(this.cacheName + '-LastFilter', filterName);
	}

	applyfilter(selectedvalue) {
    this.resetForm.next(true);
		const oldautotoggle = this.cacheObj.isAutoRefresh;
		selectedvalue = selectedvalue.value ? selectedvalue.value : selectedvalue;
		this.defaultoption = selectedvalue;
		localStorage.setItem(this.cacheName + '-LastFilter', this.defaultoption || 'Default');
		if (selectedvalue == 'Default') {
			if (
				JSON.parse(localStorage.getItem(this.cacheName + '-DefaultFilter') || '[]').filter(
					(x) => x.user == localStorage.getItem('username') && x.slice == window.location.href.split('/')[3]
				)[0]
			) {
				this.cacheObj = JSON.parse(localStorage.getItem(this.cacheName + '-DefaultFilter') || '[]').filter(
					(x) => x.user == localStorage.getItem('username') && x.slice == window.location.href.split('/')[3]
				)[0]['data'];
			}

			localStorage.setItem(this.cacheName, JSON.stringify(this.cacheObj));
		} else {
			this.cacheObj = JSON.parse(localStorage.getItem('QuickFilter') || '[]')
				.filter((x) => x.user == localStorage.getItem('username') && x.slice == window.location.href.split('/')[3])
				.filter((x) => x.filter == selectedvalue)[0]['data'];
			localStorage.setItem(this.cacheName, JSON.stringify(this.cacheObj));
		}
		this.cacheObj.isAutoRefresh = oldautotoggle;
		this.CreateFilterSummaryChips();
		this.cdRef.detectChanges();

    this.selectedFilters = this.cacheObj;
    if (this.cacheObj.selectedDays == '3') {
			const date = new Date();
			this.cacheObj.startDate = new Date(Date.UTC(date.getFullYear(), date.getMonth(), date.getDate() - 2, 0, 0, 0, 0));
			this.cacheObj.endDate = new Date(Date.UTC(date.getFullYear(), date.getMonth(), date.getDate(), 0, 0, 0, 0));
		} else if (this.cacheObj.selectedDays == '2') {
			const date = new Date();
			this.cacheObj.startDate = new Date(Date.UTC(date.getFullYear(), date.getMonth(), date.getDate(), 0, 0, 0, 0));
			this.cacheObj.endDate = new Date(Date.UTC(date.getFullYear(), date.getMonth(), date.getDate(), 0, 0, 0, 0));
		}
		setTimeout(() => {
			this.updateFC();
			this.refresh();
		}, 200);
		localStorage.setItem(this.cacheName, JSON.stringify(this.cacheObj));
	}

	CreateFilterSummaryChips() {
		if (this.cacheObj && Object.keys(this.cacheObj).length) {
			this.usersFilterSummary();
			this.statusFilterSummary();
			this.countryFilterSummary();
			this.exportIDFilterSummary();
			this.exportNameFilterSummary();
			this.exportFormatFilterSummary();
			this.reportIDFilterSummary();
			this.reportNameFilterSummary();
			this.jeeJobIDFilterSummary();
			this.reportGroupIdFilterSummary();
			this.cdmDeliveryIdsFilterSummary();
			this.datesFilterSummary(this.cacheObj.startDate, this.cacheObj.endDate);
		}
	}

	ConfirmApply(autoRefreshStatus) {
		this.saveInCache(autoRefreshStatus, false);
		this.CreateFilterSummaryChips();
    this.getSelectedFilters(this.cacheObj);

		let count;
		this.filterService.filtersSelected$.subscribe((selectedFilters: FilterRecords) => {
			count = Number(Object.keys(selectedFilters).length);
		});

		if (count > 0) {
			this.refresh();
		} else {
			if (confirm('Loading results without any filters selected will take some time to load. Are you sure you want to proceed?')) {
				this.refresh();
			}
		}
	}

	public isShownFilterTrayEventHandler($event: boolean) {
		this.filterTrayComponent?.toggleShowTray($event);
		this.FilterTrayOpenFlag = $event;
	}

	getWidth() {
		this.FilterTrayOpenFlag = !this.FilterTrayOpenFlag;
		return '100%';
	}

	filterSummaryChipClear(filteredItems) {
		if (filteredItems == 'ALL') {
			if (confirm('This will remove all of your selected filters and could not be reversed. Are you sure you want to do that?')) {
        		this.resetForm.next(true);
				this.filterTrayCacheService.getCacheNameData(this.cacheName);
			}
		}
    this.defaultoption = "Default";
		this.saveInCache(this.cacheObj.isAutoRefresh, false);
		this.CreateFilterSummaryChips();
		this.SetFiltersCount();
	}

	toggleChecked(check: any) {
		if (check === true) {
			this.saveInCache(check, true);
			this.toggleInterval = setInterval(() => {
				if (this.defaultoption != 'Default') {
					this.refresh();
				}
			}, this.timer * 1000);
		} else {
			if (this.toggleInterval !== undefined) clearInterval(this.toggleInterval);
			if (this.cacheInterval !== undefined) clearInterval(this.cacheInterval);
			this.saveInCache(check, true);
		}
	}

	SetFiltersCount() {
		let count;
		this.filterService.filtersSelected$.subscribe((selectedFilters: FilterRecords) => {
			count = Number(Object.keys(selectedFilters).length);
		});
		if (count > 0) {
			this.filterCount = '(' + count.toString() + ')';
		} else {
			this.filterCount = '';
		}
	}

	ngOnDestroy() {
		if (this.cacheInterval) {
			clearInterval(this.cacheInterval);
		}

		if (this.toggleInterval) {
			clearInterval(this.toggleInterval);
		}
		this.destroy$.next();
	    this.destroy$.complete();
	}

	countryFilterSummary() {
		this.filterService.setFilters({
			countryIds: {
				name: 'Country',
				values: this.cacheObj.countries,
				slice: window.location.href.split('/')[3],
			} as Filter,
		});
	}

	usersFilterSummary() {
		this.filterService.setFilters({
			userIds: {
				name: 'User',
				values: this.cacheObj.users?.map((i) => i.name),
				slice: window.location.href.split('/')[3],
			} as Filter,
		});
	}

	statusFilterSummary() {
		this.filterService.setFilters({
			statusIds: {
				name: 'Status',
				values: this.cacheObj.status,
				slice: window.location.href.split('/')[3],
			} as Filter,
		});
	}

	exportIDFilterSummary() {
		this.filterService.setFilters({
			exportIds: {
				name: 'Domain',
				values: this.cacheObj.exportIds,
				slice: window.location.href.split('/')[3],
			} as Filter,
		});
	}

	exportNameFilterSummary() {
		this.filterService.setFilters({
			exportName: {
				name: 'ExportName',
				values:  this.cacheObj.exportName? [{ value: this.cacheObj.exportName, label: this.cacheObj.exportName }] : '',
				slice: window.location.href.split('/')[3],
			} as unknown as Filter,
		});
	}

	exportFormatFilterSummary() {
		this.filterService.setFilters({
			exportFormatIds: {
				name: 'ExportFormat',
				values: this.cacheObj.exportFormatIds,
				slice: window.location.href.split('/')[3],
			} as unknown as Filter,
		});
	}

	reportIDFilterSummary() {
		this.filterService.setFilters({
			reportinProjectIds: {
				name: 'Operation',
				values: this.cacheObj.reportingProjectIds,
				slice: window.location.href.split('/')[3],
			} as Filter,
		});
	}

	reportNameFilterSummary() {
		this.filterService.setFilters({
			reportingProjectName: {
				name: 'reportingProjectName',
				values:  this.cacheObj.reportingProjectName? [{ value: this.cacheObj.reportingProjectName, label: this.cacheObj.reportingProjectName }] : '',
				slice: window.location.href.split('/')[3],
			} as unknown as Filter,
		});
	}

	jeeJobIDFilterSummary() {
		this.filterService.setFilters({
			jobIds: {
				name: 'Periodicity',
				values: this.cacheObj.jobIds,
				slice: window.location.href.split('/')[3],
			} as Filter,
		});
	}

	reportGroupIdFilterSummary() {
		this.filterService.setFilters({
			reportingGroupIds: {
				name: 'reportingGroupIds',
				values:  this.cacheObj.reportingGroupIds,
				slice: window.location.href.split('/')[3],
			}  as Filter,
		});
	}

	cdmDeliveryIdsFilterSummary() {
		this.filterService.setFilters({
			cdmDeliveryIds: {
				name: 'cdmDeliveryIds',
				values: this.cacheObj.cdmDeliveryIds,
				slice: window.location.href.split('/')[3],
			} as Filter,
		});
	}

	datesFilterSummary(dateStart: any, dateEnd: any) {
		if (dateStart != null || dateEnd != null) {
			const start = dateStart == 'Invalid Date' || dateStart == null ? '' : this.datepipe.transform(dateStart, 'yyyy-MM-dd');
			const end = dateEnd == 'Invalid Date' || dateEnd == null ? '' : this.datepipe.transform(dateEnd, 'yyyy-MM-dd');
			this.filterService.setFilters({
				dates: {
					name: 'Date',
					values: (start !== undefined && end !== undefined) ? [start, end] : null,
					slice: window.location.href.split('/')[3],
				} as Filter,
			});
		} else {
			this.filterService.setFilters({
				dates: {
					name: 'Date',
					values: null,
					slice: window.location.href.split('/')[3],
				} as unknown as Filter,
			});
		}
	}
	//Quick Filter Methods End//

  getSelectedFilters(selectedFilters: any){
		this.cacheObj = selectedFilters;
		this.selectedFilters = selectedFilters;
	}

}
