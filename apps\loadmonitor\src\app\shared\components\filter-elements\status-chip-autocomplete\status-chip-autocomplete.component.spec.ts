import { HttpClient } from '@angular/common/http';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { Status } from '@loadmonitor/shared/interfaces/Status';
import { HttpClientTestingModule } from '@angular/common/http/testing';

import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { SharedModule } from '@loadmonitor/shared/shared.module';

import { StatusChipAutocompleteComponent } from './status-chip-autocomplete.component';

describe('StatusChipAutocompleteComponent', () => {
  let component: StatusChipAutocompleteComponent;
  let fixture: ComponentFixture<StatusChipAutocompleteComponent>;
  let httpClient: HttpClient;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [StatusChipAutocompleteComponent],
      imports: [SharedModule, HttpClientTestingModule, NoopAnimationsModule],
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(StatusChipAutocompleteComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
    httpClient = TestBed.inject(HttpClient);
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('getSpecialStatusForExport() [METHOD]', () => {
    let returnValue = new Array<Status>();
    let exportId = 1;
    let sendingId = 2;

    it('should exist', () => {
      expect(component.getSpecialStatusesForExport).toBeTruthy();
    });

    it('should return array with elements', () => {
      returnValue = component.getSpecialStatusesForExport(exportId, sendingId);

      expect(returnValue.length).toBeGreaterThan(0);
    });
  });
});
