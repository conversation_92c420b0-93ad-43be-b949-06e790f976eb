import { ComponentFixture, TestBed } from '@angular/core/testing';
import { AppComponent } from './app.component';
import { AuthFacade } from '@dwh/lmx-lib/src/lib/services/auth';
import { UserService } from '@dwh/lmx-lib/src/lib/services/auth';
import { of } from 'rxjs';

class MockAuthFacade {
    user$ = of({ email: '<EMAIL>' }); // Mock user observable
    registerAuthentication = jest.fn(); // Mock method
}

class MockUserService {
    signInUser = jest.fn(); // Mock method
}

describe('AppComponent', () => {
    let component: AppComponent;
    let fixture: ComponentFixture<AppComponent>;
    let authFacade: AuthFacade;
    let userService: UserService;

    beforeEach(async () => {
        await TestBed.configureTestingModule({
            declarations: [AppComponent],
            providers: [
                { provide: AuthFacade, useClass: MockAuthFacade },
                { provide: UserService, useClass: MockUserService },
            ],
        }).compileComponents();
    });

    beforeEach(() => {
        fixture = TestBed.createComponent(AppComponent);
        component = fixture.componentInstance;
        authFacade = TestBed.inject(AuthFacade);
        userService = TestBed.inject(UserService);
        fixture.detectChanges();
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });

    it('should call signInUser and registerAuthentication on ngOnInit', () => {
        component.ngOnInit();
        expect(userService.signInUser).toHaveBeenCalled();
        expect(authFacade.registerAuthentication).toHaveBeenCalled();
    });

    it('should store user email in localStorage and return username when user is present', (done) => {
        component.userName$.subscribe((username) => {
            expect(username).toBe('<EMAIL>');
            expect(localStorage.getItem('Email')).toBe('<EMAIL>');
            done();
        });
    });
});
