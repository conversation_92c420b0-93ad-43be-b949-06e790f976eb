import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { HttpClient } from '@angular/common/http';
import { ConfigService } from './config.service';
import { Category } from '@loadmonitor/shared/interfaces/Category';

@Injectable({
  providedIn: 'root',
})
export class CategoryService {
  private url!:string;

  constructor(private http: HttpClient, private config: ConfigService) {
    this.url = `${this.config.getApiUrl()}/categories`;
  }

  getAsync(
    sectorIds?: number[]
  ): Observable<Category[]> {
    const body={sectorIds};
    return this.http.post<Category[]>(this.url, body);
  }
}
