import { ComponentFixture, TestBed } from '@angular/core/testing';
import { FilterTrayFormComponent } from './filter-tray-form.component'; // Adjust the import as needed
import { ReactiveFormsModule } from '@angular/forms';
import { of, Subject } from 'rxjs';
import { FilterTrayCacheService } from '@loadmonitor/shared/services/filter-tray-cache.service';
import { HttpClientTestingModule } from '@angular/common/http/testing';

describe('FilterTrayFormComponent', () => {
  let component: FilterTrayFormComponent;
  let fixture: ComponentFixture<FilterTrayFormComponent>;
  let resetFormSubject: Subject<boolean>;

  beforeEach(() => {
    resetFormSubject = new Subject<boolean>(); // Create a mock Subject

    TestBed.configureTestingModule({
      imports: [ReactiveFormsModule, HttpClientTestingModule], // Add HttpClientModule here
      declarations: [FilterTrayFormComponent],
      providers: [
        // Provide any required services here
        // For example, if FilterTrayCacheService is used in your component
        FilterTrayCacheService,
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(FilterTrayFormComponent);
    component = fixture.componentInstance;

    // Assign the mock Subject to the component's resetForm
    component.resetForm = resetFormSubject;

    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
