{"compileOnSave": false, "compilerOptions": {"rootDir": ".", "sourceMap": true, "declaration": false, "moduleResolution": "node", "emitDecoratorMetadata": true, "experimentalDecorators": true, "resolveJsonModule": true, "importHelpers": true, "target": "es2019", "module": "esnext", "lib": ["es2019", "dom"], "skipLibCheck": true, "skipDefaultLibCheck": true, "baseUrl": ".", "paths": {"@dwh/*": ["libs/*"], "@dwh/lmx-lib": ["libs/lmx-lib/src/index.ts"], "@loadmonitor/*": ["apps/loadmonitor/src/app/*"]}}, "exclude": ["node_modules", "tmp"]}