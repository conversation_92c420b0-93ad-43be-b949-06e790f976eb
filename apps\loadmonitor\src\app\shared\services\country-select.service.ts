import { Injectable } from '@angular/core';
import { keyBy, values } from 'lodash';
import { merge } from 'rxjs';
import { Country } from '../interfaces/Country';
import { CountryService } from './country.service';

@Injectable({
	providedIn: 'root',
})
export class CountrySelectService {
	private allCountries: Country[] = [];
	private CountryNumId: number[] = [];

	constructor(private countryService: CountryService) {
		this.countryService.getAsync().subscribe((result) => {
			//this.setCountries(result);
			this.allCountries = result;
		});
	}

	private setCountries(result: Country[]) {
		const merged = merge(keyBy(this.allCountries, 'id'), keyBy(result, 'id'));
		this.allCountries = values(merged);
		this.allCountries.sort((firstService: Country, otherService: Country) => firstService.name.localeCompare(otherService.name));
	}

	getAllCountries(): Country[] {
		return this.allCountries;
	}

	getSelectedCountryNames(countryIds: Country[]): string[] {
		this.CountryNumId = [];
		countryIds.forEach((i) => {
			this.CountryNumId.push(i.id);
		});
		return this.allCountries.filter((country) => this.CountryNumId.includes(country.id)).map((country: Country) => country.name) || [];
	}
	getSelectedCountryIds(countryIds: Country[]): number[] {
		this.CountryNumId = [];
		if (countryIds) {
			countryIds.forEach((i) => {
				this.CountryNumId.push(i.id);
			});
			return this.allCountries.filter((country) => this.CountryNumId.includes(country.id)).map((country: Country) => country.id) || [];
		}
		else
		return [];
	}
}
