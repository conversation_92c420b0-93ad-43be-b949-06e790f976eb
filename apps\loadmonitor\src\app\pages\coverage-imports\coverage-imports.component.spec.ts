import { ComponentFixture, TestBed } from '@angular/core/testing';
import { CoverageImportsComponent } from './coverage-imports.component';
import { CoverageImportService } from '@loadmonitor/shared/services/coverage-import.service';
import { MatSnackBar } from '@angular/material/snack-bar';
import { FormBuilder } from '@angular/forms';
import { of } from 'rxjs';
import { MatDialog } from '@angular/material/dialog';
import { FilterService } from '@dwh/lmx-lib/src';
import { FilterTrayCacheService } from '@loadmonitor/shared/services/filter-tray-cache.service';
import { DefaultSnackbarComponent } from '@loadmonitor/shared/components/default-snackbar/default-snackbar.component';
import { DatePipe } from '@angular/common';

describe('CoverageImportsComponent', () => {
  let component: CoverageImportsComponent;
  let fixture: ComponentFixture<CoverageImportsComponent>;
  let mockCoverageImportService: jest.Mocked<CoverageImportService>;
  let mockSnackBar: jest.Mocked<MatSnackBar>;
  let mockDialog: jest.Mocked<MatDialog>;
  let mockFilterService: jest.Mocked<FilterService>;
  let mockFilterTrayCacheService: jest.Mocked<FilterTrayCacheService>;

  beforeEach(() => {
    mockCoverageImportService = {
      getAsync: jest.fn(),
    } as unknown as jest.Mocked<CoverageImportService>;

    mockSnackBar = {
      openFromComponent: jest.fn(),
    } as unknown as jest.Mocked<MatSnackBar>;

    mockDialog = {
      open: jest.fn(),
    } as unknown as jest.Mocked<MatDialog>;

    mockFilterService = {
      filtersSelected$: of({}),
      setFilters: jest.fn(),
    } as unknown as jest.Mocked<FilterService>;

    mockFilterTrayCacheService = {
      getCacheValueData: jest.fn(),
      getCacheNameData: jest.fn(),
    } as unknown as jest.Mocked<FilterTrayCacheService>;

    TestBed.configureTestingModule({
      declarations: [CoverageImportsComponent],
      providers: [
        { provide: CoverageImportService, useValue: mockCoverageImportService },
        { provide: MatSnackBar, useValue: mockSnackBar },
        { provide: FormBuilder, useClass: FormBuilder },
        { provide: MatDialog, useValue: mockDialog },
        { provide: FilterService, useValue: mockFilterService },
        { provide: FilterTrayCacheService, useValue: mockFilterTrayCacheService },
        { provide: DatePipe, useValue: new DatePipe('en-US') },
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(CoverageImportsComponent);
    component = fixture.componentInstance;
  });

  it('should create the component', () => {
    expect(component).toBeTruthy();
  });

  // More tests can be added for other methods and functionalities as needed.
});
