import { Component, OnInit } from '@angular/core';

@Component({
  selector: 'lm-auth-failed',
  templateUrl: './auth-failed.component.html',
  styleUrls: ['./auth-failed.component.scss', './../../shared/shared-styles.scss'],
})
export class AuthFailedComponent implements OnInit{

  errorMessage?: string;
  errorMessageShort?: string;
  iconArrowDown = false;

  constructor() {
    this.setErrorMessagePropertiesFromLocalStorage();
  }

  ngOnInit(){
    this.setErrorMessagePropertiesFromLocalStorage();
  }

  setErrorMessagePropertiesFromLocalStorage() {
    // this.errorMessage = localStorage.getItem(`msal-error-${CLIENT_ID}`) || undefined;
    // this.errorMessageShort = this.errorMessage ? this.errorMessage?.substring(0, this.errorMessage.indexOf(':')) : undefined;
  }

  toggleIcon() {
    this.iconArrowDown = !this.iconArrowDown;
  }

}
