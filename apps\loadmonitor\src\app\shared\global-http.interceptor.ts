import { Injectable } from '@angular/core';
import { HttpErrorResponse, HttpEvent, HttpHandler, HttpInterceptor, HttpRequest } from '@angular/common/http';
import { Observable, throwError, timer, take } from 'rxjs';
import { catchError, retryWhen, mergeMap } from 'rxjs/operators';
import { MatSnackBar } from '@angular/material/snack-bar';
import { DefaultSnackbarComponent } from '@loadmonitor/shared/components/default-snackbar/default-snackbar.component';

let countRetries = 0;
const maxRetries = 3;

@Injectable()
export class GlobalHttpInterceptorService implements HttpInterceptor {

  defaultError = {
    message: 'The Ultimate Error of Life, the Universe, and Everything',
    code: 42,
  };

  constructor(private snackBar: MatSnackBar) {}

  intercept(
    req: HttpRequest<any>,
    next: HttpHandler
  ): Observable<HttpEvent<any>> {
    return next.handle(req).pipe(
      retryWhen(errors => {
        countRetries++;
        return errors
            .pipe(
              mergeMap(error => (error.status === 401 || error.status === 403) && countRetries < maxRetries ? timer(0) : throwError(() => new HttpErrorResponse(error))),
              take(maxRetries)
            )
      }),
      catchError((reqError: HttpErrorResponse) => {
        countRetries = 0;
        let errorMessage = this.generateMessage(reqError);

        if (reqError.status === 401 || reqError.status === 403) {
          errorMessage = 'There is a problem with authentication. You are unauthorized. Please try again!';
        }

        this.notify(errorMessage ? errorMessage : reqError.message);
        return throwError(() => new Error(reqError.message));
      })
    );
  }

  private generateMessage(error: any): string {
    console.log({ error });
    const message = error?.error?.title == null ? error?.error : error?.error?.title;

    // Create the status message
    const statusMessage = `${error?.status !== 0 ? error.status : this.defaultError.code} - ${
        error?.statusText !== 'Unknown Error'
          ? error.statusText
          : this.defaultError.message
    }`;

    // Combine the messages if needed, or just return the status message
    return `${message} - ${statusMessage}`; // Adjust as necessary
  }

  private notify(message: string) {
    this.snackBar.openFromComponent(DefaultSnackbarComponent, {
      duration: 10 * 1000,
      data: message,
    });
  }
}
