import { UserCache } from '@loadmonitor/shared/interfaces/caching/UserCache';
import { Country } from '../Country';
import { DomainProductGroups } from '../DomainProductGroups';
import { BaseChannel } from '../BaseChannel';
import { Category } from '../Category';
import { Sector } from '../Sector';

export interface ShopMasterDataCache {
    sectorIds: number[];
    categoryIds: number[];
    domainProductGroupIds: number[];
    domainProductGroup: DomainProductGroups[];
    cachedChannelIds: number[];

    countryIds: number[];
    periodicityIds: number[];
    periodIds: any;

    unitIds:number[];
    status: number[];
    selectedDays: string;
    startDate?: Date;
    endDate?: Date;
    users?: UserCache[];
    isAutoRefresh?: boolean;
    countries:Country[];
    channels:BaseChannel[];
    category:Category[];
    sector:Sector[];
  }
