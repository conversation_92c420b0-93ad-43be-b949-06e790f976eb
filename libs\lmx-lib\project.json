{"name": "lmx-lib", "$schema": "../../node_modules/nx/schemas/project-schema.json", "projectType": "library", "sourceRoot": "libs/lmx-lib/src", "prefix": "lmx", "tags": [], "targets": {"build": {"executor": "@nx/angular:package", "outputs": ["{workspaceRoot}/dist/libs/lmx-lib"], "options": {"project": "libs/lmx-lib/ng-package.json"}, "configurations": {"production": {"tsConfig": "libs/lmx-lib/tsconfig.lib.prod.json"}, "development": {"tsConfig": "libs/lmx-lib/tsconfig.lib.json"}}, "defaultConfiguration": "production"}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/libs/lmx-lib"], "options": {"jestConfig": "libs/lmx-lib/jest.config.ts", "passWithNoTests": true}}, "lint": {"executor": "@nx/linter:eslint"}}}