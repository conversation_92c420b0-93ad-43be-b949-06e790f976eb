import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { HttpClient } from '@angular/common/http';
import { ConfigService } from './config.service';
import { shareReplay } from 'rxjs/operators';
import { FileType } from '../interfaces/FileType';

@Injectable({
  providedIn: 'root',
})
export class FileTypeService {

  private url!:string;

  private cachedFileType! : Observable<FileType[]>;
  constructor(private http: HttpClient, private config: ConfigService) {
    this.url = `${this.config.getApiUrl()}/FileType`;
  }

  getAsync(): Observable<FileType[]> {
    if(!this.cachedFileType)
    {
      this.cachedFileType = this.http
        .get<FileType[]>(this.url)
        .pipe(shareReplay(1));
    }
    return this.cachedFileType;
  }
}
