import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { FeatureMoveRoutingModule } from './feature-move-routing.module';
import { FeatureMoveComponent } from './feature-move.component';
import { SharedModule } from '@loadmonitor/shared/shared.module';
import { MatTableModule } from '@angular/material/table';
import { MatSortModule } from '@angular/material/sort';
import { MatPaginatorModule } from '@angular/material/paginator';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatSnackBarModule } from '@angular/material/snack-bar';
import { MatButtonModule } from '@angular/material/button';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { LmxLibModule } from '@dwh/lmx-lib';

@NgModule({
  declarations: [FeatureMoveComponent],
  imports: [
    CommonModule,
    FeatureMoveRoutingModule,
    SharedModule,
    MatTableModule,
    MatSortModule,
    MatPaginatorModule,
    MatExpansionModule,
    MatSnackBarModule,
    MatButtonModule,
    FormsModule,
    ReactiveFormsModule,
    LmxLibModule
  ],
})
export class FeatureMoveModule {
}
