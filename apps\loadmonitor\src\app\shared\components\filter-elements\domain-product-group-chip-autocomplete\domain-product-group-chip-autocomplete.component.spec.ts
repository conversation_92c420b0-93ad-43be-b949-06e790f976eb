import { HttpClientTestingModule } from '@angular/common/http/testing';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { SharedModule } from '@loadmonitor/shared/shared.module';

import { DomainProductGroupChipAutocompleteComponent } from './domain-product-group-chip-autocomplete.component';

describe('DomainProductGroupChipAutocompleteComponent', () => {
  let component: DomainProductGroupChipAutocompleteComponent;
  let fixture: ComponentFixture<DomainProductGroupChipAutocompleteComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [DomainProductGroupChipAutocompleteComponent],
      imports: [SharedModule, HttpClientTestingModule, NoopAnimationsModule],
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(
      DomainProductGroupChipAutocompleteComponent
    );
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
