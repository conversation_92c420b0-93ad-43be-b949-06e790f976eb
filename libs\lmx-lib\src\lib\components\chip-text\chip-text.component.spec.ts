import { ComponentFixture, TestBed } from '@angular/core/testing';

import { ChipTextComponent } from './chip-text.component';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';

describe('ChipTextComponent', () => {
	let component: ChipTextComponent;
	let fixture: ComponentFixture<ChipTextComponent>;

	beforeEach(async () => {
		await TestBed.configureTestingModule({
			imports: [
				NoopAnimationsModule,
				ChipTextComponent
			],
		}).compileComponents();

		fixture = TestBed.createComponent(ChipTextComponent);
		component = fixture.componentInstance;
		fixture.detectChanges();
	});

	it('should create', () => {
		expect(component).toBeTruthy();
	});
});
