import { Component, OnInit, ViewChild, AfterContentInit, Input, HostListener, OnDestroy } from '@angular/core';
import { MatPaginator } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import { MatTable, MatTableDataSource } from '@angular/material/table';
import { ItemMasterData } from '@loadmonitor/shared/interfaces/ItemMasterData';
import { COMMA, ENTER } from '@angular/cdk/keycodes';
import { FormBuilder, FormGroup } from '@angular/forms';
import { JobTypes } from '@loadmonitor/shared/jobTypes';
import { ItemMasterDataService } from '@loadmonitor/shared/services/item-master-data.service';
import { DefaultSnackbarComponent } from '@loadmonitor/shared/components/default-snackbar/default-snackbar.component';
import { MatSnackBar } from '@angular/material/snack-bar';
import { SettingsConstants } from '@loadmonitor/shared/settings.constants';
import { ItemMasterDataCache } from '@loadmonitor/shared/interfaces/caching/ItemMasterDataCache';
import { CreateTicketDialogComponent } from '@loadmonitor/shared/components/create-ticket-dialog/create-ticket-dialog.component';
import { MatDialog } from '@angular/material/dialog';
import { SelectionModel } from '@angular/cdk/collections';
import { Filter, FilterRecords, FilterService, FilterTrayComponent } from '@dwh/lmx-lib/src';
import { ToggleButtonComponent } from '@gfk/ng-lib';
import { DatePipe } from '@angular/common';
import { BehaviorSubject, Subject, Subscription } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { environment } from 'apps/loadmonitor/src/environments/environment';
import { CountryService } from '@loadmonitor/shared/services/country.service';
import { UserService } from '@loadmonitor/shared/services/user.service';
import { CountrySelectService } from '@loadmonitor/shared/services/country-select.service';
import { StatusService } from '@loadmonitor/shared/services/status.service';
import { StatusSelectService } from '@loadmonitor/shared/services/status-select.service';
import { JobTypeService } from '@loadmonitor/shared/services/job-type.service';
import { DomainProductGroupService } from '@loadmonitor/shared/services/domainProductgroup.service';
import { HelperService } from '@loadmonitor/shared/services/helper.service';
import { CategoryService } from '@loadmonitor/shared/services/category.service';
import { SectorService } from '@loadmonitor/shared/services/sector.service';
import * as moment from 'moment';
import { FilterTrayCacheService } from '@loadmonitor/shared/services/filter-tray-cache.service';

@Component({
	selector: 'lm-item-master-data',
	templateUrl: './item-master-data.component.html',
	styleUrls: ['./item-master-data.component.scss'],
})
export class ItemMasterDataComponent implements OnInit, AfterContentInit, OnDestroy {
	@Input() icon = 'delete';

	@ViewChild(MatSort)
	sort: MatSort = new MatSort();

	@ViewChild(MatTable)
	table!: MatTable<ItemMasterData>;

	@ViewChild(MatPaginator)
	paginator!: MatPaginator;

	@ViewChild(FilterTrayComponent) filterTrayComponent: FilterTrayComponent | undefined;
	@ViewChild(ToggleButtonComponent) togglebuttonComponent: ToggleButtonComponent | undefined;

	//Mat-Expansion-Panel
	panelOpenState = false;
	public ItemMasterDataForm!: FormGroup;
	public cacheObj: ItemMasterDataCache = {} as ItemMasterDataCache;
	cacheName = 'cache::ItemMasterData';
  	selectedFilters: any;
	cacheValue: BehaviorSubject<any> = new BehaviorSubject<any>({});
	resetForm: Subject<boolean> = new Subject();
	/* *********************
	 * Components variables
	 ********************* */
	jobType: JobTypes = JobTypes.itemMasterData;
	imlJobType: string | undefined;

	// Progress Spinner visibility
	isLoading = false;

	//Tag-Chip-List
	readonly separatorKeysCodes = [ENTER, COMMA] as const;

	//Mat-Table
	dataSource = new MatTableDataSource<ItemMasterData>();
	selection = new SelectionModel<ItemMasterData>(true, []);
	IsSelectAll = false;
	displayedColumns: string[] = [
		'menu',
		'prepUnitId',
		'imlJobType',
		'loadUnitId',
		'status',
		'productGroupId',
		'productGroup',
		'mode',
		'jobId',
		'jeeJobInfo',
		'createdBy',
		'created',
		'started',
		'finished',
		'runtime',
		'message',
	];

	readonly date_with_time = SettingsConstants.DATE_FORMAT_WITH_TIME;

	/* *********************
	 * Components variables
	 ********************* */
	eventsSubject: Subject<void> = new Subject<void>();
	IsDisabled = false;
	environments = environment.environments;
	IsEnabled = false;
	FilterTrayOpenFlag = true;
	currentfilter = JSON.parse(localStorage.getItem('QuickFilter') || '[]').filter(
		(x) => x.user == localStorage.getItem('username') && x.slice == window.location.href.split('/')[3]
	);
	modalshow = false;
	filterCount = '';
	toggleInterval!: any;
	timer = 60;
	EnableRefresh = true;
	cacheInterval!: any;
	defaultoption = localStorage.getItem(this.cacheName + '-LastFilter') || 'Default';
	defaultfilter = JSON.parse(localStorage.getItem(this.cacheName + '-DefaultFilter') || '[]');
	private subscription!: Subscription | undefined;
	refreshedDateTime!: boolean;
	itemMasterData!: any[];

	@HostListener('document:visibilitychange', ['$event'])
	appVisibility() {
		if (document.hidden) {
			if (this.cacheInterval) {
				clearInterval(this.cacheInterval);
			}
			if (this.toggleInterval) {
				clearInterval(this.toggleInterval);
			}
		} else {
			if (this.cacheObj.isAutoRefresh === true) {
				this.cacheInterval = setInterval(() => {
					if (this.defaultoption != 'Default') {
						this.refresh();
					}
				}, this.timer * 1000);
			} else {
				if (this.cacheInterval !== undefined) clearInterval(this.cacheInterval);
				if (this.toggleInterval !== undefined) clearInterval(this.toggleInterval);
			}
		}
	}
	private destroy$ = new Subject<void>();

	constructor(
		private itemMasterDataService: ItemMasterDataService,
		private snackBar: MatSnackBar,
		public dialogbox: MatDialog,
		private formBuilder: FormBuilder,
		public filterService: FilterService,
		public datepipe: DatePipe,
		private filterTrayCacheService: FilterTrayCacheService,
		private jobTypeService: JobTypeService
	) {}

	private updateFC(): void {
		if(this.cacheObj){
			this.filterTrayCacheService.getCacheValueData(this.cacheObj);
		}
  	}


	ngOnInit(): void {
		if (this.environments.filter((x) => x.location == window.location.origin)[0]) {
			this.IsEnabled = this.environments.filter((x) => x.location == window.location.origin)[0].enabled;
		} else {
			this.IsEnabled = false;
		}
		this.ItemMasterDataForm = this.formBuilder.group({
			isAutoRefresh: this.togglebuttonComponent?.checked,
		});
		this.updateFC();

	}

	ngAfterContentInit() {
		this.afterChildComponentsLoaded();
		this.CreateFilterSummaryChips();
		this.SetFiltersCount();
		this.updateFC();
		if (this.cacheObj.isAutoRefresh === true) {
			if (this.cacheInterval === undefined) {
				this.cacheInterval = window.setInterval(() => {
					if (this.defaultoption != 'Default') {
						this.refresh();
					}
				}, this.timer * 1000);
			}
		} else {
			if (this.cacheInterval !== undefined) clearInterval(this.cacheInterval);
			if (this.toggleInterval !== undefined) clearInterval(this.toggleInterval);
		}
	}
	afterChildComponentsLoaded() {
		this.cacheObj = JSON.parse(localStorage.getItem(this.cacheName) || '{}');
	}

	saveInCache(autoRefreshStatus, AutoToggle) {
		this.setFormValue(autoRefreshStatus);
		localStorage.setItem(this.cacheName, JSON.stringify(this.selectedFilters));
		this.cacheObj = JSON.parse(localStorage.getItem(this.cacheName) || '{}');
		if (!AutoToggle) {
			this.defaultfilter = [];
			this.defaultfilter.push({
				data: this.selectedFilters,
				slice: window.location.href.split('/')[3],
				user: localStorage.getItem('username'),
			});
			localStorage.setItem(this.cacheName + '-DefaultFilter', JSON.stringify(this.defaultfilter));
			let nofilter = 0;
			let selectedfilter = '';
			this.currentfilter.forEach((i) => {
				// if (i.filter == this.defaultoption) {
				if(JSON.stringify(i.data) == JSON.stringify(this.cacheObj)){
					nofilter++;
					selectedfilter = i.filter;
				}
			});
			if (nofilter == 0) {
				this.defaultoption = 'Default';
			} else {
				this.defaultoption = selectedfilter;
			}

			localStorage.setItem(this.cacheName + '-LastFilter', this.defaultoption);
		}
		this.updateFC();
	}

	refresh(): void {
		this.refreshedDateTime = false;
		this.filterTrayComponent?.showSpinner(true);
		this.isLoading = true;
		this.destroy$.next();
		const selectedJobTypeList: any = [];
		this.jobTypeService.getAsync(JobTypes.itemMasterData).forEach((jobType: any) => {
			this.selectedFilters.jobType.forEach((selectedJobType: any) => {
				if(jobType.id == selectedJobType){
					selectedJobTypeList.push(jobType.name);
				}
			})
		})
		this.itemMasterDataService
			.getAsync(
				this.selectedFilters.countries?.map((country) => country),
				this.selectedFilters.sector?.map((sector) => sector),
				this.selectedFilters.category?.map((category) => category.id),
				this.selectedFilters.domainProductGroup?.map((domainProductGroup) => domainProductGroup.id),
				this.selectedFilters.users?.map((users) => users.userName),
				this.selectedFilters.status?.map((status) => status),
        		this.selectedFilters.prepUnitIds,
				this.selectedFilters.loadUnitIds,
				this.selectedFilters.jobIds,
				this.selectedFilters.domainProductGroupType ? (this.selectedFilters.domainProductGroupType == '1' ? 1 : 2) : null,
				selectedJobTypeList,
				this.selectedFilters.startDate ? this.selectedFilters.startDate : null,
				this.selectedFilters.endDate ? moment(this.selectedFilters.endDate).format().split('T')[0] + 'T23:59:59.999Z' : null
			)
			.pipe(
				takeUntil(this.destroy$)
			)
			.subscribe((result) => {
				this.refreshedDateTime = true;
				if (result.moreRecordsAvailable) {
					this.notify('More than 1000 results exist!');
				}

				if (result.count === 0) {
					this.notify('No result found!');
					this.itemMasterData = [];
				}
				this.isLoading = false;
				this.itemMasterData = result.records;
				// add elements to detail menu
				result.records.forEach((element) => {
					element.detailMenuConfig = [
						{
							menuText: 'Features',
							menuIcon: 'open_in_new',
							service: 'productgroupfeatures',
							params: { productgroupid: element.productGroupId },
						},
						{
							menuText: 'Load Items',
							menuIcon: 'open_in_new',
							service: 'loaditems',
							params: { productgroupid: element.productGroupId },
						},
					];
				});

				result.records.sort((a, b) => {
					return b.prepUnitId - a.prepUnitId;
				});

				this.dataSource = new MatTableDataSource<ItemMasterData>(result.records);
				this.dataSource.sort = this.sort;
				this.paginator.pageIndex = 0;
				this.dataSource.paginator = this.paginator;
				this.table.dataSource = this.dataSource;
				this.filterTrayComponent?.showSpinner(false);
			},
			(error) => {
				if (error.status === 504) {
					this.notify('Gateway Timeout. The server took too long to respond. Please try again later.');
				} 
				else {
					this.notify(`An error occurred: ${error.message}`);
				}
				this.isLoading = false;
				this.filterTrayComponent?.showSpinner(false);
			});
		//this.saveInCache(this.cacheObj.isAutoRefresh, false);
		this.SetFiltersCount();
	}

	private notify(message: string) {
		this.snackBar.openFromComponent(DefaultSnackbarComponent, {
			duration: 10 * 1000,
			data: message,
		});
	}

	openJiraTicket(): void {
		const checkedList = this.dataSource.data.filter((i) => i.isChecked);
		const dialogRef = this.dialogbox.open(CreateTicketDialogComponent, {
			width: '1000px',
			height: '487px',
			data: {
				prepUnitIds: checkedList.map((i) => {
					return i.prepUnitId;
				}),
				loadUnitIds: checkedList.map((i) => {
					return i.loadUnitId;
				}),
				jobType: this.jobType,
				title: 'ItemMasterData',
			},
		});
		dialogRef.afterClosed().subscribe((resSubmit) => {
			if (resSubmit === true) {
				this.dataSource.data.forEach((i) => (i.isChecked = false));
				this.selection.clear();
				this.IsSelectAll = false;
			}
		});
	}

	checkAtleastOneRowChecked() {
		return this.dataSource.data.filter((i) => i.isChecked).length > 0;
	}

	rowToggleSelection(ev, item) {
		item.isChecked = ev.checked;
	}

	selectRows(selectrow) {
		this.IsSelectAll = !this.IsSelectAll;
		let endIndex: number;
		const page = this.dataSource.paginator !== null ? this.dataSource.paginator.pageSize : 0;
		const pageindex = this.dataSource.paginator !== null ? this.dataSource.paginator.pageIndex : 0;

		if (!selectrow.checked) {
			this.selection.clear();
			if (this.dataSource.data.length > (pageindex + 1) * page) {
				endIndex = (pageindex + 1) * page;
			} else {
				endIndex = this.dataSource.data.length;
			}

			for (let index = pageindex * page; index < endIndex; index++) {
				const row = this.dataSource.data[index];
				this.selection.deselect(row);
				this.dataSource.data[index].isChecked = false;
			}
		} else {
			if (this.dataSource.data.length > (pageindex + 1) * page) {
				endIndex = (pageindex + 1) * page;
			} else {
				endIndex = this.dataSource.data.length;
			}
			for (let index = pageindex * page; index < endIndex; index++) {
				const row = this.dataSource.sortData(this.dataSource.data, this.sort)[index];
				this.selection.select(row);
				this.dataSource.sortData(this.dataSource.data, this.sort)[index].isChecked = true;
			}
		}
	}

	isSelectedPage() {
		const numSelected = this.selection.selected.length;
		const page = this.dataSource.paginator !== null ? this.dataSource.paginator.pageSize : 0;
		const pageindex = this.dataSource.paginator !== null ? this.dataSource.paginator.pageIndex : 0;
		let endIndex: number;
		if (this.dataSource.data.length > (pageindex + 1) * page) {
			endIndex = (pageindex + 1) * page;
		} else {
			endIndex = this.dataSource.data.length - pageindex * page;
		}

		return numSelected === endIndex;
	}

	//Quick Filter Methods//
	openmodal() {
		this.eventsSubject.next();
	}

	disablesave(status) {
		this.IsDisabled = status;
	}

	setFormValue(autoRefreshStatus) {
		this.ItemMasterDataForm.patchValue({
			isAutoRefresh: autoRefreshStatus === undefined || autoRefreshStatus === false ? false : true,
		});
	}

	saveInQuickFilter(autoRefreshStatus, filterName) {
		this.setFormValue(autoRefreshStatus);
		this.currentfilter = JSON.parse(localStorage.getItem('QuickFilter') || '[]');
		this.currentfilter.push({
			data: this.selectedFilters,
			filter: filterName,
			slice: window.location.href.split('/')[3],
			user: localStorage.getItem('username'),
		});
		this.defaultoption = filterName;
		const oldcache = this.cacheObj;
		const olddefault = localStorage.getItem(this.cacheName + '-DefaultFilter');
		this.ConfirmApply(autoRefreshStatus);
		this.cacheObj = oldcache;
		localStorage.setItem(this.cacheName + '-DefaultFilter', olddefault || '[]');
		localStorage.setItem('QuickFilter', JSON.stringify(this.currentfilter));
		localStorage.setItem(this.cacheName + '-LastFilter', filterName);
		this.SetFiltersCount();
	}

	applyfilter(selectedvalue) {
		this.resetForm.next(true);
		const oldautotoggle = this.cacheObj.isAutoRefresh;
		selectedvalue = selectedvalue.value ? selectedvalue.value : selectedvalue;
		this.defaultoption = selectedvalue;
		localStorage.setItem(this.cacheName + '-LastFilter', this.defaultoption || 'Default');
		if (selectedvalue == 'Default') {
			if (
				JSON.parse(localStorage.getItem(this.cacheName + '-DefaultFilter') || '[]').filter(
					(x) => x.user == localStorage.getItem('username') && x.slice == window.location.href.split('/')[3]
				)[0]
			) {
				this.cacheObj = JSON.parse(localStorage.getItem(this.cacheName + '-DefaultFilter') || '[]').filter(
					(x) => x.user == localStorage.getItem('username') && x.slice == window.location.href.split('/')[3]
				)[0]['data'];
			}

			localStorage.setItem(this.cacheName, JSON.stringify(this.cacheObj));
		} else {
			this.cacheObj = JSON.parse(localStorage.getItem('QuickFilter') || '[]')
				.filter((x) => x.user == localStorage.getItem('username') && x.slice == window.location.href.split('/')[3])
				.filter((x) => x.filter == selectedvalue)[0]['data'];
			localStorage.setItem(this.cacheName, JSON.stringify(this.cacheObj));
		}
		this.cacheObj.isAutoRefresh = oldautotoggle;
		this.CreateFilterSummaryChips();
		this.selectedFilters = this.cacheObj;
    if (this.cacheObj.selectedDays == '3') {
			const date = new Date();
			this.cacheObj.startDate = new Date(Date.UTC(date.getFullYear(), date.getMonth(), date.getDate() - 2, 0, 0, 0, 0));
			this.cacheObj.endDate = new Date(Date.UTC(date.getFullYear(), date.getMonth(), date.getDate(), 0, 0, 0, 0));
		} else if (this.cacheObj.selectedDays == '2') {
			const date = new Date();
			this.cacheObj.startDate = new Date(Date.UTC(date.getFullYear(), date.getMonth(), date.getDate(), 0, 0, 0, 0));
			this.cacheObj.endDate = new Date(Date.UTC(date.getFullYear(), date.getMonth(), date.getDate(), 0, 0, 0, 0));
		}
		setTimeout(() => {
			this.updateFC();
			this.refresh();
		}, 200);
		localStorage.setItem(this.cacheName, JSON.stringify(this.cacheObj));
	}

	CreateFilterSummaryChips() {
		if (this.cacheObj && Object.keys(this.cacheObj).length) {
			this.usersFilterSummary();
			this.statusFilterSummary();
			this.countryFilterSummary();
			this.domainProductGroupIdsFilterSummary();
			this.sectorIdsFilterSummary();
			this.categoryIdsFilterSummary();
			this.jeeJobIdsFilterSummary();
			this.jobTypeIdsFilterSummary();
			this.loadUnitIdsFilterSummary();
			this.prepunitFilterSummary();
			this.domainPgTypeIdTypeFilterSummary();
			this.datesFilterSummary(this.cacheObj.startDate, this.cacheObj.endDate);
		}
	}

	ConfirmApply(autoRefreshStatus) {
		this.saveInCache(autoRefreshStatus, false);
		this.CreateFilterSummaryChips();

		let count;
		this.filterService.filtersSelected$.subscribe((selectedFilters: FilterRecords) => {
			count = Number(Object.keys(selectedFilters).length);
		});

		if (count > 0) {
			this.refresh();
		} else {
			if (confirm('Loading results without any filters selected will take some time to load. Are you sure you want to proceed?')) {
				this.refresh();
			}
		}
	}

	public isShownFilterTrayEventHandler($event: boolean) {
		this.filterTrayComponent?.toggleShowTray($event);
		this.FilterTrayOpenFlag = $event;
	}

	getWidth() {
		this.FilterTrayOpenFlag = !this.FilterTrayOpenFlag;
		return '100%';
	}

	filterSummaryChipClear(filteredItems) {
		if (filteredItems == 'ALL') {
			if (confirm('This will remove all of your selected filters and could not be reversed. Are you sure you want to do that?')) {
				this.resetForm.next(true);
				this.filterTrayCacheService.getCacheNameData(this.cacheName);
			}
		}
		this.saveInCache(this.cacheObj.isAutoRefresh, false);
		this.CreateFilterSummaryChips();
		this.SetFiltersCount();
	}

	toggleChecked(check: any) {
		if (check === true) {
			this.saveInCache(check, true);
			this.toggleInterval = setInterval(() => {
				if (this.defaultoption != 'Default') {
					this.refresh();
				}
			}, this.timer * 1000);
		} else {
			if (this.toggleInterval !== undefined) clearInterval(this.toggleInterval);
			if (this.cacheInterval !== undefined) clearInterval(this.cacheInterval);
			this.saveInCache(check, true);
		}
	}

	SetFiltersCount() {
		let count;
		this.filterService.filtersSelected$.subscribe((selectedFilters: FilterRecords) => {
			count = Number(Object.keys(selectedFilters).length);
		});
		if (count > 0) {
			this.filterCount = '(' + count.toString() + ')';
		} else {
			this.filterCount = '';
		}
	}

	ngOnDestroy() {
		if (this.cacheInterval) {
			clearInterval(this.cacheInterval);
		}

		if (this.toggleInterval) {
			clearInterval(this.toggleInterval);
		}
		this.destroy$.next();
    	this.destroy$.complete();
	}

	countryFilterSummary() {
		this.filterService.setFilters({
			countryIds: {
				name: 'Country',
				values: this.cacheObj.countries,
				slice: window.location.href.split('/')[3],
			} as Filter,
		});
	}

	usersFilterSummary() {
		this.filterService.setFilters({
			userIds: {
				name: 'User',
				values: this.cacheObj.users?.map((i) => i.name),
				slice: window.location.href.split('/')[3],
			} as Filter,
		});
	}

	statusFilterSummary() {
		this.filterService.setFilters({
			statusIds: {
				name: 'Status',
				values: this.cacheObj.status,
				slice: window.location.href.split('/')[3],
			} as Filter,
		});
	}

	domainProductGroupIdsFilterSummary() {
		this.filterService.setFilters({
			domainProductGroupIds: {
				name: 'Domain',
				values: this.cacheObj.domainProductGroup,
				slice: window.location.href.split('/')[3],
			} as Filter,
		});
	}

	sectorIdsFilterSummary() {
		this.filterService.setFilters({
			sectorIds: {
				name: 'sectorIds',
				values: this.cacheObj.sector,
				slice: window.location.href.split('/')[3],
			} as unknown as Filter,
		});
	}

	categoryIdsFilterSummary() {
		this.filterService.setFilters({
			categoryIds: {
				name: 'categoryIds',
				values: this.cacheObj.category,
				slice: window.location.href.split('/')[3],
			} as Filter,
		});
	}

	prepunitFilterSummary() {
		this.filterService.setFilters({
			prepUnitIds: {
				name: 'prepUnitIds',
				values: this.cacheObj.prepUnitIds,
				slice: window.location.href.split('/')[3],
			} as unknown as Filter,
		});
	}

	loadUnitIdsFilterSummary() {
		this.filterService.setFilters({
			loadUnitIds: {
				name: 'loadUnitIds',
				values: this.cacheObj.loadUnitIds,
				slice: window.location.href.split('/')[3],
			} as Filter,
		});
	}

	jeeJobIdsFilterSummary() {
		this.filterService.setFilters({
			jeeJobIds: {
				name: 'jeeJobIds',
				values: this.cacheObj.jobIds,
				slice: window.location.href.split('/')[3],
			} as Filter,
		});
	}

	jobTypeIdsFilterSummary() {
		this.filterService.setFilters({
			baseProjectName: {
				name: 'jobTypeIds',
				values: this.cacheObj.jobType,
				slice: window.location.href.split('/')[3],
			} as unknown as Filter,
		});
	}

	domainPgTypeIdTypeFilterSummary() {
		this.filterService.setFilters({
			domainPgTypeId: {
				name: 'domainPgTypeId',
				values: this.cacheObj.domainProductGroupType ? [{ value: this.cacheObj.domainProductGroupType, label: this.cacheObj.domainProductGroupType }] : null,
				slice: window.location.href.split('/')[3],
			} as unknown as Filter,
		});
	}

	datesFilterSummary(dateStart: any, dateEnd: any) {
		if (dateStart != null || dateEnd != null) {
			const start = dateStart == 'Invalid Date' || dateStart == null ? '' : this.datepipe.transform(dateStart, 'yyyy-MM-dd');
			const end = dateEnd == 'Invalid Date' || dateEnd == null ? '' : this.datepipe.transform(dateEnd, 'yyyy-MM-dd');
			this.filterService.setFilters({
				dates: {
					name: 'Date',
					values: (start !== undefined && end !== undefined) ? [start, end] : null,
					slice: window.location.href.split('/')[3],
				} as Filter,
			});
		} else {
			this.filterService.setFilters({
				dates: {
					name: 'Date',
					values: null,
					slice: window.location.href.split('/')[3],
				} as unknown as Filter,
			});
		}
	}
	//Quick Filter Methods End//

  getSelectedFilters(selectedFilters: any) {
		this.cacheObj = selectedFilters;
		this.selectedFilters = selectedFilters;
	}
}
