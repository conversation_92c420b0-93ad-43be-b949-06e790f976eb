@use 'apps/loadmonitor/src/styles/theme/gfk-light.palette' as gfk;
@use 'sass:map' as map;
@use '@gfk/style' as gfk-style;

.full-width-table td {
  padding-right: 7px;
  padding-left: 7px;
  border: 1px solid rgba(211, 211, 211, 0.247);
  word-wrap: break-word;
}

.mat-row:not(:nth-child(4n+1)){
  background-color: map.get(gfk.$palette, 'bg-light');
}

table {
  width: 2300px;
}

table th {
  white-space: nowrap;
}

.alignright {
  float: right;
  margin-top: 8px;
  margin-bottom: 8px;
  margin-right: 36px;
  padding: 1px 20px;
  font-family: 'Lato';
}
.mat-accordion-align {
  display: inline-block;
  width: 100%;
}
.chip-list {
  width: 100%;
}

.columns{
  display: inline-grid;
}

.close.mat-button {
  position: inherit;
  top: 0;
  right: 0;
  padding: 2px;
  line-height: 3px;
  min-width: auto;
}

.close-icon {
  transition: 1s ease-in-out;
}

.close-icon:hover {
  transform: rotate(180deg);
}


.columns{
  display: inline-grid;
}


th.mat-sort-header-sorted {
  color: black;
}

tr.expanded-detail-row {
  height: 0;
}

tr.expanded-element-row:not(.expanded-row):hover {
  background: whitesmoke;
}

tr.expanded-element-row:not(.expanded-row):active {
  background: #efefef;
}

.expanded-element-row td {
  border-bottom-width: 0;
}

.expanded-element-detail {
  overflow: hidden;
  display: flex;
}

.expanded-element-diagram {
  min-width: 80px;
  border: 2px solid black;
  padding: 8px;
  font-weight: lighter;
  margin: 8px 0;
  height: 104px;
}

.expanded-element-symbol {
  font-weight: bold;
  font-size: 40px;
  line-height: normal;
}

.expanded-element-description {
  padding: 16px;
}

.expanded-element-description-attribution {
  opacity: 0.5;
}

.full-width-table .selected-row-background td {
  background-color: map.get(gfk.$palette, 'orange-light');
}

table tr:hover {
  cursor:pointer;
}

.selectAllPad{
  padding-left: 8px !important;
}
mat-paginator {
  display: flex;
  justify-content: start;
}

.alignright {
	float: right;
	margin-top: 8px;
	margin-bottom: 8px;
	padding: 1px 20px;
	font-family: 'Lato';
}

// .alignleft {
//   float: left;
//   padding-left: 2%;
//   padding-top: 0.7%;
// }


// .flexdiv-buttons {
// 	display: flex;
// 	justify-content: space-evenly;
// 	align-items: flex-end;
// 	float: right;
// 	width: 23vw;
// }

// .filter-area {
//   display: flex;
// 	flex-wrap: wrap;
// 	padding-left: 10px;
// 	padding-right: 10px;
// }

// .toggle-button {
//   display: flex;
//   justify-content: space-between;
//   float: right;
//   padding-right: 0.5%;
//   padding-top: 0.7%;
//   flex-direction: row;
//   width: 100%;
//   align-content: center;
//   align-items: center;
// }

.autoRefresh{
  padding-right: 1%;
}
