import { ComponentFixture, TestBed } from '@angular/core/testing';
import { SuccessJiraTicketCreationComponent } from './success-jira-ticket-creation.component';
import { MatSnackBar, MAT_SNACK_BAR_DATA } from '@angular/material/snack-bar';

describe('SuccessJiraTicketCreationComponent', () => {
  let component: SuccessJiraTicketCreationComponent;
  let fixture: ComponentFixture<SuccessJiraTicketCreationComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ SuccessJiraTicketCreationComponent ],
      providers: [
        {provide: MatSnackBar, useValue: {}},
        {provide: MAT_SNACK_BAR_DATA, useValue: {}}
      ]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(SuccessJiraTicketCreationComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
