import { ComponentFixture, TestBed } from '@angular/core/testing';
import { DwhQcComponent } from './dwh-qc.component';
import { MatTable } from '@angular/material/table';
import { MatPaginator } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import { FormBuilder } from '@angular/forms';
import { DWHQCService } from '@loadmonitor/shared/services/dwh-qc.service';
import { MatSnackBar } from '@angular/material/snack-bar';
import { FilterService } from '@dwh/lmx-lib/src/lib/services/filter.service';
import { of } from 'rxjs';
import { FilterTrayCacheService } from '@loadmonitor/shared/services/filter-tray-cache.service';
import { DatePipe } from '@angular/common';

describe('DwhQcComponent', () => {
  let component: DwhQcComponent;
  let fixture: ComponentFixture<DwhQcComponent>;
  let mockDWHQCService: any;
  let mockSnackBar: any;
  let mockFilterService: any;
  let mockFilterTrayCacheService: any;
  let datePipe: jest.Mocked<DatePipe>;

  beforeEach(() => {
    mockDWHQCService = { getAsync: jest.fn() };
    mockSnackBar = { openFromComponent: jest.fn() };
    mockFilterService = { filtersSelected$: of({}) };
    mockFilterTrayCacheService = { getCacheValueData: jest.fn() };
    datePipe = {
      transform: jest.fn(),
    } as any;

    TestBed.configureTestingModule({
      declarations: [DwhQcComponent],
      providers: [
        { provide: DWHQCService, useValue: mockDWHQCService },
        { provide: MatSnackBar, useValue: mockSnackBar },
        { provide: FilterService, useValue: mockFilterService },
        { provide: FilterTrayCacheService, useValue: mockFilterTrayCacheService },
        FormBuilder,
        { provide: DatePipe, useValue: datePipe },
      ],
      imports: [],
    }).compileComponents();

    fixture = TestBed.createComponent(DwhQcComponent);
    component = fixture.componentInstance;
  });

  it('should create the component', () => {
    expect(component).toBeTruthy();
  });
  
});
