import { DetailMenuConfiguration } from "./DetailMenuConfiguration";

export interface DWHRELEASE {
  loadId: number;
  jobType: string;
  qcProjectId: number;
  baseProjectId: number;
  baseProjectName: string;
  baseProjectType: string;
  baseProjectTypeId: number;
  periodId  : number;
  periodDesc: string;
  status: string;
  message: number;
  jobId: string;
  createdBy: string;
  created: Date;
  started: Date;
  finished: Date;
  runtime: Date;
  detailMenuConfig?: DetailMenuConfiguration[];
  isSelected?: boolean;
  isExpanded?: boolean;
  isChecked?: boolean;
}