import { Injectable } from '@angular/core';
import { HttpR<PERSON><PERSON>, <PERSON>ttp<PERSON><PERSON><PERSON>, <PERSON>ttpEvent, HttpInterceptor, HttpErrorResponse } from '@angular/common/http';
import { catchError, EMPTY, Observable, throwError } from 'rxjs';
import { AuthRoute } from '../../constants';
import { Router } from '@angular/router';

export const RETURN_URL = 'returnUrl';

@Injectable()
export class AuthorizationInterceptor implements HttpInterceptor {
	constructor(private router: Router) {}

	intercept(request: HttpRequest<unknown>, next: HttpHandler): Observable<HttpEvent<unknown>> {
		return next.handle(request).pipe(
			catchError((error) => {
				if (error instanceof HttpErrorResponse && error.status === 403) {
					this.router.navigate([`/${AuthRoute.FORBIDDEN}`], { queryParams: { [RETURN_URL]: this.router.url } });
					return EMPTY;
				}
				return throwError(() => error);
			})
		);
	}
}
