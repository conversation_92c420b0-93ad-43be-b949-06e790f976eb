import { EnvironmentModel } from './environment.model';

export const environment: EnvironmentModel = {
  production: false,
  dev: true,
  apiUrl: '',
  spriteIconsPath: 'assets/images/sprites/sprite.svg',
  environments:[
    {
      location:'http://localhost:4200',
      enabled: true
    },
    {
      location:'https://loadmonitor-ui.t1.dwh.in.gfk.com',
      enabled: true
    },
    {
      location:'https://loadmonitor-ui.t5.dwh.in.gfk.com',
      enabled: true
    }
      ,
    {
      location:'https://loadmonitor-ui.t3.dwh.in.gfk.com',
      enabled: true
    }
      ,
    {
      location:'https://loadmonitor-ui.t4.dwh.in.gfk.com',
      enabled: true
    },
    {
      location:'https://loadmonitor.dwh.in.gfk.com',
      enabled: true
    }
  ]
};
