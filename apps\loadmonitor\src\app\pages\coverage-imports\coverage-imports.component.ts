import { JobTypes } from '@loadmonitor/shared/jobTypes';
import { Component, OnInit, ViewChild, ChangeDetectorRef, AfterContentInit, AfterViewInit, Input, OnDestroy, HostListener } from '@angular/core';
import { COMMA, ENTER } from '@angular/cdk/keycodes';
import { FormBuilder, FormGroup } from '@angular/forms';
import { BehaviorSubject,  Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { MatSnackBar } from '@angular/material/snack-bar';
import { DefaultSnackbarComponent } from '@loadmonitor/shared//components/default-snackbar/default-snackbar.component';
import { CoverageImport } from '@loadmonitor/shared//interfaces/CoverageImport';
import { MatTable, MatTableDataSource } from '@angular/material/table';
import { MatSort } from '@angular/material/sort';
import { MatPaginator } from '@angular/material/paginator';
import { CoverageImportService } from '@loadmonitor/shared//services/coverage-import.service';
import { WorkspaceService } from '@loadmonitor/shared/services/workspace.service';
import { SettingsConstants } from '@loadmonitor/shared/settings.constants';
import { CoverageImportsCache } from '@loadmonitor/shared/interfaces/caching/CoverageImportsCache';
import { CreateTicketDialogComponent } from '@loadmonitor/shared/components/create-ticket-dialog/create-ticket-dialog.component';
import { MatDialog } from '@angular/material/dialog';
import { SelectionModel } from '@angular/cdk/collections';
import { Filter, FilterRecords, FilterService, FilterTrayComponent } from '@dwh/lmx-lib/src';
import { environment } from 'apps/loadmonitor/src/environments/environment';
import { DatePipe } from '@angular/common';
import { ToggleButtonComponent } from '@gfk/ng-lib';
import { UserService } from '@loadmonitor/shared/services/user.service';
import * as moment from 'moment';
import { FilterTrayCacheService } from '@loadmonitor/shared/services/filter-tray-cache.service';

@Component({
	selector: 'lm-coverage-imports',
	templateUrl: './coverage-imports.component.html',
	styleUrls: ['./coverage-imports.component.scss'],
})
export class CoverageImportsComponent implements OnInit, AfterContentInit, AfterViewInit, OnDestroy {

	@Input() icon = 'delete';

	@ViewChild(ToggleButtonComponent) togglebuttonComponent: ToggleButtonComponent | undefined;

	@ViewChild(FilterTrayComponent) filterTrayComponent: FilterTrayComponent | undefined;

	@ViewChild(MatSort)
	sort: MatSort = new MatSort();

	@ViewChild(MatTable)
	table!: MatTable<CoverageImport>;

	@ViewChild(MatPaginator)
	paginator!: MatPaginator;

	panelOpenState = false;

	/* *********************
	 * Components variables
	 ********************* */

	jobType: JobTypes = JobTypes.coverageImport;

	//Tag-Chip-List
	readonly separatorKeysCodes = [ENTER, COMMA] as const;


	// Progress Spinner visibility
	isLoading = false;
	public CoverageImportsForm!: FormGroup;
	public cacheObj: CoverageImportsCache = {} as CoverageImportsCache;
	cacheName = 'cache::CoverageImports';
	FilterTrayOpenFlag = true;
	defaultfilter = JSON.parse(localStorage.getItem(this.cacheName + '-DefaultFilter') || '[]');
	filterCount = '';

	EnableRefresh = true;
	modalshow = false;
	currentfilter = JSON.parse(localStorage.getItem('QuickFilter') || '[]').filter(
		(x) => x.user == localStorage.getItem('username') && x.slice == window.location.href.split('/')[3]
	);
	defaultoption = localStorage.getItem(this.cacheName + '-LastFilter') || 'Default';

	IsEnabled = false;
	IsDisabled = false;
	environments = environment.environments;
	cacheInterval!: any;
	toggleInterval!: any;

	timer = 60;
	eventsSubject: Subject<void> = new Subject<void>();
	/* *********************
	 * Components parameters
	 ********************* */

	//Mat-Table
	dataSource = new MatTableDataSource<CoverageImport>();
	selection = new SelectionModel<CoverageImport>(true, []);
	IsSelectAll = false;
	displayedColumns: string[] = [
		'menu',
		'loadId',
		'workspaceName',
		'status',
		'createdBy',
		'created',
		'started',
		'finished',
		'jobId',
		'jeeJobInfo',
		'sessionId',
		'serverIPPort',
		'message',
	];

	readonly date_with_time = SettingsConstants.DATE_FORMAT_WITH_TIME;
	refreshedDateTime!: boolean;
	selectedFilters: any;
	resetForm: Subject<boolean> = new Subject();
	coverageImportsData!: any[];

  @HostListener('document:visibilitychange', ['$event'])
	appVisibility() {
		if (document.hidden) {
			if (this.cacheInterval) {
				clearInterval(this.cacheInterval);
			}
			if (this.toggleInterval) {
				clearInterval(this.toggleInterval);
			}
		} else {
			if (this.cacheObj.isAutoRefresh === true) {
				this.cacheInterval = setInterval(() => {
					if (this.defaultoption != 'Default') {
						this.Apply();
					}
				}, this.timer * 1000);
			} else {
				if (this.cacheInterval !== undefined) clearInterval(this.cacheInterval);
				if (this.toggleInterval !== undefined) clearInterval(this.toggleInterval);
			}
		}
	}
	private destroy$ = new Subject<void>();

	constructor(
		private coverageImportService: CoverageImportService,
		private snackBar: MatSnackBar,
		private formBuilder: FormBuilder,
		private cdRef: ChangeDetectorRef,
		public dialog: MatDialog,
		public filterService: FilterService,
		public datepipe: DatePipe,
		private filterTrayCacheService: FilterTrayCacheService
	) {}

  ngOnDestroy() {
		if (this.cacheInterval) {
			clearInterval(this.cacheInterval);
		}

		if (this.toggleInterval) {
			clearInterval(this.toggleInterval);
		}
		this.destroy$.next();
	    this.destroy$.complete();
	}

	private updateFC(): void {
		if(this.cacheObj){
			this.filterTrayCacheService.getCacheValueData(this.cacheObj);
		}
  	}

	ngOnInit(): void {
		if (this.environments.filter((x) => x.location == window.location.origin)[0]) {
			this.IsEnabled = this.environments.filter((x) => x.location == window.location.origin)[0].enabled;
		} else {
			this.IsEnabled = false;
		}

    this.CoverageImportsForm = this.formBuilder.group({
		 isAutoRefresh: this.togglebuttonComponent?.checked,
		});
    this.dataSource.paginator = this.paginator;
		this.updateFC();
		this.SetFiltersCount();
 }


	ngAfterContentInit() {
		this.afterChildComponentsLoaded();
		this.CreateFilterSummaryChips();
		this.SetFiltersCount();
		this.updateFC();
		if (this.cacheObj.isAutoRefresh === true) {
			if (this.cacheInterval === undefined) {
				this.cacheInterval = setInterval(() => {
					if (this.defaultoption != 'Default') {
						this.Apply();
					}
				}, this.timer * 1000);
			}
		} else {
			if (this.cacheInterval !== undefined) clearInterval(this.cacheInterval);
			if (this.toggleInterval !== undefined) clearInterval(this.toggleInterval);
		}
	}

	ngAfterViewInit() {
		this.cdRef.detectChanges();
	}

	afterChildComponentsLoaded() {
		this.cacheObj = JSON.parse(localStorage.getItem(this.cacheName) || '{}');
	}

	openmodal() {
		this.eventsSubject.next();
	}

	disablesave(status) {
		this.IsDisabled = status;
	}

	toggleChecked(check: any) {
		if (check === true) {
			this.saveInCache(check, true);
			this.toggleInterval = setInterval(() => {
				if (this.defaultoption != 'Default') {
					this.Apply();
				}
			}, this.timer * 1000);
		} else {
			if (this.toggleInterval !== undefined) clearInterval(this.toggleInterval);
			if (this.cacheInterval !== undefined) clearInterval(this.cacheInterval);
			this.saveInCache(check, true);
		}
	}

	applyfilter(selectedvalue) {
		this.resetForm.next(true);
		const oldautotoggle = this.cacheObj.isAutoRefresh;
		selectedvalue = selectedvalue.value ? selectedvalue.value : selectedvalue;
		this.defaultoption = selectedvalue;
		localStorage.setItem(this.cacheName + '-LastFilter', this.defaultoption || 'Default');
		if (selectedvalue == 'Default') {
			if (
				JSON.parse(localStorage.getItem(this.cacheName + '-DefaultFilter') || '[]').filter(
					(x) => x.user == localStorage.getItem('username') && x.slice == window.location.href.split('/')[3]
				)[0]
			) {
				this.cacheObj = JSON.parse(localStorage.getItem(this.cacheName + '-DefaultFilter') || '[]').filter(
					(x) => x.user == localStorage.getItem('username') && x.slice == window.location.href.split('/')[3]
				)[0]['data'];
			}
			localStorage.setItem(this.cacheName, JSON.stringify(this.cacheObj));
		} else {
			this.cacheObj = JSON.parse(localStorage.getItem('QuickFilter') || '[]')
				.filter((x) => x.user == localStorage.getItem('username') && x.slice == window.location.href.split('/')[3])
				.filter((x) => x.filter == selectedvalue)[0]['data'];
			localStorage.setItem(this.cacheName, JSON.stringify(this.cacheObj));
		}
		this.cacheObj.isAutoRefresh = oldautotoggle;
		this.CreateFilterSummaryChips();
    this.selectedFilters = this.cacheObj;

		if (this.cacheObj.selectedDays == '3') {
			const date = new Date();
			this.cacheObj.startDate = new Date(Date.UTC(date.getFullYear(), date.getMonth(), date.getDate() - 2, 0, 0, 0, 0));
			this.cacheObj.endDate = new Date(Date.UTC(date.getFullYear(), date.getMonth(), date.getDate(), 0, 0, 0, 0));
		} else if (this.cacheObj.selectedDays == '2') {
			const date = new Date();
			this.cacheObj.startDate = new Date(Date.UTC(date.getFullYear(), date.getMonth(), date.getDate(), 0, 0, 0, 0));
			this.cacheObj.endDate = new Date(Date.UTC(date.getFullYear(), date.getMonth(), date.getDate(), 0, 0, 0, 0));
		}
		setTimeout(() => {
			this.updateFC();
			this.Apply();
		}, 200);

		localStorage.setItem(this.cacheName, JSON.stringify(this.cacheObj));
	}

	ConfirmApply(autoRefreshStatus) {
		this.saveInCache(autoRefreshStatus, false);
		this.CreateFilterSummaryChips();

		let count;
		this.filterService.filtersSelected$.subscribe((selectedFilters: FilterRecords) => {
			count = Number(Object.keys(selectedFilters).length);
		});

		if (count > 0) {
			this.Apply();
		} else {
			if (confirm('Loading results without any filters selected will take some time to load. Are you sure you want to proceed?')) {
				this.Apply();
			}
		}
	}

	usersFilterSummary() {
		this.filterService.setFilters({
			userIds: {
				name: 'User',
				values: this.cacheObj.users?.map((i) => i.name),
				slice: window.location.href.split('/')[3],
			} as Filter,
		});
	}

	jeeJobIdsFilterSummary() {
		this.filterService.setFilters({
			JeeJobIds: {
				name: 'Jee Job IDs',
				values: this.cacheObj.jobIds,
				slice: window.location.href.split('/')[3],
			} as Filter,
		});
	}

	exportsIdsFilterSummary() {
		this.filterService.setFilters({
			exportsIds: {
				name: 'Exports IDs',
				values: this.cacheObj.exportIds,
				slice: window.location.href.split('/')[3],
			} as Filter,
		});
	}

	datesFilterSummary(dateStart: any, dateEnd: any) {
		if (dateStart != null || dateEnd != null) {
			const start = dateStart == 'Invalid Date' || dateStart == null ? '' : this.datepipe.transform(dateStart, 'yyyy-MM-dd');
			const end = dateEnd == 'Invalid Date' || dateEnd == null ? '' : this.datepipe.transform(dateEnd, 'yyyy-MM-dd');
			this.filterService.setFilters({
				dates: {
					name: 'Date',
					values: (start !== undefined && end !== undefined) ? [start, end] : null,
					slice: window.location.href.split('/')[3],
				} as Filter,
			});
		} else {
			this.filterService.setFilters({
				dates: {
					name: 'Date',
					values: null,
					slice: window.location.href.split('/')[3],
				} as unknown as Filter,
			});
		}
	}

	statusFilterSummary() {
		this.filterService.setFilters({
			statusIds: {
				name: 'Status',
				values: this.cacheObj.status,
				slice: window.location.href.split('/')[3],
			} as Filter,
		});
	}

	workspaceFilterSummary() {
		this.filterService.setFilters({
			workspace: {
				name: 'Workspace',
				values: this.cacheObj.workspaceIds,
				slice: window.location.href.split('/')[3],
			} as Filter,
		});
	}

	CreateFilterSummaryChips() {
		if (this.cacheObj && Object.keys(this.cacheObj).length) {
			this.usersFilterSummary();
			this.jeeJobIdsFilterSummary();
			this.exportsIdsFilterSummary();
			this.statusFilterSummary();
			this.workspaceFilterSummary();
			this.datesFilterSummary(this.cacheObj.startDate, this.cacheObj.endDate);
		}
	}

	setFormValue(autoRefreshStatus) {
		this.CoverageImportsForm.setValue({
			isAutoRefresh: autoRefreshStatus === undefined || autoRefreshStatus === false ? false : true,
		});
	}

	saveInCache(autoRefreshStatus, AutoToggle) {
		this.setFormValue(autoRefreshStatus);
		localStorage.setItem(this.cacheName, JSON.stringify(this.selectedFilters));
		this.cacheObj = JSON.parse(localStorage.getItem(this.cacheName) || '{}');
		if (!AutoToggle) {
			this.defaultfilter = [];
			this.defaultfilter.push({
				data: this.selectedFilters,
				slice: window.location.href.split('/')[3],
				user: localStorage.getItem('username'),
			});
			localStorage.setItem(this.cacheName + '-DefaultFilter', JSON.stringify(this.defaultfilter));
			let nofilter = 0;
			let selectedfilter = '';
			this.currentfilter.forEach((i) => {
	        // if (i.filter == this.defaultoption) {
			if(JSON.stringify(i.data) == JSON.stringify(this.cacheObj)){
					nofilter++;
					selectedfilter = i.filter;
				}
			});
			if (nofilter == 0) {
				this.defaultoption = 'Default';
			} else {
				this.defaultoption = selectedfilter;
			}
		}
		localStorage.setItem(this.cacheName + '-LastFilter', this.defaultoption);
		this.SetFiltersCount();
		this.updateFC();
	}

	saveInQuickFilter(autoRefreshStatus, filterName) {
		this.setFormValue(autoRefreshStatus);
		this.currentfilter = JSON.parse(localStorage.getItem('QuickFilter') || '[]');
		this.currentfilter.push({
			data: this.selectedFilters,
			filter: filterName,
			slice: window.location.href.split('/')[3],
			user: localStorage.getItem('username'),
		});
		this.defaultoption = filterName;
		const oldcache = this.cacheObj;
		const olddefault = localStorage.getItem(this.cacheName + '-DefaultFilter');
		this.ConfirmApply(autoRefreshStatus);
		this.cacheObj = oldcache;
		localStorage.setItem(this.cacheName + '-DefaultFilter', olddefault || '[]');
		localStorage.setItem('QuickFilter', JSON.stringify(this.currentfilter));
		localStorage.setItem(this.cacheName + '-LastFilter', filterName);
		this.SetFiltersCount();
	}

	public isShownFilterTrayEventHandler($event: boolean) {
		this.filterTrayComponent?.toggleShowTray($event);
		this.FilterTrayOpenFlag = $event;
	}

	getWidth() {
		this.FilterTrayOpenFlag = !this.FilterTrayOpenFlag;
		return '100%';
	}


	Apply(): void {
		this.refreshedDateTime = false;
		this.isLoading = true;
		this.destroy$.next();	
		this.filterTrayComponent?.showSpinner(true);
		this.coverageImportService
			.getAsync(
        this.selectedFilters.jobIds,
        this.selectedFilters.exportIds,
        this.selectedFilters.workspaceIds?.map((workspace) => workspace),
        this.selectedFilters.users?.map((users) => users.userName),
        this.selectedFilters.status?.map((status) => status),
        this.selectedFilters.startDate ? this.selectedFilters.startDate : null,
				this.selectedFilters.endDate ? moment(this.selectedFilters.endDate).format().split('T')[0] + 'T23:59:59.999Z' : null,
			  )
			.pipe(
				takeUntil(this.destroy$)
			)
			.subscribe((result) => {
				this.refreshedDateTime = true;
				if (result.moreRecordsAvailable) {
					this.notify('More than 1000 results exist!');
				}

				if (result.count === 0) {
					this.notify('No result found!');
					this.coverageImportsData = [];
				}
				this.filterTrayComponent?.showSpinner(false);
				this.isLoading = false;
				this.coverageImportsData = result.records;
				this.dataSource = new MatTableDataSource<CoverageImport>(result.records);
				this.dataSource.sort = this.sort;
				this.paginator.pageIndex = 0;
				this.dataSource.paginator = this.paginator;
				this.table.dataSource = this.dataSource;
			},
			(error) => {
				if (error.status === 504) {
					this.notify('Gateway Timeout. The server took too long to respond. Please try again later.');
				} 
				else {
					this.notify(`An error occurred: ${error.message}`);
				}
				this.isLoading = false;
				this.filterTrayComponent?.showSpinner(false);
			});
		this.SetFiltersCount();
	}

	private notify(message: string) {
		this.snackBar.openFromComponent(DefaultSnackbarComponent, {
			duration: 10 * 1000,
			data: message,
		});
	}

	openJiraTicket(): void {
		const checkedList = this.dataSource.data.filter((i) => i.isChecked);
		const dialogRef = this.dialog.open(CreateTicketDialogComponent, {
			width: '1000px',
			height: '487px',
			data: {
				messages: checkedList.map((i) => {
					return i.message;
				}),
				jobType: this.jobType,
				title: 'CoverageImports',
			},
		});

		dialogRef.afterClosed().subscribe((resSubmit) => {
			if (resSubmit === true) {
				this.dataSource.data.forEach((i) => (i.isChecked = false));
				this.selection.clear();
				this.IsSelectAll = false;
			}
		});
	}

	rowToggleSelection(ev, item) {
		item.isChecked = ev.checked;
	}

	checkAtleastOneRowChecked() {
		return this.dataSource.data.filter((i) => i.isChecked).length > 0;
	}

	selectRows(selectrow) {
		this.IsSelectAll = !this.IsSelectAll;
		let endIndex: number;
		const page = this.dataSource.paginator !== null ? this.dataSource.paginator.pageSize : 0;
		const pageindex = this.dataSource.paginator !== null ? this.dataSource.paginator.pageIndex : 0;

		if (!selectrow.checked) {
			this.selection.clear();
			if (this.dataSource.data.length > (pageindex + 1) * page) {
				endIndex = (pageindex + 1) * page;
			} else {
				endIndex = this.dataSource.data.length;
			}

			for (let index = pageindex * page; index < endIndex; index++) {
				const row = this.dataSource.data[index];
				this.selection.deselect(row);
				this.dataSource.data[index].isChecked = false;
			}
		} else {
			if (this.dataSource.data.length > (pageindex + 1) * page) {
				endIndex = (pageindex + 1) * page;
			} else {
				endIndex = this.dataSource.data.length;
			}
			for (let index = pageindex * page; index < endIndex; index++) {
				const row = this.dataSource.sortData(this.dataSource.data, this.sort)[index];
				this.selection.select(row);
				this.dataSource.sortData(this.dataSource.data, this.sort)[index].isChecked = true;
			}
		}
	}

	isSelectedPage() {
		const numSelected = this.selection.selected.length;
		const page = this.dataSource.paginator !== null ? this.dataSource.paginator.pageSize : 0;
		const pageindex = this.dataSource.paginator !== null ? this.dataSource.paginator.pageIndex : 0;
		let endIndex: number;
		if (this.dataSource.data.length > (pageindex + 1) * page) {
			endIndex = (pageindex + 1) * page;
		} else {
			endIndex = this.dataSource.data.length - pageindex * page;
		}

		return numSelected === endIndex;
	}

	SetFiltersCount() {
		let count;
		this.filterService.filtersSelected$.subscribe((selectedFilters: FilterRecords) => {
			count = Number(Object.keys(selectedFilters).length);
		});
		if (count > 0) {
			this.filterCount = '(' + count.toString() + ')';
		} else {
			this.filterCount = '';
		}
	}

	filterSummaryChipClear() {
		if (confirm('This will remove all of your selected filters and could not be reversed. Are you sure you want to do that?')) {
      		this.resetForm.next(true);
			  this.filterTrayCacheService.getCacheNameData(this.cacheName);
		}
		this.saveInCache(this.cacheObj.isAutoRefresh, false);
		this.CreateFilterSummaryChips();
		this.SetFiltersCount();
	}

  getSelectedFilters(selectedFilters: any){
		this.cacheObj = selectedFilters;
		this.selectedFilters = selectedFilters;
	}
}
