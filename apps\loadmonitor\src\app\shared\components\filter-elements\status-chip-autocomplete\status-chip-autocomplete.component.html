<mat-form-field class="chip-list" appearance="outline">
  <mat-label>Status </mat-label>
  <mat-chip-list class="chip-list-wrapper" #chipListStatus aria-label="Status selection">
    <mat-chip *ngFor="let item of selectedStatusIds | async" [selectable]="true" [removable]="true" (removed)="removeStatusChips(item)">
      {{ item.description }}
      <mat-icon matChipRemove *ngIf="true">cancel</mat-icon>
    </mat-chip>
    <input placeholder="New ..." #statusIdsInput [formControl]="statusIdsCtrl" [matAutocomplete]="autoStatus"
      [matChipInputFor]="chipListStatus" [matChipInputSeparatorKeyCodes]="separatorKeysCodes"
      class="js-filter-inp-status" />
  </mat-chip-list>
  <mat-autocomplete #autoStatus="matAutocomplete" [autoActiveFirstOption]="true">
    <mat-option *ngFor="let item of filteredStatusIds | async" [value]="item">
      <div (click)="optionClicked($event, item)">
        <mat-checkbox class="option-checkbox" [checked]="item.selected" (change)="toggleSelection(item)"
          (click)="$event.stopPropagation()">
          {{ item.description }}
        </mat-checkbox>
      </div>
    </mat-option>
  </mat-autocomplete>
</mat-form-field>