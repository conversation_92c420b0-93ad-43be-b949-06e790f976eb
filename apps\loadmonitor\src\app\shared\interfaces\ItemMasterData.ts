import { DetailMenuConfiguration } from "./DetailMenuConfiguration";

export interface ItemMasterData {
  prepUnitId: number;
  imlJobType: number;
  loadUnitId: number;
  status: string;
  productGroupId: number;
  productGroup: string;
  mode: string;
  jobId: number;
  jobPriority: number;
  jobLoops: number;
  createdBy: string;
  created: Date;
  started: Date;
  finished: Date;
  message?: string;
  runtime: string; // TODO: time datatype????
  detailMenuConfig?: DetailMenuConfiguration[];
  isChecked: boolean;
}
