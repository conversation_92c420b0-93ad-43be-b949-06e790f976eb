import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { HttpClient } from '@angular/common/http';
import { ConfigService } from './config.service';
import { Workspace } from '@loadmonitor/shared/interfaces/Workspace';
import { shareReplay } from 'rxjs/operators';

@Injectable({
  providedIn: 'root',
})
export class WorkspaceService {
  private url!:string;
  
  private cachedWorkspace! : Observable<Workspace[]>;
  constructor(private http: HttpClient, private config: ConfigService) {
    this.url = `${this.config.getApiUrl()}/workspace`;
  }

  getAsync(): Observable<Workspace[]> {
    if(!this.cachedWorkspace)
    {
      this.cachedWorkspace = this.http
        .get<Workspace[]>(this.url)
        .pipe(shareReplay(1));
    }
    return this.cachedWorkspace;
  }
}
