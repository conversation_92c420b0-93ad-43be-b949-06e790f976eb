{"count": 4, "moreRecordsAvailable": false, "records": [{"runId": 36863, "status": "EXPORTED", "ruleSetName": "HyperChangeLob5_Super_DIY", "ruleSetId": 7226, "scopeType": "Reporting Groups / LOBs", "sourceComponentId": 1234, "targetComponentId": 2345, "createdBy": "<PERSON><PERSON>d", "created": "2019-06-19T10:09:07", "started": "2019-06-19T10:09:07", "finished": "2019-06-19T10:09:07", "message": "Something went wrong"}, {"runId": 36857, "status": "EXPORTED", "ruleSetName": "Blabalab<PERSON>", "ruleSetId": 7225, "scopeType": "Reporting Groups / LOBs", "sourceComponentId": 1235, "targetComponentId": 2365, "createdBy": "<PERSON><PERSON><PERSON>", "created": "2019-06-18T10:09:07", "started": "2019-06-18T10:09:07", "finished": "2019-06-18T10:09:07", "message": ""}, {"runId": 36963, "status": "EXPORTED", "ruleSetName": "HyperBlaBla_Super_DIY", "ruleSetId": 7326, "scopeType": "Reporting Groups / LOBs", "sourceComponentId": 1534, "targetComponentId": 2445, "createdBy": "xxxx", "created": "2019-06-01T10:09:07", "started": "2019-06-01T10:09:07", "finished": "2019-06-01T10:09:07", "message": "This is only a test record and not present in the real database"}, {"runId": 36883, "status": "EXPORTED", "ruleSetName": "XXX_XXX", "ruleSetId": 8226, "scopeType": "Reporting Groups / LOBs", "sourceComponentId": 1734, "targetComponentId": 3345, "createdBy": "testuser", "created": "2019-05-19T10:09:07", "started": "2019-05-19T10:09:07", "finished": "2019-05-19T10:09:07", "message": ""}]}