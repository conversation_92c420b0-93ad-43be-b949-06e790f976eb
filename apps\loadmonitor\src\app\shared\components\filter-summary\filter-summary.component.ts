import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { Filter, FilterRecords, FilterService } from '@dwh/lmx-lib/';
import { HelperService } from '@loadmonitor/shared/services/helper.service';

@Component({
  selector: 'lm-filter-summary',
  templateUrl: './filter-summary.component.html',
  styleUrls: ['./filter-summary.component.scss'],
})
export class FilterSummaryComponent implements OnInit {

  @Input() icon = 'delete';
  selectedFilters?: Filter[];
  @Output() resetFilters: EventEmitter<any> = new EventEmitter<any>();

  constructor(
    public filterService: FilterService,
    public util: HelperService,
  ) {
  }

  ngOnInit() {
    this.filterService.filtersSelected$.subscribe((selectedFilters: FilterRecords) => {
      this.selectedFilters = Object.keys(selectedFilters).map((key) => {
        const values = selectedFilters[key].values.map((item) => item.name || item);
        return { ...selectedFilters[key], values };
      });
    });
  }

  removeFilter(filterName) {
      this.filterService.resetFilter(filterName.name);
      this.resetFilters.emit(filterName);
      this.util.sendSelectedSectors([]);
      this.util.sendSelectedCategories([]);
  }
}

