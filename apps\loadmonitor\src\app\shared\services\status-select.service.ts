import { Injectable } from '@angular/core';
import { Status } from '../interfaces/Status';

@Injectable({
	providedIn: 'root',
})
export class StatusSelectService {
	private statusId: number[] = [];
	private statusDescription: string[] = [];
	private statusProcessIds: number[] = [];

	getSelectedStatusIds(status: Status[]): number[] {
		this.statusId = [];
		if (status) {
			status.forEach((i) => {
				this.statusId.push(i.id);
			});
			return this.statusId;
		} else return [];
	}

	getSelectedStatusNames(status: Status[]): string[] {
		this.statusDescription = [];
		if (status) {
			status.forEach((i) => {
				this.statusDescription.push(i.description);
			});
			return this.statusDescription;
		} else return [];
	}
}
