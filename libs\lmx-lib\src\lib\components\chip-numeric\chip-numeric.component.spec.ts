import { ComponentFixture, TestBed } from '@angular/core/testing';

import { ChipNumericComponent } from './chip-numeric.component';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';

describe('ChipNumericComponent', () => {
	let component: ChipNumericComponent;
	let fixture: ComponentFixture<ChipNumericComponent>;

	beforeEach(async () => {
		await TestBed.configureTestingModule({
			imports: [
				NoopAnimationsModule,
				ChipNumericComponent
			],
		}).compileComponents();

		fixture = TestBed.createComponent(ChipNumericComponent);
		component = fixture.componentInstance;
		fixture.detectChanges();
	});

	it('should create', () => {
		expect(component).toBeTruthy();
	});
});
