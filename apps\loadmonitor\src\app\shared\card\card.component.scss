@use "@gfk/style" as gfk-style;
 
  .card {
    box-shadow: 0 4px 8px 0 rgb(0 0 0 / 5%);
    padding: 15px 10px;
    background-color: white;
    border-radius: 2%;
    min-height: 160px;
    max-height: 210px;
    max-width: 439px;
    transition: 0.5s all ease-in-out;
    margin: 0 auto;
    position: relative;
    width:100%;

    h3{
        padding-bottom:10px ;
        border-bottom: 1px solid gfk-style.$grey-300;
    }
  }
  .card:hover{
      -ms-transform: scale(1.5); /* IE 9 */
      -webkit-transform: scale(1.5); /* Safari 3-8 */
      transform: scale(1.1); 
  }

  .action-div {
      float: right;
      padding: 8px 16px;
      font-size: 12px;
      a {
        background-color: gfk-style.$brand;
        color: white;
        text-decoration: none;
        padding: 8px;
        border-radius: 10%;
    }
  }
.card-body{
    padding: 0px 15px;
}

.description {
    color: gfk-style.$grey-600;
    padding: 0px 0px 15px 0px;
    line-height: 1.5;
    display: inline;
    position: relative;
    font-size: 13.3px;
}
  .card-title {
    font-size:18px;
  }
  .card-title:hover{
    color: #e55a00;
  }
  .card-footer{
    position:absolute;
    bottom:0;
    width:90%;
    height:60px; 
  }

  .tooltip {
    position: relative;
    display: inline-block;
    border-bottom: 1px dotted black;
    border: none;
  }
  
  .tooltip .tooltiptext {
    visibility: hidden;
    width: 300px;
    background-color: #f5f6f7;
    color: gfk-style.$grey-500;
    border-radius: 5px;
    box-shadow: 0 4px 8px 0 rgb(0 0 0 / 25%);
    padding: 20px;
    /* Position the tooltip */
    position: fixed;
    z-index: 1;
    bottom: 20px;
    line-height: 20px;
    left: 20px;
    cursor: pointer;
  }
  
  .tooltip:hover .tooltiptext {
    visibility: visible;
  }

  .is_disabled{
    background-color: gfk-style.$grey-300 !important;
  }
.title_is_disabled{
  color: gfk-style.$grey-300 !important;
}
