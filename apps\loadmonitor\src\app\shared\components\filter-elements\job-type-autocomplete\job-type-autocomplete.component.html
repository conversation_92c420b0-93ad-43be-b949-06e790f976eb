<mat-form-field class="chip-list" appearance="outline">
  <mat-label>Job Type </mat-label>
  <mat-chip-list class="chip-list-wrapper" #chipListJobType aria-label="JobType selection">
    <mat-chip *ngFor="let item of selectedJobTypeIds | async"
        [selectable]="true"
        [removable]="true"
      (removed)="removeJobTypeChips(item)">
      {{ item.name }}
      <mat-icon matChipRemove>cancel</mat-icon>
    </mat-chip>
    <input placeholder="New ..." #jobTypeIdsInput [formControl]="jobTypeIdsCtrl" [matAutocomplete]="autoJobType"
      [matChipInputFor]="chipListJobType" [matChipInputSeparatorKeyCodes]="separatorKeysCodes"
      class="js-filter-inp-jobType"/>
  </mat-chip-list>
  <mat-autocomplete #autoJobType="matAutocomplete"  [autoActiveFirstOption]="true">
    <mat-option *ngFor="let item of filteredJobTypeIds | async"  [value]="item">
      <div (click)="optionClicked($event, item)">
        <mat-checkbox class="option-checkbox" [checked]="item.selected" (change)="toggleSelection(item)" (click)="$event.stopPropagation()">
          {{ item.name }}
        </mat-checkbox>
      </div>
    </mat-option>
  </mat-autocomplete>
</mat-form-field>
