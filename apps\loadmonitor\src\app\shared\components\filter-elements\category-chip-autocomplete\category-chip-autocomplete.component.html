<mat-form-field class="chip-list" appearance="outline">
  <mat-label>Category </mat-label>
  <mat-chip-list class="chip-list-wrapper" #chipListCategory aria-label="Category selection">
    <mat-chip *ngFor="let item of categoryIds  | async" [selectable]="true" [removable]="true"
      (removed)="removeCategoryChips(item)">
      {{ item.name }} ({{ item.id }})
      <mat-icon matChipRemove>cancel</mat-icon>
    </mat-chip>
    <input placeholder="New ..." #categoryIdsInput [formControl]="categoryIdsCtrl" [matAutocomplete]="autoCategory"
      [matChipInputFor]="chipListCategory" [matChipInputSeparatorKeyCodes]="separatorKeysCodes"
      class="js-filter-inp-category" />
  </mat-chip-list>
  <mat-autocomplete #autoCategory="matAutocomplete" [autoActiveFirstOption]="true" (optionSelected)="selectedCategoryChips($event)">
    <mat-option *ngFor="let item of filteredCategoryIds | async" [value]="item">
      <div (click)="optionClicked($event, item)">
        <mat-checkbox class="option-checkbox" [checked]="item.selected" (change)="toggleSelection(item)"
          (click)="$event.stopPropagation()">
          {{ item.name }} ({{ item.id }})
        </mat-checkbox>
      </div>
    </mat-option>
  </mat-autocomplete>
</mat-form-field>
