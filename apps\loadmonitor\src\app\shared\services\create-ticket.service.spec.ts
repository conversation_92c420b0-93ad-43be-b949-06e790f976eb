import { TestBed, waitForAsync } from '@angular/core/testing';
import { HttpClientTestingModule, HttpTestingController, TestRequest } from '@angular/common/http/testing';
import { HttpClient } from '@angular/common/http';
import { Ticket } from '../interfaces/Ticket';
import { of } from 'rxjs';
import { ConfigService } from './config.service';
import { CreateTicketService } from './create-ticket.service';

describe('CreateTicketService', () => {
const mockApiUrl = '/api/v1/Ticket/LdGeneralIssue';
  const mockRequestBody = {

    issueType: "LdGeneralIssue",
    summary: "summary",
    description: "desc",
    loadDefinitionIds: [1]

   
  };
  const mockCreateTicket: Ticket[] = [
    {
      id: 1,
      name: "STA-1",
      url: "https://jira.gfk.com/browse/STA-1"
    }
  ];


  let service: CreateTicketService;
  let configService: ConfigService;
  let httpTestingController: HttpTestingController;
  let httpClient: HttpClient;

  beforeEach(() => {
TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [ConfigService]
    });

    service = TestBed.inject(CreateTicketService);
    configService = TestBed.inject(ConfigService);

    httpTestingController = TestBed.inject(HttpTestingController);
    httpClient = TestBed.inject(HttpClient);
  });

  afterEach(() => {
    httpTestingController.verify();
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

describe('getAsync() [Method]', () => {
    it('should exist', () => {
      jest.spyOn(service, 'getJiraTicketAsync');

      expect(service.getJiraTicketAsync).toBeTruthy();
    });

    it('should be called with POST method', waitForAsync(() => {
        jest.spyOn(httpClient, 'post');
        httpClient.post(mockApiUrl, mockCreateTicket).subscribe();

        const request: TestRequest = httpTestingController.expectOne(mockApiUrl);

        expect(request.request.method).toEqual('POST');

        request.flush(null);
      })
    );

    it('should return bcr rework data', waitForAsync(() => {
      jest.spyOn(httpClient, 'post').mockReturnValue(of(mockCreateTicket));

      httpClient.post(mockApiUrl, mockRequestBody).subscribe((bcrReworks) => {
        expect(bcrReworks).toEqual(mockCreateTicket);
      });
    }));
  });
});
