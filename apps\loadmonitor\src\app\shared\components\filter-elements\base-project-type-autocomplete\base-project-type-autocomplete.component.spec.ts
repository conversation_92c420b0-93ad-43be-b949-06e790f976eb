import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';
import { SharedModule } from '@loadmonitor/shared/shared.module';
import { COMMA, ENTER } from '@angular/cdk/keycodes';
import { BaseProjectTypeAutocompleteComponent } from './base-project-type-autocomplete.component';
import { MatAutocompleteSelectedEvent } from '@angular/material/autocomplete';
import { of } from 'rxjs';

describe('BaseProjectTypeAutocompleteComponent', () => {
  let component: BaseProjectTypeAutocompleteComponent;
  let fixture: ComponentFixture<BaseProjectTypeAutocompleteComponent>;
  const mockItem = { id: 1, name: 'Industry', selected: false };

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [BaseProjectTypeAutocompleteComponent],
      imports: [SharedModule, NoopAnimationsModule],
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(BaseProjectTypeAutocompleteComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('fieldName [Input]', () => {
    it('should have empty string as default value', () => {
      expect(component.fieldName).toEqual('');
    });
  });

  describe('baseProjectTypeIdsInput [ViewChild]', () => {
    it('should be defined', () => {
      expect(component.baseProjectTypeIdsInput).toBeDefined();
    });
  });

  describe('baseProjectTypeIds [Property]', () => {
    it('should have empty array as default value', () => {
      component.baseProjectTypeIds.subscribe(
        (res) => {
          expect(res).toEqual(of([]));
        },
        () => fail('fail!')
      );
    });
  });

  describe('separatorKeysCodes [Property]', () => {
    it('should have correct value as default value', () => {
      const mockSeparatorKeysCodes = [ENTER, COMMA];

      expect(component.separatorKeysCodes).toEqual(mockSeparatorKeysCodes);
    });
  });

  describe('baseProjectTypeIdsCtrl [FormControl]', () => {
    it('should be defined', () => {
      expect(component.baseProjectTypeIdsCtrl).toBeDefined();
    });
  });

  describe('filteredBaseProjectTypeIds [Property]', () => {
    it('should be defined', () => {
      expect(component.filteredBaseProjectTypeIds).toBeDefined();
    });
  });

  describe('allBaseProjectTypeIds [Property]', () => {
    it('should be defined', () => {
      expect(component.allBaseProjectTypeIds).toBeDefined();
    });
  });

  describe('ngOnInit() [Method]', () => {
    beforeEach(() => {
      jest.spyOn(component, 'ngOnInit');
      jest.spyOn<BaseProjectTypeAutocompleteComponent, any>(
        component,
        'setBaseProjectTypeFilter'
      );
    });

    it('should call fillBaseProjectTypes() [Method]', () => {
      jest
        .spyOn(component, 'fillBaseProjectTypes')
        .mockImplementation(() => true);

      component.ngOnInit();

      expect(component.fillBaseProjectTypes).toHaveBeenCalled();
    });
  });

  describe('fillBaseProjectTypes() [Method]', () => {
    beforeEach(() => {
      jest.spyOn(component, 'fillBaseProjectTypes');
      jest
        .spyOn(component.baseProjectTypeIdsCtrl, 'setValue')
        .mockImplementation(() => true);
    });

    it('should set correct value to allBaseProjectTypeIds [Property]', () => {
      const mockAllBaseProjectTypeIds = [
        { id: 4, name: 'IDAS DST', selected: false },
        { id: 1, name: 'Industry', selected: false },
        { id: 3, name: 'Industry/Retailer', selected: false },
        { id: 2, name: 'Retailer', selected: false },
      ];

      component.allBaseProjectTypeIds = [];

      component.fillBaseProjectTypes();

      expect(component.allBaseProjectTypeIds).toEqual(
        mockAllBaseProjectTypeIds
      );
    });
  });

  describe('setSelectedBpTypes() [Method]', () => {
    const mockAllBaseProjectTypeIds = [
      { id: 4, name: 'IDAS DST', selected: false },
      { id: 1, name: 'Industry', selected: false },
      { id: 3, name: 'Industry/Retailer', selected: false },
      { id: 2, name: 'Retailer', selected: false },
    ];

    beforeEach(() => {
      jest.spyOn(component, 'setSelectedBpTypes');
      component.allBaseProjectTypeIds = mockAllBaseProjectTypeIds;
    });
    it('should add new object to allBaseProjectTypeIds [Property]', () => {
      const mockPeriodicityIds = [5];
      const mockResult = { id: 5, name: 'Loading...', selected: true };

      component.setSelectedBpTypes(mockPeriodicityIds);

      expect(component.allBaseProjectTypeIds).toHaveLength(5);
    });

    it('should update selected in specific type element in allBaseProjectTypeIds [Property]', () => {
      const mockPeriodicityIds = [4];
      component.setSelectedBpTypes(mockPeriodicityIds);

      expect(component.allBaseProjectTypeIds[0].selected).toEqual(true);
    });
  });

  describe('removeBaseProjectTypeChips() [Method]', () => {
    it('should call toggleSelection() [Method] with correct args', () => {
      jest.spyOn(component, 'removeBaseProjectTypeChips');
      jest.spyOn(component, 'toggleSelection');

      component.removeBaseProjectTypeChips(mockItem);

      expect(component.toggleSelection).toHaveBeenCalledWith(mockItem);
    });
  });

  describe('optionClicked() [Method]', () => {
    const mockMouseClickEvent = new MouseEvent('click', {
      view: window,
      bubbles: true,
      cancelable: true,
      clientX: 20,
    });

    beforeEach(() => {
      jest.spyOn(component, 'optionClicked');
      jest.spyOn(component, 'toggleSelection').mockImplementation(() => true);
    });

    it('should call event.stopPropagation', () => {
      mockMouseClickEvent.stopPropagation = jest.fn();

      component.optionClicked(mockMouseClickEvent, mockItem);

      expect(mockMouseClickEvent.stopPropagation).toHaveBeenCalled();
    });

    it('should call toggleSelection() [Method] with correct args', () => {
      component.optionClicked(mockMouseClickEvent, mockItem);

      expect(component.toggleSelection).toHaveBeenCalledWith(mockItem);
    });
  });

  // describe('refreshSuggestionsAndEmptyInput() [Method]', () => {
  //   beforeEach(() => {
  //     jest.spyOn(component, 'toggleSelection');
  //     jest.spyOn(component, 'refreshSuggestionsAndEmptyInput');
  //     jest
  //       .spyOn(component, 'refreshSuggestionsAndEmptyInput')
  //       .mockImplementation(() => true);
  //   });

  //   it('should add the selected item to baseProjectTypeIds() [Method] array', () => {
  //     const mockToggleSelectionItem = {
  //       id: 1,
  //       name: 'Industry',
  //       selected: false,
  //     };

  //     component.baseProjectTypeIds = of([]);

  //     component.toggleSelection(mockToggleSelectionItem);

  //     expect(component.baseProjectTypeIds).toEqual([mockToggleSelectionItem]);
  //   });

  //   it('should remove the deselected item from baseProjectTypeIds() [Method] array', () => {
  //     const mockToggleSelectionItem = {
  //       id: 1,
  //       name: 'Industry',
  //       selected: true,
  //     };
  //     const mockResult = [
  //       { id: 3, name: 'Industry/Retailer', selected: false },
  //       { id: 2, name: 'Retailer', selected: false },
  //     ];

  //     component.baseProjectTypeIds = of([
  //       ...mockResult,
  //       mockToggleSelectionItem,
  //     ]);

  //     component.toggleSelection(mockToggleSelectionItem);

  //     expect(component.baseProjectTypeIds).toEqual(mockResult);
  //   });
  // });

  describe('refreshSuggestionsAndEmptyInput() [Method]', () => {
    beforeEach(() => {
      jest.spyOn(component, 'refreshSuggestionsAndEmptyInput');
      jest
        .spyOn(component.baseProjectTypeIdsCtrl, 'setValue')
        .mockImplementation();
    });

    it('should set empty string as value to baseProjectTypeIdsInput [ViewChild]', () => {
      component.baseProjectTypeIdsInput.nativeElement.value = 'Random text';

      component.refreshSuggestionsAndEmptyInput();

      const result = component.baseProjectTypeIdsInput.nativeElement.value;

      expect(result).toEqual('');
    });

    it('should call baseProjectTypeIdsCtrl [FormControl] setValue with correct args', () => {
      component.refreshSuggestionsAndEmptyInput();

      expect(
        component.baseProjectTypeIdsCtrl.setValue
      ).toHaveBeenLastCalledWith(null, {});
    });
  });

  describe('selectedBaseProjectTypeChips() [Method]', () => {
    const mockEvent = {
      option: {
        value: mockItem,
      },
    } as MatAutocompleteSelectedEvent;

    beforeEach(() => {
      jest.spyOn(component, 'selectedBaseProjectTypeChips');
      jest.spyOn(component, 'toggleSelection');
      jest
        .spyOn(component.baseProjectTypeIdsCtrl, 'setValue')
        .mockImplementation();
    });
    it('should call toggleSelection() [Method] with correct args', () => {
      component.selectedBaseProjectTypeChips(mockEvent);

      expect(component.toggleSelection).toHaveBeenCalledWith(mockItem);
    });

    it('should set empty string as value to baseProjectTypeIdsInput [ViewChild]', () => {
      component.baseProjectTypeIdsInput.nativeElement.value = 'Random text';

      component.selectedBaseProjectTypeChips(mockEvent);

      const result = component.baseProjectTypeIdsInput.nativeElement.value;

      expect(result).toEqual('');
    });

    it('should call baseProjectTypeIdsCtrl [FormControl] setValue with correct args', () => {
      component.selectedBaseProjectTypeChips(mockEvent);

      expect(
        component.baseProjectTypeIdsCtrl.setValue
      ).toHaveBeenLastCalledWith(null, {});
    });
  });

  describe('setBaseProjectTypeFilter() [Method]', () => {
    const mockAllBaseProjectTypeIds = [
      { id: 4, name: 'IDAS DST', selected: false },
      { id: 1, name: 'Industry', selected: false },
      { id: 3, name: 'Industry/Retailer', selected: false },
      { id: 2, name: 'Retailer', selected: false },
    ];

    beforeEach(() => {
      jest.spyOn<BaseProjectTypeAutocompleteComponent, any>(
        component,
        'setBaseProjectTypeFilter'
      );
      component.allBaseProjectTypeIds = mockAllBaseProjectTypeIds;
    });

    it('should return allBaseProjectTypeIds if is called with no arg', () => {
      jest.spyOn<BaseProjectTypeAutocompleteComponent, any>(
        component,
        'setBaseProjectTypeFilter'
      );

      component['setBaseProjectTypeFilter']('');

      expect(component['setBaseProjectTypeFilter']).toReturnWith(
        mockAllBaseProjectTypeIds
      );
    });

    it('should return specific baseProjectTypeIds', () => {
      jest.spyOn<BaseProjectTypeAutocompleteComponent, any>(
        component,
        'setBaseProjectTypeFilter'
      );

      const mockItem = mockAllBaseProjectTypeIds[0];
      const result = component['setBaseProjectTypeFilter'](mockItem.name);

      expect(result).toEqual([mockItem]);
    });
  });

  describe('getSelectedBpTypeIds() [Method]', () => {
    it('should be called', () => {
      jest.spyOn(component, 'getSelectedBpTypeIds');

      component.getSelectedBpTypeIds();

      expect(component.getSelectedBpTypeIds).toHaveBeenCalled();
    });
  });
});
