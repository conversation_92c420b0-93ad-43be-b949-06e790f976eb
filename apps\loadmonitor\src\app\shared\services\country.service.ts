import { Injectable} from '@angular/core';
import { Observable } from 'rxjs';
import { HttpClient } from '@angular/common/http';
import { ConfigService } from './config.service';
import { Country } from '@loadmonitor/shared/interfaces/Country';
import { map, shareReplay } from 'rxjs/operators';

@Injectable({
	providedIn: 'root',
})
export class CountryService {


	private url!:any;

	private cachedCountry!: Observable<Country[]>;
	constructor(private http: HttpClient, private config: ConfigService) {
		this.url = `${this.config.getCountryApiUrl()}/Countries`;
	}

	getAsync(): Observable<Country[]> {
		if (!this.cachedCountry) {
			this.cachedCountry = this.http.get<Country[]>(this.url).pipe(
				map((countries) => countries.sort(this.sortCountries)),
				shareReplay(1)
			);
		}
;		return this.cachedCountry;
	}

	private sortCountries = (firstCountry: Country, secondCountry: Country) => firstCountry?.name?.localeCompare(secondCountry?.name);
}
