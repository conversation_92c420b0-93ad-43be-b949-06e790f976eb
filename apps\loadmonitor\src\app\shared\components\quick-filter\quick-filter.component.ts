import { Component, EventEmitter, Input, OnInit, Output, ViewChild } from '@angular/core';
import { Observable, Subscription } from 'rxjs';

@Component({
	selector: 'lm-quick-filter',
	templateUrl: './quick-filter.component.html',
	styleUrls: ['./quick-filter.component.scss'],
})
export class QuickFilterComponent implements OnInit {
	//selectedfilter!: string;
	@Input() selectedfilter!: string;
	//@Input() currentfilter= JSON.parse(localStorage.getItem('QuickFilter') || '[]' );
	private eventsSubscription!: Subscription;
	@Input() events!: Observable<void>;
	@Input() modalshow!: boolean;
	@Output() saveInQuickFilter: EventEmitter<string> = new EventEmitter();
	@Output() updatetodefault: EventEmitter<string> = new EventEmitter();
	@Output() applyquickfilter: EventEmitter<string> = new EventEmitter();
	@Output() disabledbutton: EventEmitter<boolean> = new EventEmitter();
	@ViewChild('mySelect') mySelect;
	defaultValue = ['Select favourite filter'];
	Error = true;
	FilterName = '';
	completefilter = JSON.parse(localStorage.getItem('QuickFilter') || '[]').filter((x) => x.user == localStorage.getItem('username'));
	currentfilter = JSON.parse(localStorage.getItem('QuickFilter') || '[]').filter(
		(x) => x.user == localStorage.getItem('username') && x.slice == window.location.href.split('/')[3]
	);

	ngOnInit(): void {
		this.eventsSubscription = this.events?.subscribe(() => this.openmodal());
		const optionslength = JSON.parse(localStorage.getItem('QuickFilter') || '[]').filter(
			(x) => x.user == localStorage.getItem('username') && x.slice == window.location.href.split('/')[3]
		).length;
		if (optionslength > 4) {
			this.disabledbutton.emit(true);
		}
	}
	applyfilter(selectedvalue) {
		this.selectedfilter = selectedvalue.value;
		this.applyquickfilter.emit(selectedvalue);
	}
	closeAction() {
		if (!this.Error) this.modalshow = false;
		this.Error = false;
	}
	confirmAction() {
		if (this.currentfilter.filter((x) => x.filter == this.FilterName).length > 0) {
			alert('A quick filter with the same name already exists. Please give it a unique name.');
			this.Error = true;
		} else if (this.FilterName.trim() == '') {
			alert('Filter Name cannot be empty');
			this.Error = true;
		} else {
			this.Error = false;
			this.saveInQuickFilter.emit(this.FilterName);
			this.modalshow = false;
			this.FilterName = '';
			this.currentfilter = JSON.parse(localStorage.getItem('QuickFilter') || '[]').filter(
				(x) => x.user == localStorage.getItem('username') && x.slice == window.location.href.split('/')[3]
			);
			this.completefilter = JSON.parse(localStorage.getItem('QuickFilter') || '[]').filter((x) => x.user == localStorage.getItem('username'));
			const optionslength = JSON.parse(localStorage.getItem('QuickFilter') || '[]').filter(
				(x) => x.user == localStorage.getItem('username') && x.slice == window.location.href.split('/')[3]
			).length;
			if (optionslength > 4) {
				this.disabledbutton.emit(true);
			}
		}
	}
	cancelAction() {
		this.modalshow = false;
	}
	openmodal() {
		this.modalshow = true;
	}
	delete(selectedvalue) {
		const lastvalue = this.selectedfilter;
		const opt = confirm('Are you sure you want to delete ' + selectedvalue + ' Quick Filter? This cannot be reversed');
		if (opt) {
			let index = this.completefilter.findIndex((obj) => obj.filter == selectedvalue);
			this.completefilter.splice(index, 1);
			this.disabledbutton.emit(false);
			localStorage.setItem('QuickFilter', JSON.stringify(this.completefilter));
			index = this.currentfilter.findIndex((obj) => obj.filter == selectedvalue);
			this.currentfilter.splice(index, 1);
			if (this.selectedfilter == selectedvalue) {
				//this.selectedfilter ='Default';
				this.applyquickfilter.emit('Default');
			} else {
				this.selectedfilter = lastvalue;
			}
			this.mySelect.close();
		}
	}

	closeModal(event): void {
		if (event) {
			this.confirmAction();
		} else {
			this.cancelAction();
		}
		this.closeAction();
	}

	keyPressAlphaNumbers(event) {
		const charCode = event.which ? event.which : event.keyCode;
		if (this.FilterName.length == 0) {
			if (
				!(charCode > 47 && charCode < 58) &&
				!(charCode > 64 && charCode < 91) &&
				!(charCode > 96 && charCode < 123) &&
				charCode != 45 &&
				charCode != 95
			) {
				event.preventDefault();
				return false;
			} else {
				return true;
			}
		} else {
			if (
				!(charCode > 47 && charCode < 58) &&
				!(charCode > 64 && charCode < 91) &&
				!(charCode > 96 && charCode < 123) &&
				charCode != 32 &&
				charCode != 45 &&
				charCode != 95
			) {
				event.preventDefault();
				return false;
			} else {
				return true;
			}
		}
	}
	UpdateButtons(){
		if (this.FilterName.length == 0) {
			this.Error=true;
		}
		else{
			this.Error=false;
		}
	}
}
