import { RangedWeightingImport } from '../interfaces/RangedWeightingImport';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { HttpClient } from '@angular/common/http';
import { ConfigService } from './config.service';
import { Jobs } from '@loadmonitor/shared/interfaces/Jobs';

@Injectable({
  providedIn: 'root',
})
export class RangedWeightingImportService {
  private url!:string;

  constructor(private http: HttpClient, private config: ConfigService) {
    this.url = `${this.config.getApiUrl()}/RangedWeightingImports`;
  }

  getAsync(
    statusIds?: number[],
    importIds?: number[],
    jeeJobIds?: number[],
    qcProjectIds?: number[],
    usernames?: string[],
    qcProjectName?: string,
    startDate?: Date,
    endDate?: any
  ): Observable<Jobs<RangedWeightingImport>> {
    const body = {
      statusIds,
      importIds,
      jeeJobIds,
      qcProjectIds,
      usernames,
      qcProjectName,
      startDate,
      endDate
    };
    return this.http.post<Jobs<RangedWeightingImport>>(this.url, body);
  }
}
