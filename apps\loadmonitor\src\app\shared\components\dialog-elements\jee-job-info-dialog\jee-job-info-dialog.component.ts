import { MAT_DIALOG_DATA, MatDialog, MatDialogRef } from '@angular/material/dialog';
import { Component, Inject, Input } from '@angular/core';
import { JEEJob } from '@loadmonitor/shared/interfaces/JEEJob';
import { JEEJobService } from '@loadmonitor/shared/services/jee-job.service';
import { DefaultSnackbarComponent } from '../../default-snackbar/default-snackbar.component';
import { MatSnackBar } from '@angular/material/snack-bar';

@Component({
  selector: 'lm-jee-job-info',
  templateUrl: 'jee-job-info.component.html',
  styleUrls: ['jee-job-info.component.scss'],
})
export class JEEJobInfoComponent {
  @Input() jobId: any;

  constructor(
    private jeeJobService: JEEJobService,
    public dialog: MatDialog,
    public snackBar: MatSnackBar
  ) {
    this.jobId = '';
  }

  openDialog() {
    if (this.jobId > 0 && this.jobId) {
      this.jeeJobService.getAsync(Number(this.jobId)).subscribe(
        (result) => {
          const dialogRef = this.dialog.open(JEEJobInfoDialogComponent, {
            data: {
              numberOfExecutions: result.numberOfExecutions,
              loops: result.loops,
              priority: result.priority,
              id: Number(this.jobId),
              estimation: result.estimation
            },
          });
  
          dialogRef.afterClosed().subscribe(result => {
            console.log(`Dialog result: ${result}`);
          });
        },
        (error) => {
          if (error.status === 404) {
            this.notify("Job with value "+this.jobId+" does not exist!");
          }
        }
      );
    } else {
      this.notify("This is not a valid job id.");
    }
    return false;
  }
  


  private notify(message: string) {
    this.snackBar.openFromComponent(DefaultSnackbarComponent, {
      duration: 10 * 1000,
      data: message,
    });
  }

}

@Component({
  templateUrl: 'jee-job-info-dialog.component.html',
  styleUrls: ['jee-job-info-dialog.component.scss'],
})
export class JEEJobInfoDialogComponent {
  constructor(
    public dialogRef: MatDialogRef<JEEJobInfoDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data:JEEJob
  )
  {}


  onNoClick(): void {
    this.dialogRef.close();
  }

}
