import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { HttpClient } from '@angular/common/http';
import { ConfigService } from './config.service';
import { JEEJob } from '@loadmonitor/shared/interfaces/JEEJob';

@Injectable({
  providedIn: 'root',
})
export class JEEJobService {
  private url!:string;

  constructor(private http: HttpClient, private config: ConfigService) {
    this.url = `${this.config.getJeeApiUrl()}/Jobs/`;
  }

  getAsync(query: number): Observable<JEEJob> {
    return this.http.get<JEEJob>(this.url + query);
  }
}
