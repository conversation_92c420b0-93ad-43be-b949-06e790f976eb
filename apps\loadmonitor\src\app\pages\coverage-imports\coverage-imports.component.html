<lm-navigation></lm-navigation>

<div class="each-slice-top-headbar"  [ngClass]="FilterTrayOpenFlag ? 'filter-tray-open' : 'filter-tray-close'">
  <h3 class="alignleft">Coverage Imports</h3>
  <div class="flexdiv-buttons">
    <button testId="lmx-button" (click)="openJiraTicket()" mat-raised-button color="primary" class="jira-button-margin-right btn-secondary gfk-btn"
      [disabled]="!checkAtleastOneRowChecked()">
      Create JIRA Ticket
    </button>
    <lmx-filter-button showFilterCount="{{ filterCount }}" (isShownFilterTray)="isShownFilterTrayEventHandler($event)">
    </lmx-filter-button>
  </div>
</div>

<lmx-filter-tray
	(ButtonShowHideEvent)="getWidth()"
	(clickApplyButton)="ConfirmApply(true)"
	(clickSaveButton)="openmodal()"
	[IsEnabled]="IsEnabled"
	[IsDisabled]="IsDisabled"
>
	<lm-filter-tray-form
	    [sliceName]="'CoverageImports'"
		[resetForm]="resetForm"
		(selectedFilters)="getSelectedFilters($event)"
	>
	</lm-filter-tray-form>
</lmx-filter-tray>

<div class="filter-area"  [ngClass]="FilterTrayOpenFlag ? 'filter-tray-open' : 'filter-tray-close'">
  <div class="toggle-button">
    <div>
      <lm-quick-filter [selectedfilter]="defaultoption" class="p-3.5" [events]="eventsSubject.asObservable()"
        *ngIf="IsEnabled" [modalshow]="modalshow"
        (saveInQuickFilter)="saveInQuickFilter(cacheObj.isAutoRefresh, $event)" (applyquickfilter)="applyfilter($event)"
        (disabledbutton)="disablesave($event)">
      </lm-quick-filter>

      <lm-filter-summary (resetFilters)="filterSummaryChipClear()"></lm-filter-summary>
    </div>
    <div class="refreshedTime">
			<lm-refreshed-date-time *ngIf="refreshedDateTime" class="pr-3 d-inline-block"></lm-refreshed-date-time>
      <gfk-toggle-button
        *ngIf="EnableRefresh"
        [checked]="cacheObj.isAutoRefresh || false"
        title="Auto Refresh"
        class="autoRefresh d-inline-block"
        [disabled]="defaultoption === 'Default'"
        (onChange)="toggleChecked($event)">
      </gfk-toggle-button>
		</div>
	</div>
</div>

<article class="table-grid">
	<div class="mat-elevation-z8 tab-container" [ngClass]="(coverageImportsData?.length) ? 'table-height' : ''">
		<table mat-table [dataSource]="dataSource" matSort class="full-width-table js-tbl-coverage-imports-result" multiTemplateDataRows>
			<ng-container matColumnDef="details">
				<th mat-header-cell *matHeaderCellDef></th>
				<td mat-cell *matCellDef="let element">
					<mat-icon (click)="element.isExpanded = element.isExpanded; element.isExpanded = !element.isExpanded" *ngIf="element.isExpanded"
						>keyboard_arrow_down</mat-icon
					>
					<mat-icon (click)="element.isExpanded = !element.isExpanded; element.isExpanded = element.isExpanded" *ngIf="!element.isExpanded"
						>chevron_right</mat-icon
					>
				</td>
			</ng-container>

			<ng-container matColumnDef="menu">
				<th mat-header-cell *matHeaderCellDef class="selectAllPad">
					<mat-checkbox (change)="selectRows($event)" [checked]="this.IsSelectAll" [indeterminate]="this.IsSelectAll"> </mat-checkbox>
				</th>
				<td mat-cell *matCellDef="let element">
					<div class="flexdiv">
						<mat-checkbox [checked]="element.isChecked" (change)="rowToggleSelection($event, element)"></mat-checkbox>
						<lm-detail-menu [configuration]="element.detailMenuConfig"></lm-detail-menu>
					</div>
				</td>
			</ng-container>

			<ng-container matColumnDef="loadId">
				<th mat-header-cell *matHeaderCellDef mat-sort-header>Load ID</th>
				<td mat-cell *matCellDef="let element">{{ element.loadId }}</td>
			</ng-container>

			<ng-container matColumnDef="workspaceName">
				<th mat-header-cell *matHeaderCellDef mat-sort-header>Workspace</th>
				<td mat-cell *matCellDef="let element">{{ element.workspaceName }}</td>
			</ng-container>

			<ng-container matColumnDef="status">
				<th mat-header-cell *matHeaderCellDef mat-sort-header>Status</th>
				<td mat-cell *matCellDef="let element">{{ element.status }}</td>
			</ng-container>

			<ng-container matColumnDef="createdBy">
				<th mat-header-cell *matHeaderCellDef mat-sort-header>Created By</th>
				<td mat-cell *matCellDef="let element">{{ element.createdBy }}</td>
			</ng-container>

			<ng-container matColumnDef="created">
				<th mat-header-cell *matHeaderCellDef mat-sort-header>Created On</th>
				<td mat-cell *matCellDef="let element">{{ element.created | date: date_with_time }}</td>
			</ng-container>

			<ng-container matColumnDef="message">
				<th mat-header-cell *matHeaderCellDef mat-sort-header>Message</th>
				<td mat-cell *matCellDef="let element">{{ element.message }}</td>
			</ng-container>

			<ng-container matColumnDef="started">
				<th mat-header-cell *matHeaderCellDef mat-sort-header>Started On</th>
				<td mat-cell *matCellDef="let element">{{ element.started | date: date_with_time }}</td>
			</ng-container>

			<ng-container matColumnDef="finished">
				<th mat-header-cell *matHeaderCellDef mat-sort-header>Finished On</th>
				<td mat-cell *matCellDef="let element">{{ element.finished | date: date_with_time }}</td>
			</ng-container>

			<ng-container matColumnDef="sessionId">
				<th mat-header-cell *matHeaderCellDef mat-sort-header>Session ID</th>
				<td mat-cell *matCellDef="let element">{{ element.sessionId }}</td>
			</ng-container>

			<ng-container matColumnDef="jobId">
				<th mat-header-cell *matHeaderCellDef mat-sort-header>JEE Job Id</th>
				<td mat-cell *matCellDef="let element">{{ element.jobId }}</td>
			</ng-container>

			<ng-container matColumnDef="jeeJobInfo">
				<th mat-header-cell *matHeaderCellDef>JEE Job Info</th>
				<td mat-cell *matCellDef="let element">
					<lm-jee-job-info jobId="{{ element.jobId }}"></lm-jee-job-info>
				</td>
			</ng-container>

			<ng-container matColumnDef="serverIPPort">
				<th mat-header-cell *matHeaderCellDef mat-sort-header>ServerIPPort</th>
				<td mat-cell *matCellDef="let element">{{ element.serverIPPort }}</td>
			</ng-container>

			<tr mat-header-row *matHeaderRowDef="displayedColumns; sticky: true" class="columnHeader"></tr>
			<tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
		</table>
	</div>
	<mat-paginator [pageSizeOptions]="[10, 20, 50, 100]" showFirstLastButtons></mat-paginator>
</article>
<lm-version></lm-version>