import { Component, ElementRef, Input, OnChanges, OnInit, SimpleChanges, ViewChild } from '@angular/core';
import { BaseProjectType } from '@loadmonitor/shared/interfaces/BaseProjectType';
import { COMMA, ENTER } from '@angular/cdk/keycodes';
import { FormControl } from '@angular/forms';
import { Observable } from 'rxjs/internal/Observable';
import { map, startWith } from 'rxjs/operators';
import { get } from 'lodash';
import { MatAutocompleteSelectedEvent } from '@angular/material/autocomplete';

const EMPTY = null;

@Component({
  selector: 'lm-base-project-type-autocomplete',
  templateUrl: './base-project-type-autocomplete.component.html',
  styleUrls: ['./base-project-type-autocomplete.component.scss']
})
export class BaseProjectTypeAutocompleteComponent implements OnInit,OnChanges {
  @Input() fieldName = '';
  @Input() bpTypeIds:number[]=[];

  @ViewChild('baseProjectTypeIdsInput')
  baseProjectTypeIdsInput!: ElementRef<HTMLInputElement>;

  baseProjectTypeIds!: Observable<BaseProjectType[]>;

  //Tag-Chip-List
  readonly separatorKeysCodes = [ENTER, COMMA] as const;

  // Mat-Chips - BaseProjectType
  baseProjectTypeIdsCtrl = new FormControl(EMPTY);
  filteredBaseProjectTypeIds!: Observable<BaseProjectType[]>;
  allBaseProjectTypeIds: BaseProjectType[] = [];

  ngOnInit() {
    this.fillBaseProjectTypes();

    this.baseProjectTypeIds = this.baseProjectTypeIdsCtrl.valueChanges.pipe(
      startWith(EMPTY),
      map(() =>
        this.filterSelectedBpTypeIds(),
      ),
    );
    this.filteredBaseProjectTypeIds = this.baseProjectTypeIdsCtrl.valueChanges.pipe(
      startWith(EMPTY),
      map((baseProjectType) => this.setBaseProjectTypeFilter(get(baseProjectType, 'name', baseProjectType)))
    );


    this.setSelectedBpTypes(this.bpTypeIds);
    this.refreshSuggestionsAndEmptyInput();
  }

  fillBaseProjectTypes(): void {
    this.allBaseProjectTypeIds = [
      { id: 1, name: 'Industry', selected: false },
      { id: 2, name: 'Retailer', selected: false },
      { id: 3, name: 'Industry/Retailer', selected: false },
      { id: 4, name: 'IDAS DST', selected: false },
    ];

    this.allBaseProjectTypeIds = this.allBaseProjectTypeIds.sort((firstService: BaseProjectType, otherService: BaseProjectType) => firstService.name.localeCompare(otherService.name));

  }

  public setSelectedBpTypes(periodicityIds: number[]): void {
    periodicityIds?.forEach((PPTid: number) => {
      const found = this.allBaseProjectTypeIds.find((PPT: BaseProjectType) => PPT.id === PPTid);
      if (found) {
        found.selected = true;
      } else {
        this.allBaseProjectTypeIds.push({id: PPTid, name: 'Loading...', selected: true})
      }
    });
    this.refreshSuggestionsAndEmptyInput();
  }

  // BaseProjectType Chips

  removeBaseProjectTypeChips(item: BaseProjectType): void {
    this.toggleSelection(item);
  }

  optionClicked(event: Event, item: BaseProjectType) {
    event.stopPropagation();
    this.toggleSelection(item);
  }

  toggleSelection(item: BaseProjectType) {
    item.selected = !item.selected;
    this.refreshSuggestionsAndEmptyInput();
  }

  refreshSuggestionsAndEmptyInput() {
    if(this.baseProjectTypeIdsInput!=undefined)
    this.baseProjectTypeIdsInput.nativeElement.value = '';

      this.baseProjectTypeIdsCtrl.reset();
  }

  selectedBaseProjectTypeChips(event: MatAutocompleteSelectedEvent): void {
    this.toggleSelection(event.option.value);
    this.refreshSuggestionsAndEmptyInput();
  }

  private setBaseProjectTypeFilter(value: string): BaseProjectType[] {
    if (value === '' || value === null) {
      return this.allBaseProjectTypeIds;
    }else {
      const filterValue = value.toLowerCase();
      return this.allBaseProjectTypeIds.filter(
        (PPT) => PPT.name.toLowerCase().indexOf(filterValue) === 0,
      );
    }
  }

  public getSelectedBpTypeIds() {
    return this.filterSelectedBpTypeIds().map((status) => status.id);
  }

  public getSelectedBpTypeNames() {
    return this.filterSelectedBpTypeIds().map((status) => status.name);
  }

  filterSelectedBpTypeIds(): BaseProjectType[] {
    return this.allBaseProjectTypeIds.filter(
      (bpType) => bpType.selected,
    );
  }
  removeBaseProjectTypes(): void {
    this.filterSelectedBpTypeIds().map((x) => x.selected = false);
    this.refreshSuggestionsAndEmptyInput();
  }
  ngOnChanges(changes: SimpleChanges){
    this.allBaseProjectTypeIds.forEach(function(i){ i.selected=false })
    this.setSelectedBpTypes(changes.bpTypeIds.currentValue);
  }


}
