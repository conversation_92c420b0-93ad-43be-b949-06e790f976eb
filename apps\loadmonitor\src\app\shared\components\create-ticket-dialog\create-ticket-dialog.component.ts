
import { Component, Inject, OnInit, ViewChild } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { CreateTicketService } from '@loadmonitor/shared/services/create-ticket.service';
import { FormGroup, FormControl, Validators } from '@angular/forms';
import { NumericChipComponent } from '../filter-elements/numeric-chip/numeric-chip.component';
import { DefaultSnackbarComponent } from '../default-snackbar/default-snackbar.component';
import { MatSnackBar } from '@angular/material/snack-bar';
import { JobTypes } from '@loadmonitor/shared/jobTypes';
import { ParentIssue } from '@loadmonitor/shared/interfaces/ParentIssue';
import { SuccessJiraTicketCreationComponent } from '../success-jira-ticket-creation/success-jira-ticket-creation.component';
import { DwhReleaseJiraProperty } from '@loadmonitor/shared/interfaces/DwhReleaseJiraProperty';
import { publishRepublishJiraProperties } from '@loadmonitor/shared/interfaces/PublishRepublishJiraProperty';
import { BcrReworksJiraProperty } from '@loadmonitor/shared/interfaces/BcrReworksJiraProperty';
import { exportJiraProperties } from '@loadmonitor/shared/interfaces/ExportJiraProperties';
import { JiraTicketSummaryPrefix } from '@loadmonitor/shared/JiraTicketSummaryPrefix';

@Component({
  selector: 'lm-create-ticket-dialog',
  templateUrl: './create-ticket-dialog.component.html',
  styleUrls: ['./create-ticket-dialog.component.scss']
})
export class CreateTicketDialogComponent implements OnInit {

  JobTypes = JobTypes;
  title = "";
  jobType;

  formdata;
  @ViewChild('ids', { static: true }) ids:
    | NumericChipComponent
    | undefined;

  constructor(
    public dialogRef: MatDialogRef<CreateTicketDialogComponent>,
    private JiraExportService: CreateTicketService,
    private snackBar: MatSnackBar,
    @Inject(MAT_DIALOG_DATA) public data: any) { }
  ngOnInit(): void {
    this.formdata = new FormGroup({
      summary: new FormControl(""),
      description: new FormControl("")
    }, [Validators.required]);
  }
  async onClickSubmit(data) {
    await this.JiraExportService.getJiraTicketAsync(
      this.ticketFactory(data)
    ).subscribe((result) => {
      if(result)
        this.displaySuccess(result.name,result.url);
      else
        this.notify('Ticket creation failed due to some error');
    });
  }

  private displaySuccess(ticketNumber: string,ticketUrl :string) {
      this.snackBar.openFromComponent(SuccessJiraTicketCreationComponent, {
        duration: 10 * 1000,
        data: {TicketNumber:ticketNumber,TicketUrl:ticketUrl}
      });
    }

  private notify(message: string) {
    this.snackBar.openFromComponent(DefaultSnackbarComponent, {
      duration: 10 * 1000,
      data: message,
    });
  }

  get summary() { return this.formdata.get('summary'); }

  get description() { return this.formdata.get('description'); }

  ticketFactory = (data) => {
    data.summary = JiraTicketSummaryPrefix.prefixData +" "+ data.summary;
    switch (this.data.jobType) {
      case JobTypes.ldGeneral:
        return new LdGeneralIssue(this.data.title, data.summary, data.description, this.data.ids);
        case JobTypes.DataOrder:
          return new DataOrderIssue(this.data.title, data.summary, data.description, this.data.ids);
          case JobTypes.CsvImport:
            return new CsvImportIssue(this.data.title, data.summary, data.description, this.data.ids);
          case JobTypes.itemMasterData:
            return new ItemMasterDataIssue(this.data.title, data.summary, data.description, this.data.prepUnitIds,this.data.loadUnitIds);
       case JobTypes.DWHRelease:
            return new DwhRelease(this.data.title, data.summary, data.description, this.data.dwhReleaseJiraProperties);
      case JobTypes.rbPublishUnpublish:
            return new PublishRepublishIssue(this.data.title, data.summary, data.description, this.data.publishRepublishJiraProperties);
      case JobTypes.bcrRework:
              return new BcrReworks(this.data.title, data.summary, data.description, this.data.bcrReworksJiraProperties);
      case JobTypes.coverageImport:
        return new CoverageImports(this.data.title, data.summary, data.description, this.data.messages);
      default:
        return new ExportIssue(this.data.title, data.summary, data.description, this.data.exportJiraProperties);
    }
  };
}

class LdGeneralIssue implements ParentIssue
{
    title!: "";
    email="";
    summary!: "";
    description!: "";
    loadDefinitionIds?: number[];

    constructor(title, summary, description, loadDefinitionIds){
        this.title=title;
        this.summary=summary;
        this.description=description;
        this.loadDefinitionIds=loadDefinitionIds;
        this.email = localStorage.getItem('Email') || '';
    }
}
class DataOrderIssue implements ParentIssue
{
    title!: "";
    email="";
    summary!: "";
    description!: "";
    dataOrderIds?: number[];

    constructor(title, summary, description, dataOrderIds){
        this.title=title;
        this.summary=summary;
        this.description=description;
        this.dataOrderIds=dataOrderIds;
        this.email = localStorage.getItem('Email') || '';
    }
}
class CsvImportIssue implements ParentIssue
{
    title!: "";
    email="";
    summary!: "";
    description!: "";
    csvLoadIds?: number[];

    constructor(title, summary, description, csvLoadIds){
        this.title=title;
        this.summary=summary;
        this.description=description;
        this.csvLoadIds=csvLoadIds;
        this.email = localStorage.getItem('Email') || '';
    }
}
class ItemMasterDataIssue implements ParentIssue
{
    title!: "";
    email="";
    summary!: "";
    description!: "";
    PrepUnitIds?: number[];
    LoadUnitIds?: number[];

    constructor(title, summary, description, prepUnitId, loadUnitId){
        this.title=title;
        this.summary=summary;
        this.description=description;
        this.PrepUnitIds=prepUnitId;
        this.LoadUnitIds=loadUnitId;
        this.email = localStorage.getItem('Email') || '';
    }
}

class ExportIssue implements ParentIssue
{
    title!: "";
    email="";
    summary!: "";
    description!: "";
    exportInputProperties!: exportJiraProperties[];

    constructor(title, summary, description, exportJiraProperties){
        this.title=title;
        this.summary=summary;
        this.description=description;
        this.exportInputProperties=exportJiraProperties;
        this.email = localStorage.getItem('Email') || '';
    }
}

class PublishRepublishIssue implements ParentIssue
{
    title!: "";
    email="";
    summary!: "";
    description!: "";
    publishRepublishInputProperties!: publishRepublishJiraProperties[];

    constructor(title, summary, description, publishRepublishJiraProperties){
        this.title=title;
        this.summary=summary;
        this.description=description;
        this.publishRepublishInputProperties=publishRepublishJiraProperties;
        this.email = localStorage.getItem('Email') || '';
    }
}
class DwhRelease implements ParentIssue
{
    title!: "";
    email="";
    summary!: "";
    description!: "";
    DwhReleaseInputProperties!: DwhReleaseJiraProperty[];

    constructor(title, summary, description, dwhReleaseJiraProperties){
        this.title=title;
        this.summary=summary;
        this.description=description;
       this.DwhReleaseInputProperties=dwhReleaseJiraProperties;
       this.email = localStorage.getItem('Email') || '';
    }
}

class CoverageImports implements ParentIssue
{
    title!: "";
    email="";
    summary!: "";
    description!: "";
    Messages?: string[];

    constructor(title, summary, description, message){
        this.title=title;
        this.summary=summary;
        this.description=description;
        this.Messages=message;
        this.email = localStorage.getItem('Email') || '';
    }
}

class BcrReworks implements ParentIssue
{
    title!: "";
    email="";
    summary!: "";
    description!: "";
    BcrReworksInputProperties!: BcrReworksJiraProperty[];

    constructor(title, summary, description, bcrReworksJiraProperties){
        this.title=title;
        this.summary=summary;
        this.description=description;
       this.BcrReworksInputProperties=bcrReworksJiraProperties;
       this.email = localStorage.getItem('Email') || '';
    }

}
