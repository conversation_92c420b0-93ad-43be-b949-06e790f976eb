import { ComponentFixture, TestBed } from '@angular/core/testing';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { SharedModule } from '@loadmonitor/shared/shared.module';

import { NumericChipComponent } from './numeric-chip.component';

describe('NumericChipComponent', () => {
  let component: NumericChipComponent;
  let fixture: ComponentFixture<NumericChipComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [NumericChipComponent],
      imports: [SharedModule, NoopAnimationsModule],
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(NumericChipComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
