import { UserCache } from '@loadmonitor/shared/interfaces/caching/UserCache';
import { Country } from '../Country';

export interface ExportCache {
    exportIds: number[];
    exportFormatIds: number[];
    processStageIds: number[];
    reportingProjectIds: number[];
    jobIds: number[];
    countryIds: number[];
    status: number[];
    selectedDays: string;
    startDate?: Date;
    endDate?: Date;
    users?: UserCache[];
    exportName:string;
    reportingProjectName:string;
    isAutoRefresh?: boolean;
    countries:Country[];
    reportingGroupIds: number[];
    cdmDeliveryIds: number[];
  }
