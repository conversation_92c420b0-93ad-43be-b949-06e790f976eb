@use 'node_modules/@angular/material/index' as mat;
@use 'gfk-light.palette' as gfk;
@use 'sass:map' as map;

// Primary palette: colors most widely used across all screens and components.
$primary-palette: (
  50: #e8edf7,
  900: #1e3a98,
  A100: #d2dbff,
  A200: #9fb2ff,
  A400: #6c8aff,
  A700: #5275ff,
  contrast: (
    50: #000000,
    900: #ffffff,
    A100: #000000,
    A200: #000000,
    A400: #000000,
    A700: #ffffff,
  )
);

$primary-palette-contrast: (
  contrast: (
    map.merge(map.get($primary-palette, 'contrast'), map.get(gfk.$blue-palette, 'contrast'))
  )
);
$primary-palette-temp: map.merge(gfk.$blue-palette, $primary-palette-contrast);
$primary-palette: map.merge($primary-palette, $primary-palette-temp);

// Accent palette: colors used for the floating action button and interactive elements.
$accent-palette: (
  50: #fcebe0,
  900: #d32e00,
  A100: #fffbfb,
  A200: #ffd0c8,
  A400: #ffa595,
  A700: #ff8f7b,
  contrast: (
    50: rgba(black, 0.87),
    900: white,
    A100: rgba(black, 0.87),
    A200: white,
    A400: white,
    A700: white,
  ),
);

$accent-palette-contrast: (
  contrast: (
    map.merge(map.get($accent-palette, 'contrast'), map.get(gfk.$orange-palette, 'contrast'))
  )
);
$accent-palette-temp: map.merge(gfk.$orange-palette, $accent-palette-contrast);
$accent-palette: map.merge($accent-palette, $accent-palette-temp);

// Warn palette: colors used to convey error state.
$warn-palette: (
  50: #f7e4e2,
  900: #970904,
  A100: #ffc3c2,
  A200: #ff918f,
  A400: #ff5f5c,
  A700: #ff4642,
  contrast: (
    50: #000000,
    900: #ffffff,
    A100: #000000,
    A200: #000000,
    A400: #000000,
    A700: #ffffff,
  )
);

$warn-palette-contrast: (
  contrast: (
    map.merge(map.get($warn-palette, 'contrast'), map.get(gfk.$red-palette, 'contrast'))
  )
);
$warn-palette-temp: map.merge(gfk.$red-palette, $warn-palette-contrast);
$warn-palette: map.merge($warn-palette, $warn-palette-temp);

// Background palette for light themes.
$light-theme-background-palette: (
  //status-bar:               map.get($grey-palette, 300),
  //app-bar:                  map.get($grey-palette, 100),
  //background:               map.get($grey-palette, 50),
  //hover:                    rgba(black, 0.04),
  //card:                     white,
  //dialog:                   white,
  //disabled-button:          rgba(black, 0.12),
  //raised-button:            white,
  //focused-button:           $dark-focused,
  //selected-button:          map.get($grey-palette, 300),
  //selected-disabled-button: map.get($grey-palette, 400),
  //disabled-button-toggle:   map.get($grey-palette, 200),
  //unselected-chip:          map.get($grey-palette, 300),
  //disabled-list-option:     map.get($grey-palette, 200),
  //tooltip:                  map.get($grey-palette, 700),
);

// Foreground palette for light themes.
$light-theme-foreground-palette: (
  //base: black,
  //divider: $dark-dividers,
  //dividers: $dark-dividers,
  //disabled: $dark-disabled-text,
  //disabled-button: rgba(black, 0.26),
  //disabled-text: $dark-disabled-text,
  //elevation: black,
  //hint-text: $dark-disabled-text,
  //secondary-text: $dark-secondary-text,
  //icon: rgba(black, 0.54),
  //icons: rgba(black, 0.54),
  text: map.get(gfk.$palette, 'text'),
  //slider-min: rgba(black, 0.87),
  //slider-off: rgba(black, 0.26),
  //slider-off-active: rgba(black, 0.38),
);

// Define the palettes for your theme using the Material Design palettes
// For each palette, you can optionally specify a default, lighter, and darker hue.
$primary: mat.define-palette($primary-palette);
$accent: mat.define-palette($accent-palette, 500, 400, 600);
$warn: mat.define-palette($warn-palette);

// Create the theme object. A theme consists of configurations for individual
// theming systems such as `color` or `typography`.
$theme: mat.define-light-theme($primary, $accent, $warn);

// Insert custom background and foreground palette
$background: map-get($theme, background);
$light-theme-background-palette: map_merge(
    $background,
    $light-theme-background-palette
);
$theme: map_merge($theme, (background: $light-theme-background-palette));

$foreground: map-get($theme, foreground);
$light-theme-foreground-palette: map_merge(
    $foreground,
    $light-theme-foreground-palette
);
$theme: map_merge($theme, (foreground: $light-theme-foreground-palette));
