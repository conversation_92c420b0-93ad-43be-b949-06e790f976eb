import { LdGeneralService } from './../../shared/services/ld-general.service';
import { AfterContentInit, Component, HostListener, Input, OnDestroy, OnInit, ViewChild } from '@angular/core';
import { MatSnackBar } from '@angular/material/snack-bar';
import { FormBuilder, FormGroup } from '@angular/forms';
import { MatTable, MatTableDataSource } from '@angular/material/table';
import { DefaultSnackbarComponent } from '@loadmonitor/shared/components/default-snackbar/default-snackbar.component';
import { MatSort } from '@angular/material/sort';
import { MatPaginator } from '@angular/material/paginator';
import { JobTypes } from '@loadmonitor/shared/jobTypes';
import { LdGeneral } from '@loadmonitor/shared/interfaces/LdGeneral';
import { ProductionProjectTypeService } from '@loadmonitor/shared/services/production-project-type.service';
import { TagItem } from '@loadmonitor/shared/interfaces/TagItem';
import { SettingsConstants } from '@loadmonitor/shared/settings.constants';
import { MatBottomSheetRef } from '@angular/material/bottom-sheet';
import { animate, state, style, transition, trigger } from '@angular/animations';
import { PropertyPage } from '@loadmonitor/shared/interfaces/PropertyPage';
import { HelperService } from '@loadmonitor/shared/services/helper.service';
import { LDGeneralCache } from '@loadmonitor/shared/interfaces/caching/LDGeneralCache';
import { MatDialog } from '@angular/material/dialog';
import { Filter, FilterRecords, FilterService } from '@dwh/lmx-lib/src/lib/services/filter.service';
import { CreateTicketDialogComponent } from '@loadmonitor/shared/components/create-ticket-dialog/create-ticket-dialog.component';
import { FilterTrayComponent } from '@dwh/lmx-lib';
import { DatePipe } from '@angular/common';
import { ToggleButtonComponent } from '@gfk/ng-lib';
import { environment } from '../../../../src/environments/environment';
import { BehaviorSubject, Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { SelectionModel } from '@angular/cdk/collections';
import { CountrySelectService } from '@loadmonitor/shared/services/country-select.service';
import * as moment from 'moment';
import { FilterTrayCacheService } from '@loadmonitor/shared/services/filter-tray-cache.service';

@Component({
	selector: 'lm-ld-general',
	templateUrl: './ld-general.component.html',
	styleUrls: ['./ld-general.component.scss'],
	animations: [
		trigger('detailExpand', [
			state('collapsed', style({ height: '0px', minHeight: '0' })),
			state('expanded', style({ height: '*' })),
			transition('expanded <=> collapsed', animate('225ms cubic-bezier(0.4, 0.0, 0.2, 1)')),
		]),
	],
	providers: [CountrySelectService],
})
export class LdGeneralComponent implements OnInit, AfterContentInit, OnDestroy {
	eventsSubject: Subject<void> = new Subject<void>();
	IsEnabled = false;
	IsDisabled = false;
	FilterTrayOpenFlag = true;
	environments = environment.environments;
	modalshow = false;
	currentfilter = JSON.parse(localStorage.getItem('QuickFilter') || '[]').filter(
		(x) => x.user == localStorage.getItem('username') && x.slice == window.location.href.split('/')[3]
	);

	@ViewChild(FilterTrayComponent) filterTrayComponent: FilterTrayComponent | undefined;
	@ViewChild(ToggleButtonComponent) togglebuttonComponent: ToggleButtonComponent | undefined;

	dataSource = new MatTableDataSource<LdGeneral>();
	selection = new SelectionModel<LdGeneral>(true, []);
	IsSelectAll = false;
	@ViewChild(MatSort) sort: MatSort = new MatSort();
	@ViewChild(MatTable) table!: MatTable<LdGeneral>;
	@ViewChild(MatPaginator) paginator!: MatPaginator;

	jobType: JobTypes = JobTypes.ldGeneral;

	panelOpenState = false;
	productionProjectTypes: Map<string, string> = new Map<string, string>();

	tableView!: 'default' | 'selectable';
	anyRowSelected!: boolean;
	bottomSheetObject!: MatBottomSheetRef;

	expandedElement!: PropertyPage | null;
	ldGeneralDetail!: PropertyPage[];
	selectedRow: any;
	filterCount = '';
	EnableRefresh = true;
	readonly displayedColumns: string[] = [
		'details',
		'menu',
		'loadDefinitionId',
		'status',
		'epoLoadMode',
		'isAutoload',
		'productionProjectName',
		'productionProjectId',
		'productionProjectType',
		'period',
		'dataOrderId',
		'createdBy',
		'created',
		'sentBy',
		'sent',
		'changedBy',
		'changed',
	];

	// Progress Spinner visibility
	isLoading = false;
	/** @deprecated use `filterFG` instead for refactored form controls */
	public deprecatedForm!: FormGroup;
	isPeriodFilterHidden = false;
	public cacheObj: LDGeneralCache = {} as LDGeneralCache;
	private readonly CACHE_NAME = 'cache::LDGeneral';
	cacheInterval!: any;
	toggleInterval!: any;
	timer = 60;
	@Input() icon = 'help-outline';
	readonly date_with_time = SettingsConstants.DATE_FORMAT_WITH_TIME;
	defaultfilter = JSON.parse(localStorage.getItem(this.CACHE_NAME + '-DefaultFilter') || '[]');
	defaultoption = localStorage.getItem(this.CACHE_NAME + '-LastFilter') || 'Default';
	refreshedDateTime!: boolean;

	selectedFilters: any;
	cacheValue: BehaviorSubject<any> = new BehaviorSubject<any>({});
	resetForm: Subject<boolean> = new Subject();
	resetPeriodicityFilter: Subject<boolean> = new Subject();
	ldGeneralData!: any;

	@HostListener('document:visibilitychange', ['$event'])
	appVisibility() {
		if (document.hidden) {
			if (this.cacheInterval) {
				clearInterval(this.cacheInterval);
			}
			if (this.toggleInterval) {
				clearInterval(this.toggleInterval);
			}
		} else {
			if (this.cacheObj.isAutoRefresh === true) {
				this.cacheInterval = setInterval(() => {
					if (this.defaultoption != 'Default') {
						this.apply();
					}
				}, this.timer * 1000);
			} else {
				if (this.cacheInterval !== undefined) clearInterval(this.cacheInterval);
				if (this.toggleInterval !== undefined) clearInterval(this.toggleInterval);
			}
		}
	}
	private destroy$ = new Subject<void>();

	constructor(
		private productionProjectTypeService: ProductionProjectTypeService,
		private LdGeneralService: LdGeneralService,
		private snackBar: MatSnackBar,
		private formBuilder: FormBuilder,
		private helperService: HelperService,
		public dialog: MatDialog,
		public filterService: FilterService,
		public datepipe: DatePipe,
		private filterTrayCacheService: FilterTrayCacheService
	) {
		this.fillProductionProjectType();
	}

	applyfilter(selectedvalue) {
		this.resetForm.next(true);
		const oldautotoggle = this.cacheObj.isAutoRefresh;
		selectedvalue = selectedvalue.value ? selectedvalue.value : selectedvalue;
		this.defaultoption = selectedvalue;
		localStorage.setItem(this.CACHE_NAME + '-LastFilter', this.defaultoption || 'Default');
		if (selectedvalue == 'Default') {
			if (
				JSON.parse(localStorage.getItem(this.CACHE_NAME + '-DefaultFilter') || '[]').filter(
					(x) => x.user == localStorage.getItem('username') && x.slice == window.location.href.split('/')[3]
				)[0]
			) {
				this.cacheObj = JSON.parse(localStorage.getItem(this.CACHE_NAME + '-DefaultFilter') || '[]').filter(
					(x) => x.user == localStorage.getItem('username') && x.slice == window.location.href.split('/')[3]
				)[0]['data'];
			}

			localStorage.setItem(this.CACHE_NAME, JSON.stringify(this.cacheObj));
		} else {
			this.cacheObj = JSON.parse(localStorage.getItem('QuickFilter') || '[]')
				.filter((x) => x.user == localStorage.getItem('username') && x.slice == window.location.href.split('/')[3])
				.filter((x) => x.filter == selectedvalue)[0]['data'];
			localStorage.setItem(this.CACHE_NAME, JSON.stringify(this.cacheObj));
		}
		this.cacheObj.isAutoRefresh = oldautotoggle;
		this.CreateFilterSummaryChips();
		this.selectedFilters = this.cacheObj;
    if (this.cacheObj.selectedDays == '3') {
			const date = new Date();
			this.cacheObj.startDate = new Date(Date.UTC(date.getFullYear(), date.getMonth(), date.getDate() - 2, 0, 0, 0, 0));
			this.cacheObj.endDate = new Date(Date.UTC(date.getFullYear(), date.getMonth(), date.getDate(), 0, 0, 0, 0));
		} else if (this.cacheObj.selectedDays == '2') {
			const date = new Date();
			this.cacheObj.startDate = new Date(Date.UTC(date.getFullYear(), date.getMonth(), date.getDate(), 0, 0, 0, 0));
			this.cacheObj.endDate = new Date(Date.UTC(date.getFullYear(), date.getMonth(), date.getDate(), 0, 0, 0, 0));
		}
		setTimeout(() => {
			this.updateFilterFGValues();
			this.apply();
		}, 200);
		localStorage.setItem(this.CACHE_NAME, JSON.stringify(this.cacheObj));
	}

	private updateFilterFGValues(): void {
		if(this.cacheObj){
			this.filterTrayCacheService.getCacheValueData(this.cacheObj);
		}
	}

	openmodal() {
		this.eventsSubject.next();
	}
	disablesave(status) {
		this.IsDisabled = status;
	}

	ngOnInit(): void {
		if (this.environments.filter((x) => x.location == window.location.origin)[0]) {
			this.IsEnabled = this.environments.filter((x) => x.location == window.location.origin)[0].enabled;
		} else {
			this.IsEnabled = false;
		}
		this.deprecatedForm = this.formBuilder.group({
			isAutoRefresh: this.togglebuttonComponent?.checked,
		});
		this.updateFilterFGValues();
		this.setFiltersCount();
	}

	ngOnDestroy() {
		if (this.cacheInterval) {
			clearInterval(this.cacheInterval);
		}

		if (this.toggleInterval) {
			clearInterval(this.toggleInterval);
		}
		this.destroy$.next();
    	this.destroy$.complete();
	}

	ngAfterContentInit() {
		this.afterChildComponentsLoaded();
		this.CreateFilterSummaryChips();
		this.setFiltersCount();
		if (this.cacheObj.isAutoRefresh === true) {
			if (this.cacheInterval === undefined) {
				this.cacheInterval = window.setInterval(() => {
					if (this.defaultoption != 'Default') {
						this.apply();
					}
				}, this.timer * 1000);
			}
		} else {
			if (this.cacheInterval !== undefined) clearInterval(this.cacheInterval);
			if (this.toggleInterval !== undefined) clearInterval(this.toggleInterval);
		}
		this.updateFilterFGValues();
	}

	afterChildComponentsLoaded() {
		this.cacheObj = JSON.parse(localStorage.getItem(this.CACHE_NAME) || '{}');
		this.updateFilterFGValues();
	}

	private setFiltersCount() {
		this.filterService.filterCount$.subscribe((count: number) => {
			if (count > 0) {
				this.filterCount = '(' + count.toString() + ')';
			} else {
				this.filterCount = '';
			}
		});
	}

	toggleChecked(check: any) {
		if (check === true) {
			this.saveInCache(check, true);
			this.toggleInterval = window.setInterval(() => {
				if (this.defaultoption != 'Default') {
					this.apply();
				}
			}, this.timer * 1000);
		} else {
			if (this.toggleInterval !== undefined) clearInterval(this.toggleInterval);
			if (this.cacheInterval !== undefined) clearInterval(this.cacheInterval);
			this.saveInCache(check, true);
		}
	}
	datesFilterSummary(dateStart: any, dateEnd: any) {
		if (dateStart != null || dateEnd != null) {
			const start = dateStart == 'Invalid Date' || dateStart == null ? '' : this.datepipe.transform(dateStart, 'yyyy-MM-dd');
			const end = dateEnd == 'Invalid Date' || dateEnd == null ? '' : this.datepipe.transform(dateEnd, 'yyyy-MM-dd');
			this.filterService.setFilters({
				dates: {
					name: 'Date',
					values: (start !== undefined && end !== undefined) ? [start, end] : null,
					slice: window.location.href.split('/')[3],
				} as Filter,
			});
		} else {
			this.filterService.setFilters({
				dates: {
					name: 'Date',
					values: null,
					slice: window.location.href.split('/')[3],
				} as unknown as Filter,
			});
		}
	}

	countryFilterSummary() {
		this.filterService.setFilters({
			countryIds: {
				name: 'Country',
				values: this.cacheObj.countries,
				slice: window.location.href.split('/')[3],
			} as Filter,
		});
	}

	periodicityFilterSummary() {
		this.filterService.setFilters({
			periodicityIds: {
				name: 'Periodicity',
				values: this.cacheObj.periodicityIds? [{ value: this.cacheObj.periodicityIds, label: this.cacheObj.periodicityIds }] : null,
				slice: window.location.href.split('/')[3],
			} as Filter,
		});
	}

	periodFilterSummary() {
		this.filterService.setFilters({
			periodIds: {
				name: 'Period',
				values: this.cacheObj.periodIds,
				slice: window.location.href.split('/')[3],
			} as Filter,
		});
	}

	usersFilterSummary() {
		this.filterService.setFilters({
			userIds: {
				name: 'User',
				values: this.cacheObj.users?.map((i) => i.name),
				slice: window.location.href.split('/')[3],
			} as Filter,
		});
	}

	statusFilterSummary() {
		this.filterService.setFilters({
			statusIds: {
				name: 'Status',
				values: this.cacheObj.status,
				slice: window.location.href.split('/')[3],
			} as Filter,
		});
	}

	prodProjectNameFilterSummary() {
		const _prodProjectName = [] as any[];
		if (this.cacheObj.productionProjectName) {
			_prodProjectName.push(this.cacheObj.productionProjectName);
		}
		this.filterService.setFilters({
			prodProjectName: {
				name: 'Production Project Name',
				values: _prodProjectName,
				slice: window.location.href.split('/')[3],
			} as Filter,
		});
	}

	productionprojectIdsFilterSummary() {
		this.filterService.setFilters({
			productionprojectIds: {
				name: 'Production Project IDs',
				values: this.cacheObj.productionProjectIds,
				slice: window.location.href.split('/')[3],
			} as Filter,
		});
	}

	loadDefinitionIdsFilterSummary() {
		this.filterService.setFilters({
			loaddefinitionIds: {
				name: 'Load Definition IDs',
				values: this.cacheObj.loadDefinitionIds,
				slice: window.location.href.split('/')[3],
			} as Filter,
		});
	}

	productionProjectTypeFilterSummary() {
		this.filterService.setFilters({
			productionProjectTypeIds: {
				name: 'Production Project Type',
				values: this.cacheObj.productionProjectType,
				slice: window.location.href.split('/')[3],
			} as Filter,
		});
	}

	CreateFilterSummaryChips() {
		if (this.cacheObj && Object.keys(this.cacheObj).length) {
			this.countryFilterSummary();
			this.periodicityFilterSummary();
			this.periodFilterSummary();
			this.usersFilterSummary();
			this.statusFilterSummary();
			this.loadDefinitionIdsFilterSummary();
			this.productionprojectIdsFilterSummary();
			this.prodProjectNameFilterSummary();
			this.productionProjectTypeFilterSummary();
			this.datesFilterSummary(this.cacheObj.startDate, this.cacheObj.endDate);
		}
	}

	filterSummaryChipClear(filteredItems) {
		if (filteredItems == 'ALL') {
			if (confirm('This will remove all of your selected filters and could not be reversed. Are you sure you want to do that?')) {
				this.resetForm.next(true);
				this.filterTrayCacheService.getCacheNameData(this.CACHE_NAME);
				this.resetPeriodicityFilter.next(true);
				this.filterCount = '';
			}
		}
		this.saveInCache(this.cacheObj.isAutoRefresh, false);
		this.CreateFilterSummaryChips();
	}

	private saveFilterForm(autoRefreshStatus: boolean): void {
		this.deprecatedForm.setValue({
			isAutoRefresh: autoRefreshStatus === undefined || autoRefreshStatus === false ? false : true,
		});
	}

	saveInCache(autoRefreshStatus, AutoToggle) {
		this.saveFilterForm(autoRefreshStatus);
		localStorage.setItem(this.CACHE_NAME, JSON.stringify(this.selectedFilters));
		this.cacheObj = JSON.parse(localStorage.getItem(this.CACHE_NAME) || '{}');
		if(this.cacheObj.productionProjectName == null){
			this.cacheObj.productionProjectName = '';
		}
		if (!AutoToggle) {
			this.defaultfilter = [];
			this.defaultfilter.push({
				data: this.selectedFilters,
				slice: window.location.href.split('/')[3],
				user: localStorage.getItem('username'),
			});
			localStorage.setItem(this.CACHE_NAME + '-DefaultFilter', JSON.stringify(this.defaultfilter));
			let nofilter = 0;
			let selectedfilter = '';
			// eslint-disable-next-line @typescript-eslint/no-unused-vars
			this.currentfilter.forEach((i, a) => {
				// if (i.filter == this.defaultoption) {
				if(JSON.stringify(i.data) == JSON.stringify(this.cacheObj)){
					nofilter++;
					selectedfilter = i.filter;
				}
			});
			if (nofilter == 0) {
				this.defaultoption = 'Default';
			} else {
				this.defaultoption = selectedfilter;
			}
		}
		localStorage.setItem(this.CACHE_NAME + '-LastFilter', this.defaultoption);
		this.updateFilterFGValues();
	}

	saveInQuickFilter(autoRefreshStatus, filterName) {
		this.saveFilterForm(autoRefreshStatus);

		this.currentfilter = JSON.parse(localStorage.getItem('QuickFilter') || '[]');
		this.currentfilter.push({
			data: this.selectedFilters,
			filter: filterName,
			slice: window.location.href.split('/')[3],
			user: localStorage.getItem('username'),
		});
		this.defaultoption = filterName;
		const oldcache = this.cacheObj;
		const olddefault = localStorage.getItem(this.CACHE_NAME + '-DefaultFilter');
		this.confirmApply(autoRefreshStatus);
		this.cacheObj = oldcache;
		localStorage.setItem(this.CACHE_NAME + '-DefaultFilter', olddefault || '[]');
		localStorage.setItem('QuickFilter', JSON.stringify(this.currentfilter));
		localStorage.setItem(this.CACHE_NAME + '-LastFilter', filterName);
	}

	selectRow(row: LdGeneral) {
		this.ldGeneralDetail = [];
		row.isSelected = true;

		this.dataSource.data.forEach((element) => {
			if (row.loadDefinitionId != element.loadDefinitionId) {
				element.isSelected = false;
				element.isExpanded = false;
			}
		});

		this.selectedRow = row;
		this.LdGeneralService.getDetailAsync(row.loadDefinitionId).subscribe((result) => {
			// set runtime column
			result.forEach((element) => {
				element.runtime = this.helperService.calculateDuration(element.started, element.finished, true);
			});

			this.helperService.floatingPanelData$.next({ data: result, jobType: JobTypes.ldGeneral });
		});
	}

	apply(): void {
		// do implementation
		this.refreshedDateTime = false;
		this.isLoading = true;
		this.destroy$.next();
		this.filterTrayComponent?.showSpinner(true);
		this.LdGeneralService.getAsync(
			this.selectedFilters.countries?.map((country) => country),
			this.selectedFilters.periodIds?.map((period) => period),
			this.selectedFilters.periodicityIds ? [this.selectedFilters.periodicityIds] : [],
			this.selectedFilters.users?.map((users) => users.userName),
			this.selectedFilters.status?.map((status) => status),
			this.selectedFilters.loadDefinitionIds,
			this.selectedFilters.productionProjectIds,
			this.selectedFilters.productionProjectName,
			this.selectedFilters.productionProjectType?.map((ppt) => ppt),
			this.selectedFilters.startDate ? this.selectedFilters.startDate : null,
			this.selectedFilters.endDate ? moment(this.selectedFilters.endDate).format().split('T')[0] + 'T23:59:59.999Z' : null
		)
		.pipe(
			takeUntil(this.destroy$)
		).subscribe((result: any) => {
			this.refreshedDateTime = true;
			if (result.moreRecordsAvailable) {
				this.notify('More than 1000 results exist!');
			}
			if (result.count === 0) {
				this.notify('No result found!');
				this.ldGeneralData = [];
			}
			this.filterTrayComponent?.showSpinner(false);
			this.isLoading = false;
			this.ldGeneralData = result.records;
			// add elements to detail menu
			result.records.forEach((element: any) => {
				const currentUrl = window.location.href;
				let link = '';
				if(currentUrl.includes('t1')){
					link = 'https://facttool.t1.dwh.in.gfk.com/loaddefinition/view/'+element.loadDefinitionId;
				}
				else if(currentUrl.includes('t3')){
					link = 'https://facttool.t3.dwh.in.gfk.com/loaddefinition/view/'+element.loadDefinitionId;
				}
				else if(currentUrl.includes('t4')){
					link = 'https://facttool.t4.dwh.in.gfk.com/loaddefinition/view/'+element.loadDefinitionId;
				}
				else if(currentUrl.includes('t5')){
					link = 'https://facttool.t5.dwh.in.gfk.com/loaddefinition/view/'+element.loadDefinitionId;
				}
				else {
					link = 'https://facttool.prod.dwh.in.gfk.com/loaddefinition/view/'+element.loadDefinitionId;
				}
				element.detailMenuConfig = [
					{
						menuText: 'Open LD in FTX',
						menuIcon: 'open_in_new',
						isFTXLink: true,
						link: link
					},
					{
						menuText: 'Open LD in Fact Tool',
						menuIcon: 'open_in_new',
						service: 'openfacttool',
						params: { loaddefinitionid: element.loadDefinitionId },
					},
					{
						menuText: 'View LD Properties',
						menuIcon: 'open_in_new',
						service: 'showldproperties',
						params: { loaddefinitionid: element.loadDefinitionId },
					},
					{
						menuText: 'View Messages',
						menuIcon: 'open_in_new',
						isMessageLink: true,
						params: { loaddefinitionid: element.loadDefinitionId },
					},
				];
			});

			this.dataSource = new MatTableDataSource<LdGeneral>(result.records);
			this.dataSource.sort = this.sort;
			this.paginator.pageIndex = 0;
			this.dataSource.paginator = this.paginator;
			this.table.dataSource = this.dataSource;
		},
		(error) => {
			if (error.status === 504) {
				this.notify('Gateway Timeout. The server took too long to respond. Please try again later.');
			} 
			else {
				this.notify(`An error occurred: ${error.message}`);
			}
			this.isLoading = false;
			this.filterTrayComponent?.showSpinner(false);
		});
	}

	confirmApply(autoRefreshStatus) {
		this.saveInCache(autoRefreshStatus, false);
		this.CreateFilterSummaryChips();

		let count;
		this.filterService.filtersSelected$.subscribe((selectedFilters: FilterRecords) => {
			count = Number(Object.keys(selectedFilters).length);
		});

		if (count > 0) {
			this.apply();
		} else {
			if (confirm('Loading results without any filters selected will take some time to load. Are you sure you want to proceed?')) {
				this.apply();
			}
		}
	}

	clickNextButton() {
		this.selectedRow.isSelected = false;
	}

	rowToggleSelection(ev, item) {
		item.isChecked = ev.checked;
	}

	checkAtleastOneRowChecked() {
		return this.dataSource.data.filter((i) => i.isChecked).length > 0;
	}

	openJiraTicket(): void {
		const checkedList = this.dataSource.data.filter((i) => i.isChecked);
		const dialogRef = this.dialog.open(CreateTicketDialogComponent, {
			width: '1000px',
			height: '487px',
			data: {
				ids: checkedList.map((i) => {
					return i.loadDefinitionId;
				}),
				jobType: this.jobType,
				title: 'LdGeneral',
			},
		});

		dialogRef.afterClosed().subscribe((resSubmit) => {
			if (resSubmit === true) {
				this.dataSource.data.forEach((i) => (i.isChecked = false));
				this.selection.clear();
				this.IsSelectAll = false;
			}
		});
	}

	private notify(message: string) {
		this.snackBar.openFromComponent(DefaultSnackbarComponent, {
			duration: 10 * 1000,
			data: message,
		});
	}

	public fillProductionProjectType() {
		this.productionProjectTypeService.getAsync(JobTypes.ldGeneral).subscribe(
			(res: TagItem[]) => {
				res.forEach((i) => {
					this.productionProjectTypes.set(i.id.toString(), i.name);
				});
			},
			(error) => {
				console.log({ error });
			}
		);
	}

	public isShownFilterTrayEventHandler($event: boolean) {
		this.filterTrayComponent?.toggleShowTray($event);
		this.FilterTrayOpenFlag = $event;
	}

	onTraySizeToggle() {
		this.FilterTrayOpenFlag = !this.FilterTrayOpenFlag;
		return '100%';
	}

	selectRows(selectrow) {
		this.IsSelectAll = !this.IsSelectAll;
		let endIndex: number;
		const page = this.dataSource.paginator !== null ? this.dataSource.paginator.pageSize : 0;
		const pageindex = this.dataSource.paginator !== null ? this.dataSource.paginator.pageIndex : 0;

		if (!selectrow.checked) {
			this.selection.clear();
			if (this.dataSource.data.length > (pageindex + 1) * page) {
				endIndex = (pageindex + 1) * page;
			} else {
				endIndex = this.dataSource.data.length;
			}

			for (let index = pageindex * page; index < endIndex; index++) {
				const row = this.dataSource.data[index];
				this.selection.deselect(row);
				this.dataSource.data[index].isChecked = false;
			}
		} else {
			if (this.dataSource.data.length > (pageindex + 1) * page) {
				endIndex = (pageindex + 1) * page;
			} else {
				endIndex = this.dataSource.data.length;
			}
			for (let index = pageindex * page; index < endIndex; index++) {
				const row = this.dataSource.sortData(this.dataSource.data, this.sort)[index];
				this.selection.select(row);
				this.dataSource.sortData(this.dataSource.data, this.sort)[index].isChecked = true;
			}
		}
	}

	getSelectedFilters(selectedFilters: any) {
		this.cacheObj = selectedFilters;
		this.selectedFilters = selectedFilters;
	}
}
