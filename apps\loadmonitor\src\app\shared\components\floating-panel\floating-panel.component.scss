th {
  min-height: 30px;
  vertical-align: middle;

  padding-right: 7px;
  padding-left: 7px;
  word-wrap: break-word;
  min-width: 100px;
}

.full-width-table td {
  padding-right: 7px;
  padding-left: 7px;
  word-wrap: break-word;
  min-width: 100px;
}

table tbody {
  width: 100%;
}

th.mat-header-cell:first-of-type {
  padding-left: 7px;
}

.columns{
  display: inline-grid;
}

.mat-margin-left {
  margin-left: 8px;
}

