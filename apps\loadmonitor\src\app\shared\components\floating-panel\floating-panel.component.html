<div style="margin-left: 15px">
	<section align="left">
		<lm-progress-spinner *ngIf="showLoading" class="progress-spinner"></lm-progress-spinner>
	</section>
</div>

<mat-card *ngIf="showEmptyMessage && dataSource.data.length === 0">No property information available</mat-card>

<table mat-table [dataSource]="dataSource" class="full-width-table js-tbl-log-messages-result" *ngIf="dataSource.data.length > 0">
	<!--- Note that these columns can be defined in any order.
        The actual rendered columns are set as a property on the row definition" -->

	

	<!-- Name Column -->
	<ng-container matColumnDef="message">
		<th mat-header-cell *matHeaderCellDef>Description</th>
		<td mat-cell *matCellDef="let element">{{ element.message }}</td>
	</ng-container>

	<!-- Symbol Column -->
	<ng-container matColumnDef="occurred">
		<th mat-header-cell *matHeaderCellDef>Occured</th>
		<td mat-cell *matCellDef="let element">{{ element.occurred | date: date_with_time }}</td>
	</ng-container>

	<!-- Symbol Column -->
	<ng-container matColumnDef="logId">
		<th mat-header-cell *matHeaderCellDef>Log ID</th>
		<td mat-cell *matCellDef="let element">{{ element.logId }}</td>
	</ng-container>

	<tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
	<tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
</table>


<!-- CSV IMPORT -->
<mat-card *ngIf="showEmptyMessageForCsvImport && dataSourceCsv.data.length === 0">No property information available</mat-card>

<table mat-table [dataSource]="dataSourceCsv" class="full-width-table js-tbl-log-messages-result" *ngIf="dataSourceCsv.data.length > 0">
	<!--- Note that these columns can be defined in any order.
        The actual rendered columns are set as a property on the row definition" -->

	<!-- Position Column -->
	<ng-container matColumnDef="logEntry">
		<th mat-header-cell *matHeaderCellDef>Log Entry</th>
		<td mat-cell *matCellDef="let element">{{ element.logEntry }}</td>
	</ng-container>

	<!-- Name Column -->
	<ng-container matColumnDef="createdBy">
		<th mat-header-cell *matHeaderCellDef>Created By</th>
		<td mat-cell *matCellDef="let element">{{ element.createdBy }}</td>
	</ng-container>

	<!-- Symbol Column -->
	<ng-container matColumnDef="created">
		<th mat-header-cell *matHeaderCellDef>Created When</th>
		<td mat-cell *matCellDef="let element">{{ element.created | date: date_with_time }}</td>
	</ng-container>

	<tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
	<tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
</table>


<!-- SHOP MASTER -->
<mat-card *ngIf="showEmptyMessageForShopMasterData && dataSourceShopMaster.data.length === 0">No property information available</mat-card>

	<table
		mat-table
		[dataSource]="dataSourceShopMaster"
		class="full-width-table js-tbl-log-messages-result"
		*ngIf="dataSourceShopMaster.data.length > 0"
	>
	<!--- Note that these columns can be defined in any order.
        The actual rendered columns are set as a property on the row definition" -->
	<!-- Position Column -->

	<ng-container matColumnDef="dwhUnitId">
		<th mat-header-cell *matHeaderCellDef>DWH Unit Id</th>
		<td mat-cell *matCellDef="let element">{{ element.dwhUnitId }}</td>
	</ng-container>

	<!-- Name Column -->
	<ng-container matColumnDef="rowsUpdatedDwhMasterData">
		<th mat-header-cell *matHeaderCellDef>Rows Updated DWH (Master Data)</th>
		<td mat-cell *matCellDef="let element">{{ element.rowsUpdatedDwhMasterData }}</td>
	</ng-container>

	<!-- Name Column -->
	<ng-container matColumnDef="rowsUpdatedQcMasterData">
		<th mat-header-cell *matHeaderCellDef>Rows Updated QC (Master Data)</th>
		<td mat-cell *matCellDef="let element">{{ element.rowsUpdatedQcMasterData }}</td>
	</ng-container>

	<!-- Symbol Column -->
	<ng-container matColumnDef="rowsUpdatedDwhRearrange">
		<th mat-header-cell *matHeaderCellDef>Rows Updated DWH (Rearrange)</th>
		<td mat-cell *matCellDef="let element">{{ element.rowsUpdatedDwhRearrange }}</td>
	</ng-container>

	<!-- Symbol Column -->
	<ng-container matColumnDef="rowsUpdatedQcRearrange">
		<th mat-header-cell *matHeaderCellDef>Rows Updated QC (Rearrange)</th>
		<td mat-cell *matCellDef="let element">{{ element.rowsUpdatedQcRearrange }}</td>
	</ng-container>

	<!-- Symbol Column -->
	<ng-container matColumnDef="rowsUpdatedDwhComposedFeat">
		<th mat-header-cell *matHeaderCellDef>Rows Updated DWH (Composed Feat)</th>
		<td mat-cell *matCellDef="let element">{{ element.rowsUpdatedDwhComposedFeat }}</td>
	</ng-container>

	<!-- Symbol Column -->
	<ng-container matColumnDef="rowsUpdatedQcComposedFeat">
		<th mat-header-cell *matHeaderCellDef>Rows Updated QC (Composed Feat)</th>
		<td mat-cell *matCellDef="let element">{{ element.rowsUpdatedQcComposedFeat }}</td>
	</ng-container>

	<tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
	<tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
</table>


<!-- LD General -->
<mat-card *ngIf="showEmptyMessageForLdGeneral && dataSourceLdGeneral.data.length === 0">No property information available</mat-card>

	<table
		mat-table
		[dataSource]="dataSourceLdGeneral"
		class="full-width-table js-tbl-log-messages-result"
		*ngIf="dataSourceLdGeneral.data.length > 0"
	>
	<!--- Note that these columns can be defined in any order.
        The actual rendered columns are set as a property on the row definition" -->
	<!-- Position Column -->

	<ng-container matColumnDef="loadId">
		<th mat-header-cell *matHeaderCellDef>Load Id</th>
		<td mat-cell *matCellDef="let element">{{ element.loadId }}</td>
	</ng-container>

	<ng-container matColumnDef="step">
		<th mat-header-cell *matHeaderCellDef>Step</th>
		<td mat-cell *matCellDef="let element">{{ element.step }}</td>
	</ng-container>

	<ng-container matColumnDef="status">
		<th mat-header-cell *matHeaderCellDef>Status</th>
		<td mat-cell *matCellDef="let element">{{ element.status }}</td>
	</ng-container>

	<ng-container matColumnDef="qcProjectId">
		<th mat-header-cell *matHeaderCellDef>QC Project Id</th>
		<td mat-cell *matCellDef="let element">{{ element.qcProjectId }}</td>
	</ng-container>

	<ng-container matColumnDef="baseProject">
		<th mat-header-cell *matHeaderCellDef>Base Project</th>
		<td mat-cell *matCellDef="let element">{{ element.baseProject }}</td>
	</ng-container>

	<ng-container matColumnDef="period">
		<th mat-header-cell *matHeaderCellDef>Period</th>
		<td mat-cell *matCellDef="let element">{{ element.period }}</td>
	</ng-container>

	<ng-container matColumnDef="jobId">
		<th mat-header-cell *matHeaderCellDef>JEE Job Id</th>
		<td mat-cell *matCellDef="let element">{{ element.jobId }}</td>
	</ng-container>

	<ng-container matColumnDef="jeeJobInfo">
		<th mat-header-cell *matHeaderCellDef>JEE Job Info</th>
		<td mat-cell *matCellDef="let element">
			<lm-jee-job-info jobId="{{ element.jobId }}"></lm-jee-job-info>
		</td>
	</ng-container>

	<ng-container matColumnDef="createdBy">
		<th mat-header-cell *matHeaderCellDef>Created By</th>
		<td mat-cell *matCellDef="let element">{{ element.createdBy }}</td>
	</ng-container>

	<ng-container matColumnDef="created">
		<th mat-header-cell *matHeaderCellDef>Created On</th>
		<td mat-cell *matCellDef="let element">{{ element.created | date: date_with_time }}</td>
	</ng-container>

	<ng-container matColumnDef="started">
		<th mat-header-cell *matHeaderCellDef>Started On</th>
		<td mat-cell *matCellDef="let element">{{ element.started | date: date_with_time }}</td>
	</ng-container>

	<ng-container matColumnDef="runtime">
		<th mat-header-cell *matHeaderCellDef mat-sort-header>Runtime</th>
		<td mat-cell *matCellDef="let element">{{ element.runtime }}</td>
	</ng-container>

	<ng-container matColumnDef="finished">
		<th mat-header-cell *matHeaderCellDef>Finished On</th>
		<td mat-cell *matCellDef="let element">{{ element.finished | date: date_with_time }}</td>
	</ng-container>

	<ng-container matColumnDef="message">
		<th mat-header-cell *matHeaderCellDef>Message</th>
		<td mat-cell *matCellDef="let element">{{ element.message }}</td>
	</ng-container>

	<tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
	<tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
</table>


<!-- MIGRATION RULE RUN -->
<mat-card *ngIf="showEmptyMessageForMigrationRuleRun && dataSourceMigrationRuleRun.data.length === 0">No property information available</mat-card>

<table
	mat-table
	[dataSource]="dataSourceMigrationRuleRun"
	class="full-width-table js-tbl-log-messages-result"
	*ngIf="dataSourceMigrationRuleRun.data.length > 0"
>
	<!--- Note that these columns can be defined in any order.
        The actual rendered columns are set as a property on the row definition" -->
	<ng-container matColumnDef="messageType">
		<th mat-header-cell *matHeaderCellDef>Message Type</th>
		<td mat-cell *matCellDef="let element">{{ element.messageType }}</td>
	</ng-container>

	<ng-container matColumnDef="message">
		<th mat-header-cell *matHeaderCellDef>Message</th>
		<td mat-cell *matCellDef="let element">{{ element.message }}</td>
	</ng-container>

	<tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
	<tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
</table>

<!-- PUBLISH REPUBLISH-->
<mat-card *ngIf="showEmptyMessageForPublishRepublish && dataSourcePublishRepublish.data.length === 0">No property information available</mat-card>

<table
	mat-table
	[dataSource]="dataSourcePublishRepublish"
	class="full-width-table js-tbl-log-messages-result"
	*ngIf="dataSourcePublishRepublish.data.length > 0"
	multiTemplateDataRows
>
	<ng-container matColumnDef="jobId">
		<th mat-header-cell *matHeaderCellDef>jobId</th>
		<td mat-cell *matCellDef="let element">{{ element.jobId }}</td>
	</ng-container>

	<ng-container matColumnDef="jeeJobInfo">
		<th mat-header-cell *matHeaderCellDef>JEE Job Info</th>
		<td mat-cell *matCellDef="let element">
			<lm-jee-job-info jobId="{{ element.jobId }}"></lm-jee-job-info>
		</td>
	</ng-container>

	<ng-container matColumnDef="status">
		<th mat-header-cell *matHeaderCellDef mat-sort-header>Status</th>
		<td mat-cell *matCellDef="let element">{{ element.status }}</td>
	</ng-container>

	<ng-container matColumnDef="message">
		<th mat-header-cell *matHeaderCellDef mat-sort-header>Message</th>
		<td mat-cell *matCellDef="let element">{{ element.message }}</td>
	</ng-container>

	<ng-container matColumnDef="operation">
		<th mat-header-cell *matHeaderCellDef mat-sort-header>Operation</th>
		<td mat-cell *matCellDef="let element">{{ element.operation }}</td>
	</ng-container>

	<ng-container matColumnDef="serverName">
		<th mat-header-cell *matHeaderCellDef mat-sort-header>ServerName</th>
		<td mat-cell *matCellDef="let element">{{ element.serverName }}</td>
	</ng-container>

	<ng-container matColumnDef="specification">
		<th mat-header-cell *matHeaderCellDef mat-sort-header>Specification</th>
		<td mat-cell *matCellDef="let element">{{ element.specification }}</td>
	</ng-container>

	<ng-container matColumnDef="created">
		<th mat-header-cell *matHeaderCellDef mat-sort-header>Created On</th>
		<td mat-cell *matCellDef="let element">{{ element.created | date: date_with_time }}</td>
	</ng-container>

	<ng-container matColumnDef="started">
		<th mat-header-cell *matHeaderCellDef mat-sort-header>Started On</th>
		<td mat-cell *matCellDef="let element">
			{{ element.started | date: date_with_time }}
		</td>
	</ng-container>

	<ng-container matColumnDef="finished">
		<th mat-header-cell *matHeaderCellDef mat-sort-header>Finished On</th>
		<td mat-cell *matCellDef="let element">
			{{ element.finished | date: date_with_time }}
		</td>
	</ng-container>

	<tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
	<tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
</table>

<!-- FEATURE MOVE -->
<mat-card *ngIf="showEmptyMessageForFeatureMove && dataSourceFeatureMove.data.length === 0">No property information available</mat-card>

<table
	mat-table
	[dataSource]="dataSourceFeatureMove"
	class="full-width-table js-tbl-log-messages-result"
	*ngIf="dataSourceFeatureMove.data.length > 0"
>
	<!--- Note that these columns can be defined in any order.
        The actual rendered columns are set as a property on the row definition" -->
	<ng-container matColumnDef="featureId">
		<th mat-header-cell *matHeaderCellDef>Feature Id</th>
		<td mat-cell *matCellDef="let element">{{ element.featureId }}</td>
	</ng-container>

	<ng-container matColumnDef="featureName">
		<th mat-header-cell *matHeaderCellDef>Feature Name</th>
		<td mat-cell *matCellDef="let element">{{ element.featureName }}</td>
	</ng-container>

	<ng-container matColumnDef="productGroupId">
		<th mat-header-cell *matHeaderCellDef>Product Group Id</th>
		<td mat-cell *matCellDef="let element">{{ element.productGroupId }}</td>
	</ng-container>

	<ng-container matColumnDef="productGroupName">
		<th mat-header-cell *matHeaderCellDef>Product Group Name</th>
		<td mat-cell *matCellDef="let element">{{ element.productGroupName }}</td>
	</ng-container>

	<ng-container matColumnDef="oldPosition">
		<th mat-header-cell *matHeaderCellDef>Old Position</th>
		<td mat-cell *matCellDef="let element">{{ element.oldPosition }}</td>
	</ng-container>

	<ng-container matColumnDef="newPosition">
		<th mat-header-cell *matHeaderCellDef>New Position</th>
		<td mat-cell *matCellDef="let element">{{ element.newPosition }}</td>
	</ng-container>

	<ng-container matColumnDef="started">
		<th mat-header-cell *matHeaderCellDef>Started On</th>
		<td mat-cell *matCellDef="let element">{{ element.started }}</td>
	</ng-container>

	<ng-container matColumnDef="finished">
		<th mat-header-cell *matHeaderCellDef>Finished On</th>
		<td mat-cell *matCellDef="let element">{{ element.finished }}</td>
	</ng-container>

	<tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
	<tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
</table>


<!-- Exports -->
<mat-card *ngIf="showEmptyMessageForExport && dataSourceExports.data.length === 0">No property information available</mat-card>

<table mat-table [dataSource]="dataSourceExports" class="full-width-table js-tbl-log-messages-result" *ngIf="dataSourceExports.data.length > 0">
	<!--- Note that these columns can be defined in any order.
        The actual rendered columns are set as a property on the row definition" -->
	<ng-container matColumnDef="deliveryId">
		<th mat-header-cell *matHeaderCellDef>Delivery Id</th>
		<td mat-cell *matCellDef="let element">{{ element.deliveryId }}</td>
	</ng-container>

	<ng-container matColumnDef="deliveryType">
		<th mat-header-cell *matHeaderCellDef>Delivery Type</th>
		<td mat-cell *matCellDef="let element">{{ element.deliveryType }}</td>
	</ng-container>

	<ng-container matColumnDef="emailaddress">
		<th mat-header-cell *matHeaderCellDef>Email Address</th>
		<td mat-cell *matCellDef="let element">{{ element.emailaddress }}</td>
	</ng-container>

	<ng-container matColumnDef="ftpHost">
		<th mat-header-cell *matHeaderCellDef>FtpHost</th>
		<td mat-cell *matCellDef="let element">{{ element.ftpHost }}</td>
	</ng-container>

	<ng-container matColumnDef="ftpRemoteDir">
		<th mat-header-cell *matHeaderCellDef>FtpRemoteDir</th>
		<td mat-cell *matCellDef="let element">{{ element.ftpRemoteDir }}</td>
	</ng-container>

	<ng-container matColumnDef="ftpUsername">
		<th mat-header-cell *matHeaderCellDef>FtpUsername</th>
		<td mat-cell *matCellDef="let element">{{ element.ftpUsername }}</td>
	</ng-container>

	<ng-container matColumnDef="message">
		<th mat-header-cell *matHeaderCellDef>Message</th>
		<td mat-cell *matCellDef="let element">{{ element.message }}</td>
	</ng-container>

	<ng-container matColumnDef="status">
		<th mat-header-cell *matHeaderCellDef>Status</th>
		<td mat-cell *matCellDef="let element">{{ element.status }}</td>
	</ng-container>

	<ng-container matColumnDef="countryGroup">
		<th mat-header-cell *matHeaderCellDef>CounytryGroup</th>
		<td mat-cell *matCellDef="let element">{{ element.countryGroup }}</td>
	</ng-container>

	<ng-container matColumnDef="instrument">
		<th mat-header-cell *matHeaderCellDef>Instrument</th>
		<td mat-cell *matCellDef="let element">{{ element.instrument }}</td>
	</ng-container>

	<ng-container matColumnDef="productGroup">
		<th mat-header-cell *matHeaderCellDef>ProductGroup</th>
		<td mat-cell *matCellDef="let element">{{ element.productGroup }}</td>
	</ng-container>

	<ng-container matColumnDef="client">
		<th mat-header-cell *matHeaderCellDef>Client</th>
		<td mat-cell *matCellDef="let element">{{ element.client }}</td>
	</ng-container>

	<tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
	<tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
</table>

