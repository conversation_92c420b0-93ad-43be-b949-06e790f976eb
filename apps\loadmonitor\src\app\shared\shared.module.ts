import { NgModule } from '@angular/core';
import { CommonModule, DatePipe } from '@angular/common';
import { PageNotFoundComponent } from './components/page-not-found/page-not-found.component';
import { DefaultSnackbarComponent } from './components/default-snackbar/default-snackbar.component';
import { MatTableModule } from '@angular/material/table';
import { MatIconModule } from '@angular/material/icon';
import { MatFormFieldModule } from '@angular/material/form-field';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatInputModule } from '@angular/material/input';
import { MatChipsModule } from '@angular/material/chips';
import { MatSelectModule } from '@angular/material/select';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { UserChipAutocompleteComponent } from '@loadmonitor/shared/components/filter-elements/user-chip-autocomplete/user-chip-autocomplete.component';
import { PeriodicityFilterComponent } from '@loadmonitor/shared/components/filter-elements/periodicity-filter/periodicity-filter.component';
import { HistoryFilterComponent } from './components/filter-elements/history-filter/history-filter.component';
import { CountryChipAutocompleteComponent } from './components/filter-elements/country-chip-autocomplete/country-chip-autocomplete.component';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { JEEJobInfoDialogComponent, JEEJobInfoComponent } from './components/dialog-elements/jee-job-info-dialog/jee-job-info-dialog.component';
import { MatButtonModule } from '@angular/material/button';
import { ProductionProjectTypeFilterComponent } from './components/filter-elements/production-project-type-filter/production-project-type-filter.component';
import { StatusChipAutocompleteComponent } from './components/filter-elements/status-chip-autocomplete/status-chip-autocomplete.component';
import { DetailMenuComponent } from './components/detail-menu/detail-menu.component';
import { FloatingPanelComponent } from './components/floating-panel/floating-panel.component';
import { MatMenuModule } from '@angular/material/menu';
import { DomainProductGroupChipAutocompleteComponent } from '@loadmonitor/shared/components/filter-elements/domain-product-group-chip-autocomplete/domain-product-group-chip-autocomplete.component';
import { MatDialogModule } from '@angular/material/dialog';
import { CategoryChipAutocompleteComponent } from './components/filter-elements/category-chip-autocomplete/category-chip-autocomplete.component';
import { ChannelChipAutocompleteComponent } from './components/filter-elements/channel-chip-autocomplete/channel-chip-autocomplete.component';
import { SectorChipAutocompleteComponent } from './components/filter-elements/sector-chip-autocomplete/sector-chip-autocomplete.component';
import { ExportIdChipInputComponent } from './components/filter-elements/export-id-chip-input/export-id-chip-input.component';
import { NumericChipComponent } from './components/filter-elements/numeric-chip/numeric-chip.component';
import { BasicBooleanComponent } from './components/filter-elements/basic-boolean/basic-boolean.component';
import { NavigationComponent } from '@loadmonitor/shared/components/navigation/navigation.component';
import { MatTabsModule } from '@angular/material/tabs';
import { RouterModule } from '@angular/router';
import { MatNativeDateModule } from '@angular/material/core';
import { MatListModule } from '@angular/material/list';
import { MatRadioModule } from '@angular/material/radio';
import { MatCardModule } from '@angular/material/card';
import { MatSnackBarModule } from '@angular/material/snack-bar';
import { UnitTypeChipAutoCompleteComponent } from './components/filter-elements/unit-type/unit-type.component';
import { ProgressSpinnerComponent } from './components/progress-spinner/progress-spinner.component';
import { BaseProjectTypeAutocompleteComponent } from './components/filter-elements/base-project-type-autocomplete/base-project-type-autocomplete.component';
import { JobTypeAutocompleteComponent } from './components/filter-elements/job-type-autocomplete/job-type-autocomplete.component';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { SuccessJiraTicketCreationComponent } from './components/success-jira-ticket-creation/success-jira-ticket-creation.component';
import { CreateTicketDialogComponent } from './components/create-ticket-dialog/create-ticket-dialog.component';
import { FilterSummaryComponent } from './components/filter-summary/filter-summary.component';
import { NgLibModule } from '@gfk/ng-lib';
import { QuickFilterComponent } from './components/quick-filter/quick-filter.component';
import { ChipAutocompleteComponent } from "../../../../../libs/lmx-lib/src/lib/components/chip-autocomplete/chip-autocomplete.component";
import { DateRangeAutocompleteComponent } from '@dwh/lmx-lib/src/lib/components/date-range-autocomplete/date-range-autocomplete.component';
import { RefreshedDateTimeComponent } from './components/refreshed-date-time/refreshed-date-time.component';
import { ChipTextComponent } from '@dwh/lmx-lib/src/lib/components/chip-text/chip-text.component';
import { ChipSelectComponent } from '@dwh/lmx-lib/src/lib/components/chip-select/chip-select.component';
import { ChipNumericComponent } from '@dwh/lmx-lib/src/lib/components/chip-numeric/chip-numeric.component';
import { FilterTrayFormComponent } from './components/filter-tray-form/filter-tray-form.component';
import { VersionComponent } from './components/version/version.component';

const components = [
	UserChipAutocompleteComponent,
	PeriodicityFilterComponent,
	ProductionProjectTypeFilterComponent,
	StatusChipAutocompleteComponent,
	HistoryFilterComponent,
	CountryChipAutocompleteComponent,
	JEEJobInfoComponent,
	JEEJobInfoDialogComponent,
	PageNotFoundComponent,
	DefaultSnackbarComponent,
	DetailMenuComponent,
	FloatingPanelComponent,
	DomainProductGroupChipAutocompleteComponent,
	CategoryChipAutocompleteComponent,
	ChannelChipAutocompleteComponent,
	SectorChipAutocompleteComponent,
	ExportIdChipInputComponent,
	NumericChipComponent,
	BasicBooleanComponent,
	NavigationComponent,
	UnitTypeChipAutoCompleteComponent,
	BaseProjectTypeAutocompleteComponent,
	JobTypeAutocompleteComponent,
	CreateTicketDialogComponent,
	FilterSummaryComponent,
	QuickFilterComponent,
	RefreshedDateTimeComponent,
	FilterTrayFormComponent,
	VersionComponent
];

const modules = [
	CommonModule,
	MatTableModule,
	MatIconModule,
	MatFormFieldModule,
	FormsModule,
	ReactiveFormsModule,
	MatInputModule,
	MatChipsModule,
	MatSelectModule,
	MatAutocompleteModule,
	MatDatepickerModule,
	MatButtonModule,
	MatMenuModule,
	MatDialogModule,
	MatTabsModule,
	RouterModule,
	MatNativeDateModule,
	MatListModule,
	MatRadioModule,
	MatCardModule,
	MatSnackBarModule,
	MatCheckboxModule,
	NgLibModule,
];

@NgModule({
    declarations: [...components, ProgressSpinnerComponent, SuccessJiraTicketCreationComponent],
    exports: [...components, ...modules, ProgressSpinnerComponent],
    providers: [DatePipe],
    imports: [...modules, ChipAutocompleteComponent, DateRangeAutocompleteComponent, ChipTextComponent, ChipSelectComponent, ChipNumericComponent]
})
export class SharedModule {}
