import { Component } from '@angular/core';
import { COMMA, ENTER } from '@angular/cdk/keycodes';
import { MatChipInputEvent } from '@angular/material/chips';

@Component({
  selector: 'lm-export-id-chip-input',
  templateUrl: './export-id-chip-input.component.html',
  styleUrls: ['./export-id-chip-input.component.scss']
})
export class ExportIdChipInputComponent {

  exportIds: number[] = [];

  readonly separatorKeysCodes = [ENTER, COMMA] as const;

  addChips(event: MatChipInputEvent, tags: number[]): void {
    const value = (event.value || '').trim();
    if (!Number(value)) {
      return;
    }

    if (value) {
      //eslint-disable-next-line radix
      tags.push(Number.parseInt(value));
    }

    event.chipInput?.clear();
  }

  removeChips(item: number, tags: number[]): void {
    const index = tags.indexOf(item);

    if (index >= 0) {
      tags.splice(index, 1);
    }
  }
}
