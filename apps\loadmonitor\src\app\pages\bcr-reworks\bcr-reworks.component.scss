@use 'apps/loadmonitor/src/styles/theme/gfk-light.palette' as gfk;
@use 'sass:map' as map;
@use '@gfk/style' as gfk-style;


.full-width-table {
	td {
		padding-right: 7px;
		padding-left: 7px;
		border: 1px solid rgba(211, 211, 211, 0.247);
		word-wrap: break-word;
	}

	tr {
		&:nth-child(2n) {
			background-color: map.get(gfk.$palette, 'bg-light');
		}
	}
}

table {
	width: 3000px;
}

table th {
	white-space: nowrap;
}
.alignleft {
	float: left;
}

.alignright {
	float: right;
	margin-top: 8px;
	margin-bottom: 8px;
	margin-right: 36px;
	padding: 1px 20px;
	font-family: 'Lato';
}

.mat-accordion-align {
	display: inline-block;
	width: 100%;
}
.chip-list {
	width: 100%;
}

.columns {
	display: inline-grid;
}

th.mat-sort-header-sorted {
	color: black;
}

.option-checkbox {
	margin: 0 12px;
}

.chip-list-wrapper {
	min-height: 3em;
}

.selectAllPad {
	padding-left: 8px !important;
}

mat-paginator {
  display: flex;
  justify-content: start;
}

.autoRefresh{
  padding-right: 1%;
}

.version {
	color: #808080;
}