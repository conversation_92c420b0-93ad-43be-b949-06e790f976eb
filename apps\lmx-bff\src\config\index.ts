import { Config, DomainProxy } from '@gfk/bff';
import { version } from '../../../../package.json';
const config: Config = {
	environment: 'local',
	cookie: {
		keys: ['CAT KEYBOARD CATERPILLAR'],
		// maxAge: 1000
	},
	proxy: [
		new DomainProxy({
			domain: 'https://loadmonitor-api.prod.dwh.in.gfk.com',
			route: '/lmx',
		}),
		new DomainProxy({
			domain: 'https://jee-api.prod.dwh.in.gfk.com',
			route: '/jee',
		}),
    new DomainProxy({
      domain: 'https://country-api-shared.prod.dwh.in.gfk.com',
      route: '/country-api',
		}),
    new DomainProxy({
			domain: 'https://date-api-shared.prod.dwh.in.gfk.com',
			route: '/date-api',
		}),
	],
	identityServer: {
		codeFlow: {
			knownAuthorities: ['https://GfkDataPlatform.b2clogin.com'],
			clientId: 'a4bb765c-e9f7-476e-889c-02c40de2e001', // TBD: Has to be adapted in ENV
			clientSecret: '****************************************', // TBD: Has to be adapted in ENV
			authority: 'https://GfkDataPlatform.b2clogin.com/GfkDataPlatform.onmicrosoft.com/B2C_1A_SUSI_INTERNAL',
			redirectUri: 'http://localhost:4200/redirect-web',
			scopes: [`openid`, 'offline_access', 'https://GfkDataPlatform.onmicrosoft.com/urm/Users.Me'],
		},
		clientCredentialsFlow: {
			clientId: 'a4bb765c-e9f7-476e-889c-02c40de2e001', // TBD: Has to be adapted in ENV
			clientSecret: '****************************************', // TBD: Has to be adapted in ENV
			authority: 'https://GfkDataPlatform.b2clogin.com/GfkDataPlatform.onmicrosoft.com/B2C_1A_SUSI_INTERNAL',
			apps: [
				{
					domain: 'loadmonitor-api.prod.dwh.in.gfk.com',
					scopes: ['https://GfkDataPlatform.onmicrosoft.com/dwh.lmx/.default'],
				},
				{
					domain: 'jee-api.prod.dwh.in.gfk.com',
					scopes: ['https://GfkDataPlatform.onmicrosoft.com/dwh.jee/.default'],
				},
        {
					domain: 'https://country-api-shared.prod.dwh.in.gfk.com',
					scopes: ['https://GfkDataPlatform.onmicrosoft.com/dwh.lmx/.default'],
				},
        {
					domain: 'date-api-shared.prod.dwh.in.gfk.com',
					scopes: ['https://GfkDataPlatform.onmicrosoft.com/dwh.shared.date/.default'],
				},
			]
		},
	},
	urm: {
		baseUri: '',
		userEndpoint: '/api/v1/users/me',
		roleEndpoint: '/api/v1/roles/me',
		roleFilter: ['*DWH*'],
		disabled: true
	},
	exception: {
		showDetails: true,
	},
	health: {
		name: 'LMX-BFF',
		version,
	},
	tracing: {
		namespace: 'local',
		otelCollectorUrl: 'https://frontend.collector.tracing.vpc1448.in.gfk.com:443/v1/traces',
		serviceName: 'LMX.BFF',
		version,
		customResourceAttributes: {
			developer: 'LMX',
		},
	},
	metric: { disabled:true,route:"metrics",app:"BFF Example APP",prefix:"bff_app_"}
};

export default config;
