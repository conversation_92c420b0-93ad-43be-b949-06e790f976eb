import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { HttpClient } from '@angular/common/http';
import { ConfigService } from './config.service';
import { UnitType } from '../interfaces/UnitType';
import { shareReplay } from 'rxjs/operators';

@Injectable({
  providedIn: 'root',
})
export class UnitTypeService {

  private url!:string;

  private cachedUnitType! : Observable<UnitType[]>;
  constructor(private http: HttpClient, private config: ConfigService) {
    this.url = `${this.config.getApiUrl()}/unitTypes`;
  }

  getAsync(): Observable<UnitType[]> {
    if(!this.cachedUnitType)
    {
      this.cachedUnitType = this.http
        .get<UnitType[]>(this.url)
        .pipe(shareReplay(1));
    }
    return this.cachedUnitType;
  }
}
