import { AfterViewInit, ChangeDetector<PERSON>ef, Component, HostListener, On<PERSON><PERSON>roy, OnInit, inject } from '@angular/core';
import { AuthFacade } from '@dwh/lmx-lib/src/lib/services/auth';
import { Observable, Subject, map } from 'rxjs';

@Component({
  selector: 'lm-navigation',
  templateUrl: './navigation.component.html',
  styleUrls: ['./navigation.component.scss']
})
export class NavigationComponent implements OnInit, OnDestroy, AfterViewInit {

	private readonly _destroying$ = new Subject<void>
  ();
	private readonly authFacade = inject(AuthFacade);

	TestApp = 'Load Monitor';
	pageName!: string;
	currentTab = window.location.href.split('/')[3];
	userName$: Observable<string> = this.authFacade.user$.pipe(map((user: any) => (user ?  `${user.email.split('@')[0].split('.')[1]}, ${user.email.split('.')[0]}` : '')));
	IsSubNavBarOpened = true;
	isIframe = false;

	@HostListener('window:resize', ['$event'])
	onResize() {
		this.getMarginTop(0);
	}

  	tabActive(activetab: string, pagesName: string, toggle: boolean) {
		if (this.currentTab == activetab && toggle) {
			this.currentTab = '';
			this.IsSubNavBarOpened = false;
		} else {
			this.currentTab = activetab;
			this.IsSubNavBarOpened = true;
		}

		this.pageName = pagesName;
	}

	getMarginTop(number) {
		if (this.IsSubNavBarOpened) return number + 25 + 'px';
		else return number + 9 - 56 + 'px';
	}

	constructor(private cdr: ChangeDetectorRef) {}

	ngOnInit() {
		if (
				this.currentTab === 'publish-republish' ||
				this.currentTab === 'migration-rule-runs' ||
				this.currentTab === 'data-order' ||
				this.currentTab === 'ld-general'
			) {
				this.pageName = 'Monitor DWH Process';
			} else if (this.currentTab === 'bcr-reworks' || this.currentTab === 'feature-move') {
				this.pageName = 'DWH Global Process';
			} else if (this.currentTab === 'dwh-qc' || this.currentTab === 'dwhrelease' || this.currentTab === 'logging-qc') {
				this.pageName = 'QC Monitoring';
			} else if (this.currentTab === 'shop-master-data' || this.currentTab === 'item-master-data') {
				this.pageName = 'Monitor Master Data';
			} else if (this.currentTab === 'coverage-imports' || this.currentTab === 'csv-imports' || this.currentTab === 'ranged-weighting-import') {
				this.pageName = 'Monitor Data Import';
			} else {
				this.pageName = 'Monitor Data Export';
			}
			this.isIframe = window !== window.parent && !window.opener;

			this.tabActive(this.currentTab, this.pageName, false);
	}

	ngAfterViewInit() {
		this.cdr.detectChanges();
	}

  	ngOnDestroy(): void {
		this._destroying$.next(undefined);
		this._destroying$.complete();
	}

	openJiraFeedback(e: Event): void {
		e.preventDefault();
		const jiraFeedbackButton = document.querySelector('#atlwdg-trigger');
		if (jiraFeedbackButton) {
			const event = new Event('click');
			jiraFeedbackButton.dispatchEvent(event);
		}
	}

	signOut(): void {
		this.authFacade.signOut().subscribe();
	}

	signIn(): void {
		this.authFacade.signIn();
	}
}
