import { LoggingQC } from '../interfaces/LoggingQC';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { HttpClient } from '@angular/common/http';
import { ConfigService } from './config.service';
import { Jobs } from '@loadmonitor/shared/interfaces/Jobs';
@Injectable({
	providedIn: 'root',
})
export class LoggingQCService {
	private url!:string;
	constructor(private http: HttpClient, private config: ConfigService) {
		this.url = `${this.config.getApiUrl()}/QCLoggings`;
	}
    // TODO: test and maybe rework
	getAsync(
		jobIds?: number[],
		exportIds?: number[],
        userNames?: string[],
		statusIds?: number[],
		startDate?: Date,
    	endDate?: any
	): Observable<Jobs<LoggingQC>> {
		const body = {
			jobIds,
			exportIds,
            userNames,
			statusIds,
			startDate,
    		endDate
		};
		return this.http.post<Jobs<LoggingQC>>(this.url, body);
	}
}
