import { inject, NgZ<PERSON> } from '@angular/core';
import { filter, fromEvent, map, Subject } from 'rxjs';
import { runInZone } from '../functions/run-in-zone';
import { MessageEvent, TabMessage } from '../interfaces';

export abstract class TabCommunicationService<T> {
	protected CHANNEL_NAME = '';
	protected MESSAGE_TYPE = '';
	protected readonly message$ = new Subject<TabMessage<T>>();

	private channel!: BroadcastChannel;
	private readonly EVENT_NAME = 'message';
	private readonly ngZone = inject(NgZone);

	protected initialize() {
		if (!this.CHANNEL_NAME || !this.MESSAGE_TYPE) {
			throw new Error('Broadcastchannel not initialized correctly.');
		}
		this.channel = new BroadcastChannel(this.CHANNEL_NAME);
		this.setUpEventListener();
	}

	protected postMessage(payload: T) {
		const message: TabMessage<T> = {
			payload,
			type: this.MESSAGE_TYPE,
		};
		this.channel.postMessage(message);
	}

	private isMessageEvent(object: any): object is MessageEvent<T> {
		try {
			return object['data'] && 'type' in object.data && 'payload' in object.data;
		} catch {
			return false;
		}
	}

	private setUpEventListener() {
		fromEvent(this.channel, this.EVENT_NAME)
			.pipe(
				runInZone(this.ngZone),
				filter((event: Event) => this.isMessageEvent(event)),
				map((event: unknown) => (event as MessageEvent<T>)?.data)
			)
			.subscribe((message: TabMessage<T>) => this.message$.next(message));
	}
}
