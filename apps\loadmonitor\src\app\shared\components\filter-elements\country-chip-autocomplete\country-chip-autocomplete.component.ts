import {
	Component,
	ElementRef,
	ViewChild,
	OnInit,
	Input,
	AfterViewInit,
	ChangeDetectorRef,
	Output, EventEmitter, SimpleChanges, OnChanges,
  } from '@angular/core';
  import { FormControl } from '@angular/forms';
  import { Country } from '@loadmonitor/shared/interfaces/Country';
  import { CountryService } from '@loadmonitor/shared/services/country.service';
  import { Observable } from 'rxjs/internal/Observable';
  import { debounceTime, map, startWith } from 'rxjs/operators';
  import { get, keyBy, merge, values } from 'lodash';
  import { COMMA, ENTER } from '@angular/cdk/keycodes';
  import { MatAutocompleteSelectedEvent } from '@angular/material/autocomplete';
  
  
  const EMPTY = null;
  
  @Component({
	selector: 'lm-country-chip-autocomplete',
	templateUrl: './country-chip-autocomplete.component.html',
	styleUrls: ['./country-chip-autocomplete.component.scss'],
  })
  
  export class CountryChipAutocompleteComponent implements OnInit, AfterViewInit, OnChanges {
  
  
	@Input() countryIds: number[] = [];
	@Output() valueChanges = new EventEmitter<Country[]>();
  
	@ViewChild('countryIdsInput')
	countryIdsInput!: ElementRef<HTMLInputElement>;
  
  
	// Mat-Chips - Country
	countryIdsCtrl = new FormControl(EMPTY);
	filteredCountryIds!: Observable<Country[]>;
	selectedCountryIds!: Observable<Country[]>;
	allCountryIds: Country[] = [];
  
	//Tag-Chip-List
	readonly separatorKeysCodes = [ENTER, COMMA] as const;
  
	constructor(private countryService: CountryService,
	  private cdRef: ChangeDetectorRef) { }
  
	ngAfterViewInit(): void {
	  this.refreshSuggestionsAndEmptyInput();
	  this.cdRef.detectChanges();
	}
  
	ngOnInit(): void {
	  this.getCountry();
	  this.filteredCountryIds = this.countryIdsCtrl.valueChanges.pipe(
		startWith(EMPTY),
		debounceTime(300),
		map((country) => this.setCountryFilter(get(country, 'name', country)))
	  );
  
	  //Todo this should be a BehaviorSubject or something, not pipe() on unrelated observable
	  this.selectedCountryIds = this.countryIdsCtrl.valueChanges.pipe(
		map(() =>
		  this.filterSelectedCountries(),
		),
	  );
	  this.selectedCountryIds.subscribe((countryIds) => {
		this.valueChanges.emit(countryIds);
	  });
	}
  
	getCountry(): void {
	  this.countryService.getAsync().subscribe((result) => {
		const merged = merge(keyBy(this.allCountryIds, 'id'), keyBy(result, 'id'));
		this.allCountryIds = values(merged);
		this.allCountryIds.sort((firstService: Country, otherService: Country) =>
		  firstService.name.localeCompare(otherService.name));
		this.setSelectedCountryIds(this.countryIds);
		this.refreshSuggestionsAndEmptyInput();
	  });
	}
  
	// Country Chips
	removeCountryChips(item: Country): void {
	  this.toggleSelection(item);
	}
  
	// Remove Country Filter
	public removeAllCounty(): void {
	  this.filterSelectedCountries().map((country) => country.selected = false);
	  this.refreshSuggestionsAndEmptyInput();
	}
  
	optionClicked(event: Event, item: Country): void {
	  event.stopPropagation();
	  this.toggleSelection(item);
	}
  
	selectedCountryChips(event: MatAutocompleteSelectedEvent): void {
	  this.toggleSelection(event.option.value);
	}
  
	toggleSelection(item: Country): void {
	  item.selected = !item.selected;
	  this.refreshSuggestionsAndEmptyInput();
	}
  
	refreshSuggestionsAndEmptyInput() {
	  this.countryIdsCtrl.reset();
	  if (this.countryIdsInput != undefined)
		this.countryIdsInput.nativeElement.value = '';
	}
  
	public getSelectedCountryIds() {
	  return this.filterSelectedCountries().map((country) => country.id);
	}
  
	public getSelectedCountrNames() {
	  return this.filterSelectedCountries().map((country) => country.name);
	}
  
  
	filterSelectedCountries(): Country[] {
	  return this.allCountryIds.filter(
		(country) => country.selected,
	  );
	}
  
	private setCountryFilter(value: string | null): Country[] {
	  if (value === '' || value === null) {
		return this.allCountryIds;
	  } else {
		const filterValue = value.toLowerCase();
		return this.allCountryIds.filter(
		  (country) => country.name.toLowerCase().indexOf(filterValue) === 0,
		);
	  }
	}
  
	public setSelectedCountryIds(countryIds: number[]): void {
	  countryIds?.forEach((countryId: number) => {
		const found = this.allCountryIds.find((country: Country) => country.id === countryId);
		if (found) {
		  found.selected = true;
		} else {
		  this.allCountryIds.push({ id: countryId, isoName: 'Loading...', name: 'Loading...', selected: true })
		}
	  });
	  this.refreshSuggestionsAndEmptyInput();
	}
	ngOnChanges(changes: SimpleChanges){
	  this.allCountryIds.forEach(function(i){ i.selected=false })
	  this.setSelectedCountryIds(changes.countryIds.currentValue);
	}
  }
  