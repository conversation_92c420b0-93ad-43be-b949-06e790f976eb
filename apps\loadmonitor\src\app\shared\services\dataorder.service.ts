import { DataOrder } from '../interfaces/DataOrder';
import { Injectable } from '@angular/core';
import { Observable, Subject } from 'rxjs';
import { HttpClient } from '@angular/common/http';
import { ConfigService } from './config.service';
import { Jobs } from '@loadmonitor/shared/interfaces/Jobs';
import { PropertyPage } from '../interfaces/PropertyPage';

@Injectable({
  providedIn: 'root',
})
export class DataOrderService {
  private url!:string;
  public floatingPanelData$ = new Subject<PropertyPage[]>();

  constructor(private http: HttpClient, private config: ConfigService) {
    this.url = `${this.config.getApiUrl()}/DataOrders`;
  }

  getAsync(
    dataOrderIds?: number[],
    countryIds?: number[],
    periodIds?: number[],
    periodicityIds?: number[],
    productionProjectIds?: number[],
    productionProject?: string,
    productionProjectTypeIds?: number[],
    statusIds?: number[],
    userNames?: string[],
    startDate?: Date,
    endDate?: any
  ): Observable<Jobs<DataOrder>> {
    const body = {
      dataOrderIds,
      countryIds,
      periodIds,
      periodicityIds,
      productionProjectIds,
      productionProject,
      productionProjectTypeIds,
      userNames,
      statusIds,
      startDate,
      endDate,
    };
    return this.http.post<Jobs<DataOrder>>(this.url, body);
  }

  getDetailAsync(
    dataOrderId?: number
  ): Observable<Jobs<PropertyPage>> {
    return this.http.get<Jobs<PropertyPage>>(this.url + '/' + dataOrderId);
  }
}

