import { Exports } from '../interfaces/Export';
import { ExportService } from './export.service';
import { HttpClientTestingModule, HttpTestingController, TestRequest } from '@angular/common/http/testing';
import { HttpClient } from '@angular/common/http';
import { TestBed, waitForAsync } from '@angular/core/testing';
import { ConfigService } from './config.service';
import { of } from 'rxjs';

describe('ExportService', () => {
  const mockApiUrl = 'api/exports';
  const mockExports: Exports[] = [
    {
      exportId: 1,
      exportName: 'test',
      reportProjectName: 'test project',
      reportProjectId: 2,
      toolFormat: 'CSV',
      status: 'EXPORTED',
      message: 'test test test',
      expectedExportCount: 6,
      allocatedExportCount: 5,
      period: 'test period',
      jobId: 45454,
      favoriteId: 5855,
      favoriteName: 'test favorite',
      scope: 'test scope',
      profileId: 1254,
      profileName: 'xyz',
      createdBy: 'testuser',
      created: new Date(),
      finished: new Date(),
      started: new Date(),
      estimatedRuntime: '1h',
      runtime: null,
      warningLevel: 1
    }
  ];

  const mockRequestBody = {
    countryIds: [15,17],
    startDate: new Date()
  };

  let service: ExportService;
  let configService: ConfigService;
  let httpTestingController: HttpTestingController;
  let httpClient: HttpClient;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [ConfigService]
    });

    service = TestBed.inject(ExportService);
    configService = TestBed.inject(ConfigService);

    httpTestingController = TestBed.inject(HttpTestingController);
    httpClient = TestBed.inject(HttpClient);
  });

  afterEach(() => {
    httpTestingController.verify();
  });

  it('should create an instance', () => {
    expect(service).toBeTruthy();
  });

  describe('getAsync() [Method]', () => {
    it('should exist', () => {
      jest.spyOn(service, 'getAsync');

      expect(service.getAsync).toBeTruthy();
    });

    it('should be called with POST method', waitForAsync(() => {
      jest.spyOn(httpClient, 'post');

      httpClient.post(mockApiUrl, mockRequestBody).subscribe();

      const testRequest: TestRequest = httpTestingController.expectOne(mockApiUrl);

      expect(testRequest.request.method).toEqual('POST');

      testRequest.flush(null);
    }));

    it('should return all exports', waitForAsync(() => {
      // how does this work???
      jest.spyOn(httpClient, 'post').mockReturnValue(of(mockExports));

      httpClient.post(mockApiUrl, mockRequestBody).subscribe((exports) => {
        expect(exports).toEqual(mockExports);
      });
    }));

  });
});
