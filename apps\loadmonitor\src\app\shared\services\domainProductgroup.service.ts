import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { HttpClient } from '@angular/common/http';
import { ConfigService } from './config.service';
import { DomainProductGroups } from '@loadmonitor/shared/interfaces/DomainProductGroups';

@Injectable({
  providedIn: 'root',
})
export class DomainProductGroupService {
  private url!:string;

  constructor(private http: HttpClient, private config: ConfigService) {
    this.url = `${this.config.getApiUrl()}/DomainProductGroups`;
  }

  getAsync(sectorIds?: number[], categoryIds? :number[]): Observable<DomainProductGroups[]> {
    const body={sectorIds, categoryIds};
    return this.http.post<DomainProductGroups[]>(this.url, body);
  }
}
