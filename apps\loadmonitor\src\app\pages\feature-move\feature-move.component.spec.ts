import { ComponentFixture, TestBed } from '@angular/core/testing';
import { FeatureMoveComponent } from './feature-move.component';
import { MatSnackBar } from '@angular/material/snack-bar';
import { FeatureMoveService } from '@loadmonitor/shared/services/feature-move.service';
import { FormBuilder } from '@angular/forms';
import { of } from 'rxjs';
import { MatTableDataSource } from '@angular/material/table';
import { FilterTrayComponent } from '@dwh/lmx-lib/src';
import { ToggleButtonComponent } from '@gfk/ng-lib';
import { HelperService } from '@loadmonitor/shared/services/helper.service';
import { FilterService } from '@dwh/lmx-lib/src';
import { DatePipe } from '@angular/common';
import { FilterTrayCacheService } from '@loadmonitor/shared/services/filter-tray-cache.service';

describe('FeatureMoveComponent', () => {
  let component: FeatureMoveComponent;
  let fixture: ComponentFixture<FeatureMoveComponent>;
  let featureMoveService: jest.Mocked<FeatureMoveService>;
  let snackBar: jest.Mocked<MatSnackBar>;
  let helperService: jest.Mocked<HelperService>;
  let filterService: jest.Mocked<FilterService>;
  let filterTrayCacheService: jest.Mocked<FilterTrayCacheService>;

  beforeEach(async () => {
    featureMoveService = { getAsync: jest.fn(), getDetailAsync: jest.fn() } as any;
    snackBar = { openFromComponent: jest.fn() } as any;
    helperService = { calculateDuration: jest.fn() } as any;
    filterService = { filtersSelected$: of({}) } as any;
    filterTrayCacheService = { getCacheValueData: jest.fn(), getCacheNameData: jest.fn() } as any;

    await TestBed.configureTestingModule({
      declarations: [FeatureMoveComponent, FilterTrayComponent, ToggleButtonComponent],
      providers: [
        { provide: FeatureMoveService, useValue: featureMoveService },
        { provide: MatSnackBar, useValue: snackBar },
        { provide: FormBuilder, useValue: new FormBuilder() },
        { provide: HelperService, useValue: helperService },
        { provide: FilterService, useValue: filterService },
        { provide: DatePipe, useValue: new DatePipe('en-US') },
        { provide: FilterTrayCacheService, useValue: filterTrayCacheService },
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(FeatureMoveComponent);
    component = fixture.componentInstance;
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  // Add more tests for other methods and functionalities as necessary.
});
