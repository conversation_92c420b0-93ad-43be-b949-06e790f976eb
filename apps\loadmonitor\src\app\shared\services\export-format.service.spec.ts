import { ExportFormatService } from './export-format.service';
import { ConfigService } from './config.service';
import { HttpClientTestingModule, HttpTestingController, TestRequest } from '@angular/common/http/testing';
import { HttpClient } from '@angular/common/http';
import { TestBed, waitForAsync } from '@angular/core/testing';
import { of } from 'rxjs';
import { ExportFormat } from '../interfaces/ExportFormat';

describe('ExportFormatService', () => {
  const mockApiUrl = 'api/ExportFormats';
  const mockExportFormats: ExportFormat[] = [
    {
      id: 6,
      description: 'QUICKVIEW_CSV',
      type: 1,
      selected: false
    },
    {
      id: 9,
      description: 'EXCEL',
      type: 1,
      selected: false
    },
    {
      id: 11,
      description: 'DWH2MDM',
      type: 1,
      selected: false
    }
  ];

  let service: ExportFormatService;
  let configService: ConfigService;
  let httpTestingController: HttpTestingController;
  let httpClient: HttpClient;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [ConfigService]
    });

    service = TestBed.inject(ExportFormatService);
    configService = TestBed.inject(ConfigService);

    httpTestingController = TestBed.inject(HttpTestingController);
    httpClient = TestBed.inject(HttpClient);
  });

  afterEach(() => {
    httpTestingController.verify();
  });

  it('should create an instance', () => {
    expect(service).toBeTruthy();
  });


  describe('getAsync() [Method]', () => {
    it('should exist', () => {
      jest.spyOn(service, 'getAsync');

      expect(service.getAsync).toBeTruthy();
    });

    it('should be called with GET method', waitForAsync(() => {
      jest.spyOn(httpClient, 'get');

      httpClient.get(mockApiUrl).subscribe();

      const testRequest: TestRequest = httpTestingController.expectOne(mockApiUrl);

      expect(testRequest.request.method).toEqual('GET');

      testRequest.flush(null);
    }));

    it('should return all export formats', waitForAsync(() => {
      jest.spyOn(httpClient, 'get').mockReturnValue(of(mockExportFormats));

      httpClient.get(mockApiUrl).subscribe((exportFormats) => {
        expect(exportFormats).toEqual(mockExportFormats);
      });
    }));
  });

});
