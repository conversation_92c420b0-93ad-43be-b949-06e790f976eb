<lm-navigation></lm-navigation>

<div class="each-slice-top-headbar" [ngClass]="FilterTrayOpenFlag ? 'filter-tray-open' : 'filter-tray-close'">
	<h3 class="alignleft">Item Master Data</h3>
	<div class="flexdiv-buttons">
		<button testId="lmx-button"
			gfk-button
			type="primary"
			class="jira-button-margin-right btn-secondary gfk-btn"
			(click)="openJiraTicket()"
			[disabled]="!checkAtleastOneRowChecked()"
		>
			Create JIRA Ticket
		</button>
		<lmx-filter-button showFilterCount="{{ filterCount }}" (isShownFilterTray)="isShownFilterTrayEventHandler($event)"></lmx-filter-button>
	</div>
</div>

<lmx-filter-tray
	(ButtonShowHideEvent)="this.getWidth()"
	(clickApplyButton)="ConfirmApply(true)"
	(clickSaveButton)="openmodal()"
	[IsEnabled]="IsEnabled"
	[IsDisabled]="IsDisabled"
>
	<lm-filter-tray-form
		[sliceName]="'ItemMasterData'"
		[resetForm]="resetForm"
		(selectedFilters)="getSelectedFilters($event)"
	>
	</lm-filter-tray-form>
</lmx-filter-tray>

<div class="filter-area" [ngClass]="FilterTrayOpenFlag ? 'filter-tray-open' : 'filter-tray-close'">
	<div class="toggle-button">
		<div>
			<lm-quick-filter
				[selectedfilter]="defaultoption"
				class="p-3.5"
				[events]="eventsSubject.asObservable()"
				*ngIf="IsEnabled"
				[modalshow]="modalshow"
				(saveInQuickFilter)="saveInQuickFilter(cacheObj.isAutoRefresh, $event)"
				(applyquickfilter)="applyfilter($event)"
				(disabledbutton)="disablesave($event)"
			>
			</lm-quick-filter>
			<lm-filter-summary (resetFilters)="filterSummaryChipClear($event)"></lm-filter-summary>
		</div>
		<div class="refreshedTime">
			<lm-refreshed-date-time *ngIf="refreshedDateTime" class="pr-3 d-inline-block"></lm-refreshed-date-time>
			<gfk-toggle-button
				*ngIf="EnableRefresh && IsEnabled"
				[checked]="cacheObj.isAutoRefresh || false"
				title="Auto Refresh"
				class="autoRefresh d-inline-block"
				[disabled]="defaultoption === 'Default'"
				(onChange)="toggleChecked($event)"
			>
			</gfk-toggle-button>
		</div>
	</div>
</div>
<article class="table-grid">
	<div class="mat-elevation-z8 tab-container" [ngClass]="(itemMasterData?.length) ? 'table-height' : ''">
		<table mat-table [dataSource]="dataSource" matSort class="full-width-table js-tbl-bcr-reworks-result" multiTemplateDataRows>
			<ng-container matColumnDef="details">
				<th mat-header-cell *matHeaderCellDef></th>
				<td mat-cell *matCellDef="let element">
					<mat-icon (click)="element.isExpanded = element.isExpanded; element.isExpanded = !element.isExpanded" *ngIf="element.isExpanded"
						>keyboard_arrow_down</mat-icon
					>
					<mat-icon (click)="element.isExpanded = !element.isExpanded; element.isExpanded = element.isExpanded" *ngIf="!element.isExpanded"
						>chevron_right</mat-icon
					>
				</td>
			</ng-container>

			<ng-container matColumnDef="menu">
				<th mat-header-cell *matHeaderCellDef class="selectAllPad">
					<mat-checkbox (change)="selectRows($event)" [checked]="this.IsSelectAll" [indeterminate]="this.IsSelectAll"></mat-checkbox>
				</th>
				<td mat-cell *matCellDef="let element">
					<div class="flexdiv">
						<mat-checkbox class="matcheckbox" [checked]="element.isChecked" (change)="rowToggleSelection($event, element)"></mat-checkbox>
						<lm-detail-menu [configuration]="element.detailMenuConfig"></lm-detail-menu>
					</div>
				</td>
			</ng-container>

			<ng-container matColumnDef="prepUnitId">
				<th mat-header-cell *matHeaderCellDef mat-sort-header>Prep Unit ID</th>
				<td mat-cell *matCellDef="let element" class="js-result_prep-unit-id">{{ element.prepUnitId }}</td>
			</ng-container>

			<ng-container matColumnDef="imlJobType">
				<th mat-header-cell *matHeaderCellDef mat-sort-header>Job Type</th>
				<td mat-cell *matCellDef="let element" class="js-result-iml-job-type">{{ element.imlJobType }}</td>
			</ng-container>

			<ng-container matColumnDef="loadUnitId">
				<th mat-header-cell *matHeaderCellDef mat-sort-header>Load Unit ID</th>
				<td mat-cell *matCellDef="let element" class="js-result-load-unit-id">{{ element.loadUnitId }}</td>
			</ng-container>

			<ng-container matColumnDef="status">
				<th mat-header-cell *matHeaderCellDef mat-sort-header>Status</th>
				<td mat-cell *matCellDef="let element" class="js-result-status">{{ element.status }}</td>
			</ng-container>

			<ng-container matColumnDef="productGroupId">
				<th mat-header-cell *matHeaderCellDef mat-sort-header>Productgroup ID</th>
				<td mat-cell *matCellDef="let element" class="js-result-productgroup-id">{{ element.productGroupId }}</td>
			</ng-container>

			<ng-container matColumnDef="productGroup">
				<th mat-header-cell *matHeaderCellDef mat-sort-header>Productgroup</th>
				<td mat-cell *matCellDef="let element" class="js-result-productgroup">{{ element.productGroup }}</td>
			</ng-container>

			<ng-container matColumnDef="mode">
				<th mat-header-cell *matHeaderCellDef mat-sort-header>Mode</th>
				<td mat-cell *matCellDef="let element" class="js-result-mode">{{ element.mode }}</td>
			</ng-container>

			<ng-container matColumnDef="jobId">
				<th mat-header-cell *matHeaderCellDef mat-sort-header>JEE Job Id</th>
				<td mat-cell *matCellDef="let element" class="js-result-jee-job-id">{{ element.jobId }}</td>
			</ng-container>

			<ng-container matColumnDef="jeeJobInfo">
				<th mat-header-cell *matHeaderCellDef>JEE Job Info</th>
				<td mat-cell *matCellDef="let element">
					<lm-jee-job-info jobId="{{ element.jobId }}"></lm-jee-job-info>
				</td>
			</ng-container>

			<ng-container matColumnDef="jobPriority">
				<th mat-header-cell *matHeaderCellDef mat-sort-header>JEE Job Priority</th>
				<td mat-cell *matCellDef="let element" class="js-result-jee-job-priority">{{ element.jobPriority }}</td>
			</ng-container>

			<ng-container matColumnDef="jobLoops">
				<th mat-header-cell *matHeaderCellDef mat-sort-header>JEE Job Restarts</th>
				<td mat-cell *matCellDef="let element" class="js-result-job-loops">
					{{ element.jobLoops }}
				</td>
			</ng-container>

			<ng-container matColumnDef="createdBy">
				<th mat-header-cell *matHeaderCellDef mat-sort-header>Created By</th>
				<td mat-cell *matCellDef="let element" class="js-result-created-by">{{ element.createdBy }}</td>
			</ng-container>

			<ng-container matColumnDef="created">
				<th mat-header-cell *matHeaderCellDef mat-sort-header>Created On</th>
				<td mat-cell *matCellDef="let element" class="js-result-created-on">
					{{ element.created | date: date_with_time }}
				</td>
			</ng-container>

			<ng-container matColumnDef="started">
				<th mat-header-cell *matHeaderCellDef mat-sort-header>Started On</th>
				<td mat-cell *matCellDef="let element" class="js-result-started-on">
					{{ element.started | date: date_with_time }}
				</td>
			</ng-container>

			<ng-container matColumnDef="finished">
				<th mat-header-cell *matHeaderCellDef mat-sort-header>Finished On</th>
				<td mat-cell *matCellDef="let element" class="js-result-finished-on">
					{{ element.finished | date: date_with_time }}
				</td>
			</ng-container>

			<ng-container matColumnDef="runtime">
				<th mat-header-cell *matHeaderCellDef mat-sort-header>Runtime</th>
				<td mat-cell *matCellDef="let element" class="js-result-runtime">{{ element.runtime }}</td>
			</ng-container>

			<ng-container matColumnDef="message">
				<th mat-header-cell *matHeaderCellDef mat-sort-header>Message</th>
				<td mat-cell *matCellDef="let element" class="js-result-message">{{ element.message }}</td>
			</ng-container>

			<tr mat-header-row *matHeaderRowDef="displayedColumns; sticky: true" class="columnHeader"></tr>
			<tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
		</table>
	</div>
	<mat-paginator [pageSizeOptions]="[10, 20, 50, 100]" showFirstLastButtons class="js-result-paginator"></mat-paginator>
</article>
<lm-version></lm-version>