@use '@gfk/style' as gfk-style;

.width-15px {
  width: 15px;
}

/* The Modal (background) */
.ldmessagemodal {
  display: block; /* Hidden by default */
  width: 100%;
    height: 100%;
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 9000;
    display: flex;
    justify-content: center;
    align-items: center;
}
.ldmessagemodalClose {
  display: none; /* Hidden by default */
}
/* Modal Content/Box */
.modal-content {
  background-color: #fefefe;
  margin: 15% auto; /* 15% from the top and centered */
  padding: 5px;
  border-radius: 4px;
  border: 1px solid #888;
  width: 65%; /* Could be more or less, depending on screen size */
}
.modal-header {
  font: 400 1.75rem / 1.5 Lato, sans-serif;
  letter-spacing: normal;
  margin: 0 0 16px;
}
/* The Close Button */
.close {
  color: gfk-style.$brand;
  float: right;
  font-size: 28px;
  font-weight: bold;
}

.close:hover,
.close:focus {
  color: gfk-style.$brand;
  text-decoration: none;
  cursor: pointer;
}
.dwh-table{
  height: 500px;
  overflow-y: scroll;
}
table {
  border-collapse: collapse;
  border-spacing: 0;
  width: 100%;
}

th, td {
  text-align: left;
  padding: 8px;
}

tr:nth-child(even){background-color: #f2f2f2}
