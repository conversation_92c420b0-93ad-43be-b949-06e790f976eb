import { DetailMenuConfiguration } from "./DetailMenuConfiguration";

export interface PublishRepublish {
    loadId: number;
    rbProjectId: number;
    status: string;
    message?: string;
    operation: string;
    serverName?: string;
    specification?: string;
    rbProject: string;
    projectType: string;
    country: string;
    fromPeriod: string;
    toPeriod: string;
    jobId: number;
    createdBy: string;
    created: Date;
    started: Date;
    finished: Date;
    detailMenuConfig?: DetailMenuConfiguration[];
    isChecked?: boolean;
    isSelected?: boolean;
    isExpanded?: boolean;
  }
