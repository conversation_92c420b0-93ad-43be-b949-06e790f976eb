import { Compo<PERSON>, ElementRef, <PERSON>Child, <PERSON><PERSON>nit, On<PERSON>estroy, Input, OnChanges, SimpleChanges } from '@angular/core';
import { FormControl } from '@angular/forms';
import { Category } from '@loadmonitor/shared/interfaces/Category';
import { CategoryService } from '@loadmonitor/shared/services/category.service';
import { Observable } from 'rxjs';
import { debounceTime, map, startWith } from 'rxjs/operators';
import { get } from 'lodash';
import { COMMA, ENTER } from '@angular/cdk/keycodes';
import { HelperService } from '@loadmonitor/shared/services/helper.service';
import { Subscription } from 'rxjs';
import { MatAutocompleteSelectedEvent } from '@angular/material/autocomplete';

const EMPTY = null;

@Component({
  selector: 'lm-category-chip-autocomplete',
  templateUrl: './category-chip-autocomplete.component.html',
  styleUrls: ['./category-chip-autocomplete.component.scss']
})
export class CategoryChipAutocompleteComponent implements <PERSON><PERSON><PERSON>t, <PERSON><PERSON><PERSON>roy,OnChanges {

  @ViewChild('categoryIdsInput')
  categoryIdsInput!: ElementRef<HTMLInputElement>;

  @Input() cachedCategories: number[] = [];

  categoryIds!: Observable<Category[]>;
  sectorIds: number[];

  readonly separatorKeysCodes = [ENTER, COMMA] as const;

  // Mat-Chips - Category
  categoryIdsCtrl = new FormControl(EMPTY);
  filteredCategoryIds!: Observable<Category[]>;
  allCategoryIds: Category[] = [];

  categoryIdsPerSector: Category[] = [];

  private subscription!: Subscription | undefined;


  constructor(
    private categoryService: CategoryService,
    private util: HelperService
  ) {
    this.sectorIds = [];
    this.fillCategories();
    this.filteredCategoryIds = this.categoryIdsCtrl.valueChanges.pipe(
      startWith(EMPTY),
      debounceTime(300),
      map((category) => this.setCategoryFilter(get(category, 'name', category)))
    );

    this.categoryIds = this.categoryIdsCtrl.valueChanges.pipe(
      startWith(EMPTY),
      map(() =>
        this.filterSelectedCategory(),
      ),
    );
  }

  ngOnInit() {

    this.subscription = this.util.getSelectedSectors().subscribe(result => {

      this.sectorIds = result.sectorIds;

      this.fillCategoriesPerSector(this.sectorIds);

    });

  }

  ngOnDestroy() {
    this.subscription?.unsubscribe();
  }

  public getSelectedCategoryIds() {
    return this.filterSelectedCategory().map((Sector) => Sector.id);
  }
  filterSelectedCategory(): Category[] {
    return this.allCategoryIds.filter(
      (Category) => Category.selected,
    );
  }

  removeCategory(): void {
    this.filterSelectedCategory().map((x) => x.selected = false);
    this.refreshSuggestionsAndEmptyInput();
  }


  fillCategories(): void {
    this.categoryService.getAsync([]).subscribe((result) => {
      const allCategoryIds = result.map((r) => {
        const tag: Category = { id: r.id, name: r.name, sectorId: r.sectorId, selected: false };
        return tag;
      });

      this.allCategoryIds = allCategoryIds.sort((firstCategory: Category, otherCategory: Category) => firstCategory.name.localeCompare(otherCategory.name));

      this.fillCategoriesPerSector(this.sectorIds);
      this.setSelectedCategoryIds(this.cachedCategories);
    });

  }

  fillCategoriesPerSector(sectorIds: number[]): void {
    this.categoryIdsPerSector = sectorIds.length != 0 ? this.allCategoryIds.filter(i => sectorIds.some(j => j == i.sectorId)) : this.allCategoryIds
    this.refreshSuggestionsAndEmptyInput();
  }

  public setSelectedCategoryIds(CategoryIds: number[]): void {

    CategoryIds?.forEach((Categoryid: number) => {
      const found = this.allCategoryIds.find((Category: Category) => Category.id === Categoryid);
      if (found) {
        found.selected = true;
      }

    });
    this.util.sendSelectedCategories(this.allCategoryIds.filter(i => i.selected).map((i) => i.id));
    this.refreshSuggestionsAndEmptyInput();
  }

  // Category Chips

  removeCategoryChips(item: Category): void {
    this.toggleSelection(item);}

  optionClicked(event: Event, item: Category) {
    event.stopPropagation();
    this.toggleSelection(item);
  }

  toggleSelection(item: Category) {
    item.selected = !item.selected;
    this.util.sendSelectedCategories(this.allCategoryIds.filter(i => i.selected).map((i) => i.id));
    this.refreshSuggestionsAndEmptyInput();
  }

  refreshSuggestionsAndEmptyInput() {
    this.categoryIdsCtrl.reset();
    if(this.categoryIdsInput)
      this.categoryIdsInput.nativeElement.value = '';
  }

  private setCategoryFilter(value: string): Category[] {
    if (value === '' || value === null) {
      return this.categoryIdsPerSector;
    }
    else {
      const filterValue = value.toLowerCase();
      return this.categoryIdsPerSector.filter(
        (Category) => Category.name.toLowerCase().indexOf(filterValue) === 0,
      );
    }
  }

  selectedCategoryChips(event: MatAutocompleteSelectedEvent): void {
    this.toggleSelection(event.option.value);
    this.categoryIdsCtrl.setValue(null);
    this.util.sendSelectedCategories(this.allCategoryIds.filter(i => i.selected).map((i) => i.id));
  }
  ngOnChanges(changes: SimpleChanges){
    this.allCategoryIds.forEach(function(i){ i.selected=false })
    this.setSelectedCategoryIds(changes.cachedCategories.currentValue);
  }
}
