import { ComponentFixture, TestBed } from '@angular/core/testing';
import { DataOrderComponent } from './data-order.component';
import { MatTable } from '@angular/material/table';
import { MatPaginator } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import { MatSnackBar } from '@angular/material/snack-bar';
import { FormBuilder } from '@angular/forms';
import { of } from 'rxjs';
import { DataOrderService } from '@loadmonitor/shared/services/dataorder.service';
import { HelperService } from '@loadmonitor/shared/services/helper.service';
import { DatePipe } from '@angular/common';
import { MatDialog, MatDialogModule } from '@angular/material/dialog';

describe('DataOrderComponent', () => {
    let component: DataOrderComponent;
    let fixture: ComponentFixture<DataOrderComponent>;
    let dataOrderServiceMock: any;
    let datePipe: jest.Mocked<DatePipe>;

    beforeEach(async () => {
      datePipe = {
        transform: jest.fn(),
      } as any;
      dataOrderServiceMock = {
        getAsync: jest.fn().mockReturnValue(of({ records: [], moreRecordsAvailable: false, count: 0 })),
        getDetailAsync: jest.fn().mockReturnValue(of({ records: [] })),
      };

    await TestBed.configureTestingModule({
          declarations: [DataOrderComponent, MatTable, MatPaginator, MatSort],
          imports: [
            MatDialogModule
          ],
          providers: [
            { provide: DataOrderService, useValue: dataOrderServiceMock },
            { provide: MatSnackBar, useValue: { openFromComponent: jest.fn() } },
            FormBuilder,
            HelperService,
            { provide: DatePipe, useValue: datePipe },
            MatDialog
          ]
      }).compileComponents();

      fixture = TestBed.createComponent(DataOrderComponent);
      component = fixture.componentInstance;
    });

    afterEach(() => {
        jest.clearAllMocks();
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });
    
});
