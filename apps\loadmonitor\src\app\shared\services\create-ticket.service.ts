import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { ParentIssue } from '../interfaces/ParentIssue';
import { Ticket } from '../interfaces/Ticket';
import { ConfigService } from './config.service';

@Injectable({
  providedIn: 'root'
})
export class CreateTicketService {

  constructor(private http: HttpClient, private config: ConfigService) {}
  getJiraTicketAsync(
    issue: ParentIssue
  ): Observable<Ticket> {
    const body = {
      ...issue
    };
  return this.http.post<Ticket>(`${this.config.getApiUrl()}/Ticket/`+issue.title, body);
 
  }
}

