<lm-navigation></lm-navigation>
<div class="each-slice-top-headbar" [ngClass]="FilterTrayOpenFlag ? 'filter-tray-open' : 'filter-tray-close'">
	<h3 class="alignleft">Shop Master Data</h3>
	<div class="flexdiv-buttons">
		<lmx-filter-button showFilterCount="{{ filterCount }}" (isShownFilterTray)="isShownFilterTrayEventHandler($event)"></lmx-filter-button>
	</div>
</div>

<lmx-filter-tray
	(ButtonShowHideEvent)="this.getWidth()"
	(clickApplyButton)="ConfirmApply(cacheObj.isAutoRefresh)"
	(clickSaveButton)="openmodal()"
	[IsEnabled]="IsEnabled"
	[IsDisabled]="IsDisabled"
>
	<lm-filter-tray-form
		[sliceName]="'ShopMasterData'"
		[resetForm]="resetForm"
		(selectedFilters)="getSelectedFilters($event)"
		[isPeriodHidden]="isPeriodFilterHidden"
	>
	</lm-filter-tray-form>
</lmx-filter-tray>

<div class="filter-area" [ngClass]="FilterTrayOpenFlag ? 'filter-tray-open' : 'filter-tray-close'">
	<div class="toggle-button">
		<div>
			<lm-quick-filter
				[selectedfilter]="defaultoption"
				class="p-3.5"
				[events]="eventsSubject.asObservable()"
				*ngIf="IsEnabled"
				[modalshow]="modalshow"
				(saveInQuickFilter)="saveInQuickFilter(cacheObj.isAutoRefresh, $event)"
				(applyquickfilter)="applyfilter($event)"
				(disabledbutton)="disablesave($event)"
			>
			</lm-quick-filter>
			<lm-filter-summary (resetFilters)="filterSummaryChipClear($event)"></lm-filter-summary>
		</div>
		<div class="refreshedTime">
			<lm-refreshed-date-time *ngIf="refreshedDateTime" class="pr-3 d-inline-block"></lm-refreshed-date-time>
			<gfk-toggle-button
				*ngIf="EnableRefresh && IsEnabled"
				[checked]="cacheObj.isAutoRefresh || false"
				title="Auto Refresh"
				class="autoRefresh d-inline-block"
				[disabled]="defaultoption === 'Default'"
				(onChange)="toggleChecked($event)"
			>
			</gfk-toggle-button>
		</div>
	</div>
	<article class="table-grid">
		<div class="mat-elevation-z8 tab-container" [ngClass]="(shopMasterData?.length) ? 'table-height' : ''">
			<table mat-table [dataSource]="dataSource" matSort class="full-width-table js-tbl-shop-master-data-result" multiTemplateDataRows>
				<ng-container matColumnDef="menu">
					<th mat-header-cell *matHeaderCellDef></th>
					<td mat-cell *matCellDef="let element">
						<mat-icon
							(click)="element.isExpanded = element.isExpanded; element.isExpanded = !element.isExpanded"
							*ngIf="element.isExpanded"
							>keyboard_arrow_down</mat-icon
						>
						<mat-icon
							(click)="element.isExpanded = !element.isExpanded; element.isExpanded = element.isExpanded"
							*ngIf="!element.isExpanded"
							>chevron_right</mat-icon
						>
					</td>
					<!-- <td mat-cell *matCellDef='let element'>
        <mat-icon *ngIf="element.isSelected">keyboard_arrow_down</mat-icon>
        <mat-icon *ngIf="!element.isSelected">chevron_right</mat-icon>
      </td> -->
				</ng-container>

				<ng-container matColumnDef="unitId">
					<th mat-header-cell *matHeaderCellDef mat-sort-header>Unit ID</th>
					<td mat-cell *matCellDef="let element" class="js-result-shop-master-data-id">{{ element.unitId }}</td>
				</ng-container>

				<ng-container matColumnDef="status">
					<th mat-header-cell *matHeaderCellDef mat-sort-header>Status</th>
					<td mat-cell *matCellDef="let element" class="js-result-unit-type">{{ element.status }}</td>
				</ng-container>

				<ng-container matColumnDef="outletMasterDataName">
					<th mat-header-cell *matHeaderCellDef mat-sort-header>Name of Outlet Master Data Load</th>
					<td mat-cell *matCellDef="let element" class="js-result-shop-master-data-status">{{ element.outletMasterDataName }}</td>
				</ng-container>

				<ng-container matColumnDef="createdBy">
					<th mat-header-cell *matHeaderCellDef mat-sort-header>Created By</th>
					<td mat-cell *matCellDef="let element" class="js-result-created-by">{{ element.createdBy }}</td>
				</ng-container>

				<ng-container matColumnDef="created">
					<th mat-header-cell *matHeaderCellDef mat-sort-header>Created On</th>
					<td mat-cell *matCellDef="let element" class="js-result-created">
						{{ element.created | date: date_with_time }}
					</td>
				</ng-container>

				<ng-container matColumnDef="started">
					<th mat-header-cell *matHeaderCellDef mat-sort-header>Started On</th>
					<td mat-cell *matCellDef="let element" class="js-result-started">
						{{ element.started | date: date_with_time }}
					</td>
				</ng-container>

				<ng-container matColumnDef="finished">
					<th mat-header-cell *matHeaderCellDef mat-sort-header>Finished On</th>
					<td mat-cell *matCellDef="let element" class="js-result-finished">
						{{ element.finished | date: date_with_time }}
					</td>
				</ng-container>

				<ng-container matColumnDef="errorMessage">
					<th mat-header-cell *matHeaderCellDef mat-sort-header>Error Message</th>
					<td mat-cell *matCellDef="let element" class="js-result-errorMessage">{{ element.errorMessage }}</td>
				</ng-container>

				<ng-container matColumnDef="expandedDetail">
					<td mat-cell *matCellDef="let element" [attr.colspan]="displayedColumns.length">
						<div class="expanded-element-detail" [@detailExpand]="element.isExpanded ? 'expanded' : 'collapsed'">
							<lm-floating-panel *ngIf="element.isExpanded"></lm-floating-panel>
						</div>
					</td>
					<!-- <td mat-cell *matCellDef="let row" [attr.colspan]="displayedColumns.length">
        <div class="expanded-element-detail"
             [@detailExpand]="row.isSelected ? 'expanded' : 'collapsed'">
             <lm-floating-panel *ngIf='row.isSelected' ></lm-floating-panel>
        </div>
      </td> -->
				</ng-container>

				<tr mat-header-row *matHeaderRowDef="displayedColumns; sticky: true" class="columnHeader"></tr>

				<tr
					mat-row
					*matRowDef="let element; columns: displayedColumns"
					[class.selected-row-background]="element.isSelected"
					[class.expanded-row]="expandedElement === element"
					(click)="selectRow(element); expandedElement = expandedElement === element ? null : element"
				></tr>
				<tr mat-row *matRowDef="let row; columns: ['expandedDetail']" class="expanded-detail-row"></tr>
			</table>
		</div>
		<mat-paginator [pageSizeOptions]="[10, 20, 50, 100]" (page)="clickNextButton()" showFirstLastButtons></mat-paginator>
	</article>
</div>
<lm-version></lm-version>