@use 'node_modules/@angular/material/index' as mat;
@use 'material-gfk-light.palette' as gfk;

$config: mat.get-color-config(gfk.$theme);
$background: map-get($config, background);
$foreground: map-get($config, foreground);

//==================================================/DO NOT USE=========================================================
// Include theme styles for core and each component used in your app.
// Alternatively, you can import and @include the theme mixins for each component
// that you are using.
//@include mat.all-component-themes(gfk.$theme);
//===================================================DO NOT USE/========================================================

//=================================================/USE INSTEAD=========================================================
// Emit theme-dependent styles for common features used across multiple components.
@include mat.core-theme(gfk.$theme);

// Emit styles for MatButton based on `$gfk-theme`.
// Because the configuration passed to `define-dark-theme` omits typography, `button-theme` will not emit
// any typography styles.
@include mat.autocomplete-theme(gfk.$theme);
// @include mat.badge-theme(gfk.$theme);
@include mat.bottom-sheet-theme(gfk.$theme);
@include mat.button-theme(gfk.$theme);
@include mat.button-toggle-theme(gfk.$theme);
// @include mat.card-theme(gfk.$theme);
@include mat.checkbox-theme(gfk.$theme);
@include mat.chips-theme(gfk.$theme);
@include mat.table-theme(gfk.$theme);
@include mat.datepicker-theme(gfk.$theme);
@include mat.dialog-theme(gfk.$theme);
@include mat.divider-theme(gfk.$theme);
@include mat.expansion-theme(gfk.$theme);
@include mat.form-field-theme(gfk.$theme);
@include mat.grid-list-theme(gfk.$theme);
@include mat.icon-theme(gfk.$theme);
@include mat.input-theme(gfk.$theme);
@include mat.list-theme(gfk.$theme);
@include mat.menu-theme(gfk.$theme);
@include mat.paginator-theme(gfk.$theme);
// @include mat.progress-bar-theme(gfk.$theme);
// @include mat.progress-spinner-theme(gfk.$theme);
@include mat.radio-theme(gfk.$theme);
@include mat.select-theme(gfk.$theme);
// @include mat.sidenav-theme(gfk.$theme);
// @include mat.slide-toggle-theme(gfk.$theme);
// @include mat.slider-theme(gfk.$theme);
// @include mat.stepper-theme(gfk.$theme);
@include mat.sort-theme(gfk.$theme);
@include mat.tabs-theme(gfk.$theme);
// @include mat.toolbar-theme(gfk.$theme);
// @include mat.tooltip-theme(gfk.$theme);
// @include mat.tree-theme(gfk.$theme);
@include mat.snack-bar-theme(gfk.$theme);
//================================================USE INSTEAD/==========================================================
