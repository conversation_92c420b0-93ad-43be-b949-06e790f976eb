import { HttpClientTestingModule, HttpTestingController, TestRequest } from '@angular/common/http/testing';
import { HttpClient } from '@angular/common/http';
import { TestBed, waitForAsync } from '@angular/core/testing';
import { ConfigService } from './config.service';
import { of } from 'rxjs';
import {DataOrder} from "@loadmonitor/shared/interfaces/DataOrder";
import {DataOrderService} from "@loadmonitor/shared/services/dataorder.service";

describe('DataOrderService', () => {
  const mockApiUrl = 'api/exports';
  const mockDataOrder: DataOrder[] = [
    {
      dataOrderId: 1,
      countryIds: 1,
      periodIds: 1,
      periodicityIds: 1,
      productionProjectIds: 1,
      productionProjecttName: 'test projekt',
      productionProjectType: 1,
      statusIds: 1,
      userNames: 'test name',
      startDate: new Date(),
      endDate: new Date(),
      started: new Date(),
      finished: new Date(),
      runtime: new Date(),
      isSelected: true,
      isExpanded: true,
    }
  ];

  const mockRequestBody = {
    countryIds: [15,17],
    startDate: new Date()
  };

  let service: DataOrderService;
  let configService: ConfigService;
  let httpTestingController: HttpTestingController;
  let httpClient: HttpClient;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [ConfigService]
    });

    service = TestBed.inject(DataOrderService);
    configService = TestBed.inject(ConfigService);

    httpTestingController = TestBed.inject(HttpTestingController);
    httpClient = TestBed.inject(HttpClient);
  });

  afterEach(() => {
    httpTestingController.verify();
  });

  it('should create an instance', () => {
    expect(service).toBeTruthy();
  });

  describe('getAsync() [Method]', () => {
    it('should exist', () => {
      jest.spyOn(service, 'getAsync');

      expect(service.getAsync).toBeTruthy();
    });

    it('should be called with POST method', waitForAsync(() => {
      jest.spyOn(httpClient, 'post');

      httpClient.post(mockApiUrl, mockRequestBody).subscribe();

      const testRequest: TestRequest = httpTestingController.expectOne(mockApiUrl);

      expect(testRequest.request.method).toEqual('POST');

      testRequest.flush(null);
    }));

    it('should return DataOrder', waitForAsync(() => {
      // how does this work???
      jest.spyOn(httpClient, 'post').mockReturnValue(of(mockDataOrder));

      httpClient.post(mockApiUrl, mockRequestBody).subscribe((DataOrder) => {
        expect(mockDataOrder).toEqual(mockDataOrder);
      });
    }));

  });
});
