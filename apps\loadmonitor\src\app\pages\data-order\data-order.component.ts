import { Component, ViewChild, OnInit, AfterContentInit, Input, HostListener, OnDestroy } from '@angular/core';
import { MatSort } from '@angular/material/sort';
import { MatTable, MatTableDataSource } from '@angular/material/table';
import { MatPaginator } from '@angular/material/paginator';
import { FormBuilder, FormGroup } from '@angular/forms';
import { BehaviorSubject, Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { MatSnackBar } from '@angular/material/snack-bar';
import { DefaultSnackbarComponent } from '@loadmonitor/shared/components/default-snackbar/default-snackbar.component';
import { SettingsConstants } from '@loadmonitor/shared/settings.constants';
import { DataOrder } from '@loadmonitor/shared/interfaces/DataOrder';
import { DataOrderCache } from '@loadmonitor/shared/interfaces/caching/DataOrderCache';
import { DataOrderService } from '@loadmonitor/shared/services/dataorder.service';
import { HelperService } from '@loadmonitor/shared/services/helper.service';
import { JobTypes } from '@loadmonitor/shared/jobTypes';
import { PropertyPage } from '@loadmonitor/shared/interfaces/PropertyPage';
import { animate, state, style, transition, trigger } from '@angular/animations';
import { CreateTicketDialogComponent } from '@loadmonitor/shared/components/create-ticket-dialog/create-ticket-dialog.component';
import { MatDialog } from '@angular/material/dialog';
import { SelectionModel } from '@angular/cdk/collections';
import { Filter, FilterRecords, FilterService, FilterTrayComponent } from '@dwh/lmx-lib';
import { environment } from 'apps/loadmonitor/src/environments/environment';
import { DatePipe } from '@angular/common';
import { ToggleButtonComponent } from '@gfk/ng-lib';
import * as moment from 'moment';
import { FilterTrayCacheService } from '@loadmonitor/shared/services/filter-tray-cache.service';

@Component({
	selector: 'lm-data-order',
	templateUrl: './data-order.component.html',
	styleUrls: ['./data-order.component.scss'],
	animations: [
		trigger('detailExpand', [
			state('collapsed', style({ height: '0px', minHeight: '0' })),
			state('expanded', style({ height: '*' })),
			transition('expanded <=> collapsed', animate('225ms cubic-bezier(0.4, 0.0, 0.2, 1)')),
		]),
	],
})
export class DataOrderComponent implements OnInit, AfterContentInit, OnDestroy {
	/* *********
	 * ViewChild
	 ********* */
	eventsSubject: Subject<void> = new Subject<void>();
	@Input() icon = 'delete';
	@Input() icon_help = 'help-outline';

	@ViewChild(MatSort)
	sort: MatSort = new MatSort();

	@ViewChild(MatTable)
	table!: MatTable<DataOrder>;

	@ViewChild(MatPaginator)
	paginator!: MatPaginator;

	@ViewChild(FilterTrayComponent) filterTrayComponent: FilterTrayComponent | undefined;

	@ViewChild(ToggleButtonComponent) togglebuttonComponent: ToggleButtonComponent | undefined;

	/* *********************
	 * Components variables
	 ********************* */
	jobType: JobTypes = JobTypes.DataOrder;
	productionProject!: string;
	productionProjectTypes: Map<string, string> = new Map<string, string>();
	isPeriodFilterHidden = false;
	selectedRow: any;
	IsEnabled = false;
	IsDisabled = false;
	FilterTrayOpenFlag = true;
	environments = environment.environments;
	filterCount = '';
	modalshow = false;
	currentfilter = JSON.parse(localStorage.getItem('QuickFilter') || '[]');
	EnableRefresh = true;
	cacheInterval!: any;
	toggleInterval!: any;
	timer = 60;

	// Progress Spinner visibility
	isLoading = false;
	public dataOrderForm!: FormGroup;

	public cacheObj: DataOrderCache = {} as DataOrderCache;
	cacheName = 'cache::DataOrder';
	defaultoption = localStorage.getItem(this.cacheName + '-LastFilter') || 'Default';
	defaultfilter = JSON.parse(localStorage.getItem(this.cacheName + '-DefaultFilter') || '[]');

	selectedFilters: any;
	cacheValue: BehaviorSubject<any> = new BehaviorSubject<any>({});
	resetForm: Subject<boolean> = new Subject();
	resetPeriodicityFilter: Subject<boolean> = new Subject();
	/* *********************
	 * Components parameters
	 ********************* */

	//Mat-Table
	dataSource = new MatTableDataSource<DataOrder>();
	selection = new SelectionModel<DataOrder>(true, []);
	IsSelectAll = false;
	displayedColumns: string[] = [
		'details',
		'menu',
		'dataOrderId',
		'status',
		'productionProjectId',
		'productionProjectName',
		'projectType',
		'dataOrderType',
		'period',
		'deadline',
		'createdBy',
		'created',
		'sentBy',
		'sent',
	];

	//Mat-Expansion-Panel
	panelOpenState = false;

	readonly date_with_time = SettingsConstants.DATE_FORMAT_WITH_TIME;
	readonly only_time = SettingsConstants.FORMAT_ONLY_TIME;

	// Property Pages
	expandedElement!: PropertyPage | null;
	dwhReleaseDetail!: PropertyPage[];

	refreshedDateTime!: boolean;
	dataOrderData!: any[];

	/* **************
	 * Public Methods
	 ************** */

	@HostListener('document:visibilitychange', ['$event'])
	appVisibility() {
		if (document.hidden) {
			if (this.cacheInterval) {
				clearInterval(this.cacheInterval);
			}
			if (this.toggleInterval) {
				clearInterval(this.toggleInterval);
			}
		} else {
			if (this.cacheObj.isAutoRefresh === true) {
				this.cacheInterval = setInterval(() => {
					if (this.defaultoption != 'Default') {
						this.Apply();
					}
				}, this.timer * 1000);
			} else {
				if (this.cacheInterval !== undefined) clearInterval(this.cacheInterval);
				if (this.toggleInterval !== undefined) clearInterval(this.toggleInterval);
			}
		}
	}
	private destroy$ = new Subject<void>();

	constructor(
		private dataOrderService: DataOrderService,
		private helperService: HelperService,
		private snackBar: MatSnackBar,
		public dialogbox: MatDialog,
		private formBuilder: FormBuilder,
		public datepipe: DatePipe,
		public filterService: FilterService,
		private filterTrayCacheService: FilterTrayCacheService
	) {}

	ngOnInit(): void {
		if (this.environments.filter((x) => x.location == window.location.origin)[0]) {
			this.IsEnabled = this.environments.filter((x) => x.location == window.location.origin)[0].enabled;
		} else {
			this.IsEnabled = false;
		}
		this.dataOrderForm = this.formBuilder.group({
			isAutoRefresh: this.togglebuttonComponent?.checked,
		});
		// this.updateFilterFGValues();
		this.SetFiltersCount();
	}

	ngAfterContentInit() {
		this.afterChildComponentsLoaded();
		this.CreateFilterSummaryChips();
		this.SetFiltersCount();
		if (this.cacheObj.isAutoRefresh === true) {
			if (this.cacheInterval === undefined) {
				this.cacheInterval = setInterval(() => {
					if (this.defaultoption != 'Default') {
						this.Apply();
					}
				}, this.timer * 1000);
			}
		} else {
			if (this.cacheInterval !== undefined) clearInterval(this.cacheInterval);
			if (this.toggleInterval !== undefined) clearInterval(this.toggleInterval);
		}
	}

	afterChildComponentsLoaded() {
		this.cacheObj = JSON.parse(localStorage.getItem(this.cacheName) || '{}');
		this.updateFilterFGValues();
	}

	// eslint-disable-next-line @angular-eslint/use-lifecycle-interface
	ngOnDestroy() {
		if (this.cacheInterval) {
			clearInterval(this.cacheInterval);
		}

		if (this.toggleInterval) {
			clearInterval(this.toggleInterval);
		}
		this.destroy$.next();
	    this.destroy$.complete();
	}

	setFormValue(autoRefreshStatus) {
		this.dataOrderForm.setValue({
			isAutoRefresh: autoRefreshStatus === undefined || autoRefreshStatus === false ? false : true,
		});
	}

	saveInCache(autoRefreshStatus, AutoToggle) {
		this.setFormValue(autoRefreshStatus);
		localStorage.setItem(this.cacheName, JSON.stringify(this.selectedFilters));

		this.cacheObj = JSON.parse(localStorage.getItem(this.cacheName) || '{}');
		if(this.cacheObj.productionProjectName == null){
			this.cacheObj.productionProjectName = '';
		}
		if (!AutoToggle) {
			this.defaultfilter = [];
			this.defaultfilter.push({
				data: this.selectedFilters,
				slice: window.location.href.split('/')[3],
				user: localStorage.getItem('username'),
			});
			localStorage.setItem(this.cacheName + '-DefaultFilter', JSON.stringify(this.defaultfilter));
			let nofilter = 0;
			let selectedfilter = '';
			// eslint-disable-next-line @typescript-eslint/no-unused-vars
			this.currentfilter.forEach((i, a) => {
				// if (i.filter == this.defaultoption) {
				if(JSON.stringify(i.data) == JSON.stringify(this.cacheObj)){
					nofilter++;
					selectedfilter = i.filter;
				}
			});
			if (nofilter == 0) {
				this.defaultoption = 'Default';
			} else {
				this.defaultoption = selectedfilter;
			}
		}
		this.updateFilterFGValues();
	}

	applyfilter(selectedvalue) {
		this.resetForm.next(true);
		const oldautotoggle = this.cacheObj.isAutoRefresh;
		selectedvalue = selectedvalue.value ? selectedvalue.value : selectedvalue;
		this.defaultoption = selectedvalue;
		localStorage.setItem(this.cacheName + '-LastFilter', this.defaultoption || 'Default');
		if (selectedvalue == 'Default') {
			if (
				JSON.parse(localStorage.getItem(this.cacheName + '-DefaultFilter') || '[]').filter(
					(x) => x.user == localStorage.getItem('username') && x.slice == window.location.href.split('/')[3]
				)[0]
			) {
				this.cacheObj = JSON.parse(localStorage.getItem(this.cacheName + '-DefaultFilter') || '[]').filter(
					(x) => x.user == localStorage.getItem('username') && x.slice == window.location.href.split('/')[3]
				)[0]['data'];
			}

			localStorage.setItem(this.cacheName, JSON.stringify(this.cacheObj));
		} else {
			this.cacheObj = JSON.parse(localStorage.getItem('QuickFilter') || '[]')
				.filter((x) => x.user == localStorage.getItem('username') && x.slice == window.location.href.split('/')[3])
				.filter((x) => x.filter == selectedvalue)[0]['data'];
			localStorage.setItem(this.cacheName, JSON.stringify(this.cacheObj));
		}
		this.cacheObj.isAutoRefresh = oldautotoggle;
		this.CreateFilterSummaryChips();
		this.selectedFilters = this.cacheObj;

		if (this.cacheObj.selectedDays == '3') {
			const date = new Date();
			this.cacheObj.startDate = new Date(Date.UTC(date.getFullYear(), date.getMonth(), date.getDate() - 2, 0, 0, 0, 0));
			this.cacheObj.endDate = new Date(Date.UTC(date.getFullYear(), date.getMonth(), date.getDate(), 0, 0, 0, 0));
		} else if (this.cacheObj.selectedDays == '2') {
			const date = new Date();
			this.cacheObj.startDate = new Date(Date.UTC(date.getFullYear(), date.getMonth(), date.getDate(), 0, 0, 0, 0));
			this.cacheObj.endDate = new Date(Date.UTC(date.getFullYear(), date.getMonth(), date.getDate(), 0, 0, 0, 0));
		}
		setTimeout(() => {
			this.updateFilterFGValues();
			this.Apply();
		}, 200);
		localStorage.setItem(this.cacheName, JSON.stringify(this.cacheObj));
	}

	private updateFilterFGValues(): void {
		if(this.cacheObj){
			this.filterTrayCacheService.getCacheValueData(this.cacheObj);
		}
	}

	saveInQuickFilter(autoRefreshStatus, filterName) {
		this.setFormValue(autoRefreshStatus);
		this.currentfilter = JSON.parse(localStorage.getItem('QuickFilter') || '[]');
		this.currentfilter.push({
			data: this.selectedFilters,
			filter: filterName,
			slice: window.location.href.split('/')[3],
			user: localStorage.getItem('username'),
		});
		this.defaultoption = filterName;
		const oldcache = this.cacheObj;
		const olddefault = localStorage.getItem(this.cacheName + '-DefaultFilter');
		this.ConfirmApply(autoRefreshStatus);
		this.cacheObj = oldcache;
		localStorage.setItem(this.cacheName + '-DefaultFilter', olddefault || '[]');
		localStorage.setItem('QuickFilter', JSON.stringify(this.currentfilter));
		localStorage.setItem(this.cacheName + '-LastFilter', filterName);
		this.SetFiltersCount();
	}

	selectRow(row: DataOrder) {
		this.dwhReleaseDetail = [];
		row.isSelected = true;

		this.dataSource.data.forEach((element) => {
			if (row.dataOrderId != element.dataOrderId) {
				element.isExpanded = false;
				element.isSelected = false;
			}
		});

		this.selectedRow = row;
		this.dataOrderService.getDetailAsync(row.dataOrderId).subscribe((result) => {
			this.helperService.floatingPanelData$.next({ data: result.records, jobType: JobTypes.DataOrder });
		});
	}

	Apply(): void {
		this.refreshedDateTime = false;
		this.isLoading = true;
		this.destroy$.next();
		this.filterTrayComponent?.showSpinner(true);
		this.dataOrderService
			.getAsync(
				this.selectedFilters.dataOrderIds,
				this.selectedFilters.countries?.map((country) => country),
				this.selectedFilters.periodIds?.map((period) => period),
				this.selectedFilters.periodicityIds ? [this.selectedFilters.periodicityIds] : [],
				this.selectedFilters.productionProjectIds,
				this.selectedFilters.productionProjectName,
				this.selectedFilters.productionProjectType?.map((ppt) => ppt),
				this.selectedFilters.status?.map((status) => status),
				this.selectedFilters.users?.map((users) => users.userName),
				this.selectedFilters.startDate ? this.selectedFilters.startDate : null,
				this.selectedFilters.endDate ? moment(this.selectedFilters.endDate).format().split('T')[0] + 'T23:59:59.999Z' : null
			)
			.pipe(
				takeUntil(this.destroy$)
			)
			.subscribe((result) => {
				this.refreshedDateTime = true;
				if (result.moreRecordsAvailable) {
					this.notify('More than 1000 results exist!');
				}

				if (result.count === 0) {
					this.notify('No result found!');
					this.dataOrderData = [];
				}
				this.isLoading = false;
				this.filterTrayComponent?.showSpinner(false);
				this.dataOrderData = result.records;
				// set runtime column
				result.records.forEach((element) => {
					if (element.started != null && element.finished != null) {
						element.runtime = new Date();
						const startedDate = new Date(element.started);
						const finishedDate = new Date(element.finished);
						element.runtime.setHours(finishedDate.getHours() - startedDate.getHours());
						element.runtime.setMinutes(finishedDate.getMinutes() - startedDate.getMinutes());
						element.runtime.setSeconds(finishedDate.getSeconds() - startedDate.getSeconds());
					}
				});

				this.dataSource = new MatTableDataSource<DataOrder>(result.records);
				this.dataSource.sort = this.sort;
				this.paginator.pageIndex = 0;
				this.dataSource.paginator = this.paginator;
				this.table.dataSource = this.dataSource;
			},
			(error) => {
				if (error.status === 504) {
					this.notify('Gateway Timeout. The server took too long to respond. Please try again later.');
				} 
				else {
					this.notify(`An error occurred: ${error.message}`);
				}
				this.isLoading = false;
				this.filterTrayComponent?.showSpinner(false);
			});
		this.SetFiltersCount();
	}

	private notify(message: string) {
		this.snackBar.openFromComponent(DefaultSnackbarComponent, {
			duration: 10 * 1000,
			data: message,
		});
	}

	clickNextButton() {
		this.selectedRow.isSelected = false;
	}
	checkAtleastOneRowChecked() {
		return this.dataSource.data.filter((i) => i.isChecked).length > 0;
	}
	rowToggleSelection(ev, item) {
		item.isChecked = ev.checked;
	}
	openJiraTicket(): void {
		const checkedList = this.dataSource.data.filter((i) => i.isChecked);
		const dialogRef = this.dialogbox.open(CreateTicketDialogComponent, {
			width: '1000px',
			height: '487px',
			data: {
				ids: checkedList.map((i) => {
					return i.dataOrderId;
				}),
				jobType: this.jobType,
				title: 'DataOrder',
			},
		});

		dialogRef.afterClosed().subscribe((resSubmit) => {
			if (resSubmit === true) {
				this.dataSource.data.forEach((i) => (i.isChecked = false));
				this.selection.clear();
				this.IsSelectAll = false;
			}
		});
	}

	selectRows(selectrow) {
		this.IsSelectAll = !this.IsSelectAll;
		let endIndex: number;
		const page = this.dataSource.paginator !== null ? this.dataSource.paginator.pageSize : 0;
		const pageindex = this.dataSource.paginator !== null ? this.dataSource.paginator.pageIndex : 0;

		if (!selectrow.checked) {
			this.selection.clear();
			if (this.dataSource.data.length > (pageindex + 1) * page) {
				endIndex = (pageindex + 1) * page;
			} else {
				endIndex = this.dataSource.data.length;
			}

			for (let index = pageindex * page; index < endIndex; index++) {
				const row = this.dataSource.data[index];
				this.selection.deselect(row);
				this.dataSource.data[index].isChecked = false;
			}
		} else {
			if (this.dataSource.data.length > (pageindex + 1) * page) {
				endIndex = (pageindex + 1) * page;
			} else {
				endIndex = this.dataSource.data.length;
			}
			for (let index = pageindex * page; index < endIndex; index++) {
				const row = this.dataSource.sortData(this.dataSource.data, this.sort)[index];
				this.selection.select(row);
				this.dataSource.sortData(this.dataSource.data, this.sort)[index].isChecked = true;
			}
		}
	}

	isSelectedPage() {
		const numSelected = this.selection.selected.length;
		const page = this.dataSource.paginator !== null ? this.dataSource.paginator.pageSize : 0;
		const pageindex = this.dataSource.paginator !== null ? this.dataSource.paginator.pageIndex : 0;
		let endIndex: number;
		if (this.dataSource.data.length > (pageindex + 1) * page) {
			endIndex = (pageindex + 1) * page;
		} else {
			endIndex = this.dataSource.data.length - pageindex * page;
		}

		return numSelected === endIndex;
	}

	public isShownFilterTrayEventHandler($event: boolean) {
		this.filterTrayComponent?.toggleShowTray($event);
		this.FilterTrayOpenFlag = $event;
	}

	getWidth() {
		this.FilterTrayOpenFlag = !this.FilterTrayOpenFlag;
		return '100%';
	}

	openmodal() {
		this.eventsSubject.next();
	}
	disablesave(status) {
		this.IsDisabled = status;
	}

	filterSummaryChipClear(filteredItems) {
		if (filteredItems == 'ALL') {
			if (confirm('This will remove all of your selected filters and could not be reversed. Are you sure you want to do that?')) {
				this.resetForm.next(true);
				this.filterTrayCacheService.getCacheNameData(this.cacheName);
				this.resetPeriodicityFilter.next(true);
				this.filterCount = '';
			}
		}
		this.saveInCache(this.cacheObj.isAutoRefresh, false);
		this.CreateFilterSummaryChips();
		this.SetFiltersCount();
	}

	SetFiltersCount() {
		let count;
		this.filterService.filtersSelected$.subscribe((selectedFilters: FilterRecords) => {
			count = Number(Object.keys(selectedFilters).length);
		});
		if (count > 0) {
			this.filterCount = '(' + count.toString() + ')';
		} else {
			this.filterCount = '';
		}
	}

	toggleChecked(check: any) {
		if (check === true) {
			this.saveInCache(check, true);
			this.toggleInterval = setInterval(() => {
				if (this.defaultoption != 'Default') {
					this.Apply();
				}
			}, this.timer * 1000);
		} else {
			if (this.toggleInterval !== undefined) clearInterval(this.toggleInterval);
			if (this.cacheInterval !== undefined) clearInterval(this.cacheInterval);
			this.saveInCache(check, true);
		}
	}

	ConfirmApply(autoRefreshStatus) {
		this.saveInCache(autoRefreshStatus, false);
		this.CreateFilterSummaryChips();
		this.getSelectedFilters(this.cacheObj);
		let count;
		this.filterService.filtersSelected$.subscribe((selectedFilters: FilterRecords) => {
			count = Number(Object.keys(selectedFilters).length);
		});

		if (count > 0) {
			this.Apply();
		} else {
			if (confirm('Loading results without any filters selected will take some time to load. Are you sure you want to proceed?')) {
				this.Apply();
			}
		}
	}

	datesFilterSummary(dateStart: any, dateEnd: any) {
		if (dateStart != null || dateEnd != null) {
			const start = dateStart == 'Invalid Date' || dateStart == null ? '' : this.datepipe.transform(dateStart, 'yyyy-MM-dd');
			const end = dateEnd == 'Invalid Date' || dateEnd == null ? '' : this.datepipe.transform(dateEnd, 'yyyy-MM-dd');
			this.filterService.setFilters({
				dates: {
					name: 'Date',
					values: (start !== undefined && end !== undefined) ? [start, end] : null,
					slice: window.location.href.split('/')[3],
				} as Filter,
			});
		} else {
			this.filterService.setFilters({
				dates: {
					name: 'Date',
					values: null,
					slice: window.location.href.split('/')[3],
				} as unknown as Filter,
			});
		}
	}

	countryFilterSummary() {
		this.filterService.setFilters({
			countryIds: {
				name: 'Country',
				values: this.cacheObj.countries,
				slice: window.location.href.split('/')[3],
			} as Filter,
		});
	}

	periodicityFilterSummary() {
		this.filterService.setFilters({
			periodicityIds: {
				name: 'Periodicity',
				values: this.cacheObj.periodicityIds? [{ value: this.cacheObj.periodicityIds, label: this.cacheObj.periodicityIds }] : null,
				slice: window.location.href.split('/')[3],
			} as Filter,
		});
	}

	periodFilterSummary() {
		this.filterService.setFilters({
			periodIds: {
				name: 'Period',
				values: this.cacheObj.periodIds,
				slice: window.location.href.split('/')[3],
			} as Filter,
		});
	}

	usersFilterSummary() {
		this.filterService.setFilters({
			userIds: {
				name: 'User',
				values: this.cacheObj.users?.map((i) => i.name),

				slice: window.location.href.split('/')[3],
			} as Filter,
		});
	}

	statusFilterSummary() {
		this.filterService.setFilters({
			statusIds: {
				name: 'Status',
				values: this.cacheObj.status,
				slice: window.location.href.split('/')[3],
			} as Filter,
		});
	}

	prodProjectNameFilterSummary() {
		const _prodProjectName = [] as any[];
		if (this.cacheObj.productionProjectName) {
			_prodProjectName.push(this.cacheObj.productionProjectName);
		}
		this.filterService.setFilters({
			prodProjectName: {
				name: 'Production Project Name',
				values: _prodProjectName,
				slice: window.location.href.split('/')[3],
			} as Filter,
		});
	}

	productionprojectIdsFilterSummary() {
		this.filterService.setFilters({
			productionprojectIds: {
				name: 'Production Project IDs',
				values: this.cacheObj.productionProjectIds,
				slice: window.location.href.split('/')[3],
			} as Filter,
		});
	}

	dataOrderIdsFilterSummary() {
		this.filterService.setFilters({
			dataOrderIds: {
				name: 'Data Order IDs',
				values: this.cacheObj.dataOrderIds,
				slice: window.location.href.split('/')[3],
			} as Filter,
		});
	}

	productionProjectTypeFilterSummary() {
		this.filterService.setFilters({
			productionProjectTypeIds: {
				name: 'Production Project Type',
				values: this.cacheObj.productionProjectType,
				slice: window.location.href.split('/')[3],
			} as Filter,
		});
	}

	CreateFilterSummaryChips() {
		if (this.cacheObj && Object.keys(this.cacheObj).length) {
			this.countryFilterSummary();
			this.periodicityFilterSummary();
			this.periodFilterSummary();
			this.usersFilterSummary();
			this.statusFilterSummary();
			this.dataOrderIdsFilterSummary();
			this.productionprojectIdsFilterSummary();
			this.prodProjectNameFilterSummary();
			this.productionProjectTypeFilterSummary();
			this.datesFilterSummary(this.cacheObj.startDate, this.cacheObj.endDate);
		}
	}

	getSelectedFilters(selectedFilters: any) {
		this.cacheObj = selectedFilters;
		this.selectedFilters = selectedFilters;
	}
}
