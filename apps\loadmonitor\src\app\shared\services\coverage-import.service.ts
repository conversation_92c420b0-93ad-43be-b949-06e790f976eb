import { CoverageImport } from '../interfaces/CoverageImport';
import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { HttpClient } from '@angular/common/http';
import { ConfigService } from './config.service';
import { Jobs } from '@loadmonitor/shared/interfaces/Jobs';

@Injectable({
	providedIn: 'root',
})

export class CoverageImportService {
	private url!:string;

	constructor(private http: HttpClient, private config: ConfigService) {
		this.url = `${this.config.getApiUrl()}/coverageimports`;
	}

	getAsync(
		jobIds?: number[],
		exportIds?: number[],
		workspaceIds?: number[],
		userNames?: string[],
		statusIds?: number[],
		startDate?: Date,
    	endDate?: any
	): Observable<Jobs<CoverageImport>> {
		const body = {
			jobIds,
			exportIds,
			workspaceIds,
			userNames,
			statusIds,
			startDate,
      		endDate
		};

		return this.http.post<Jobs<CoverageImport>>(this.url, body);
	}

}
