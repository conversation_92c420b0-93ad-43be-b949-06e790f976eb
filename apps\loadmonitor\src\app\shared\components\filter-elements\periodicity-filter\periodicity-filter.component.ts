import { Period } from '@loadmonitor/shared/interfaces/Period';
import { ENTER, COMMA } from '@angular/cdk/keycodes';
import {
  Component,
  ElementRef,
  Input,
  ViewChild,
  OnInit,
  AfterViewInit,
  ChangeDetectorRef,
  Output,
  EventEmitter,
  SimpleChanges,
  OnChanges,
} from '@angular/core';
import { FormControl } from '@angular/forms';
import { Periodicity } from '@loadmonitor/shared/interfaces/Periodicity';
import { PeriodicityService } from '@loadmonitor/shared/services/periodicity.service';
import { Observable, Subject, of } from 'rxjs';
import { debounceTime, map, startWith, switchMap } from 'rxjs/operators';
import { PeriodService } from '@loadmonitor/shared/services/period.service';
import { keyBy, merge, values } from 'lodash';
import { PeriodCache } from '@loadmonitor/shared/interfaces/caching/PeriodCache';

@Component({
  selector: 'lm-periodicity-filter',
  templateUrl: './periodicity-filter.component.html',
  styleUrls: ['./periodicity-filter.component.scss'],
})
export class PeriodicityFilterComponent implements OnInit, AfterViewInit, OnChanges {
  @Input() isPeriodFilterHidden = false;
  @Input() periodicityIds!: Periodicity;
  @Input() periodIds!: PeriodCache[];
  @Output() valueChangesPeriodicity = new EventEmitter<any>();
  @Output() valueChangesPeriod = new EventEmitter<any>();
  @Input() resetPeriodicity!: Subject<boolean>;
  @ViewChild('periodicityInput')
  private periodicityInput!: ElementRef<HTMLInputElement>;

  @ViewChild('periodInput')
  private periodInput!: ElementRef<HTMLInputElement>;

  readonly separatorKeysCodes = [ENTER, COMMA] as const;
  public selectedPeriodicities!: Observable<any>;

  filteredPeriodicities!: Observable<Periodicity[]>;
  selectedPeriodicity: Periodicity | undefined;
  periodicityFormCtrl: FormControl = new FormControl('');

  periodicities: Periodicity[] = [];
  fillPeriodicity: Periodicity[] = [];

  public selectedPeriodsObs!: Observable<any>;
  periodFormCtrl: FormControl = new FormControl('');
  filteredPeriods!: Observable<Period[]>;
  selectedPeriods: Period[] = [];

  constructor(private periodicityService: PeriodicityService, private periodService: PeriodService, private cdRef: ChangeDetectorRef) { }

  getSelectedPeriods() {
    return this.selectedPeriods.map((i) => {
      return <PeriodCache>{ id: i.id, name: i.name };
    });
  }

  ngAfterViewInit(): void {
    this.refreshPeriodicitySuggestionsAndEmptyInput();
    this.cdRef.detectChanges();
  }

  onChange = () => {
    // This is intentionally left empty
  };
  onTouched = () => {
    // This is intentionally left empty
  };

  ngOnInit() {
    this.fillPeriodicities();

    this.periodicityFormCtrl.valueChanges.subscribe((y) => {
      if (y) {
        this.fillPeriodicity = this.periodicities.filter((x) => {
          return x.name.toLowerCase().includes(y.id ? y.name.toLocaleLowerCase() : y.toLowerCase());
        });
        this.filteredPeriodicities = of(this.fillPeriodicity);
      } else this.filteredPeriodicities = of(this.periodicities);

      this.periodFormCtrl.setValue('');
      this.PeriodFilter();

    });

    this.filteredPeriods = this.periodFormCtrl.valueChanges.pipe(
      startWith(''),
      debounceTime(500),
      switchMap((value) => {
        const paramValue = (value === null) ? undefined : value;
        return this.getPeriodsAfterAdjustment(paramValue);
      })
    );

    //Todo this should be a BehaviorSubject or something, not pipe() on unrelated observable
    this.selectedPeriodicities = this.periodicityFormCtrl.valueChanges.pipe(
      map(() =>
        this.filterSelectedPeriodicities()
      ),
    );


    this.selectedPeriodsObs = this.periodFormCtrl.valueChanges.pipe(
      map(() => {
        this.filterSelectedPeriods();
      })
    );

    this.selectedPeriodicities.subscribe((periodicityIds) => {
      const periodicityObject = { periodicityIds: periodicityIds, type: "periodicity" };
      this.valueChangesPeriodicity.emit(periodicityObject);
    });


    this.resetPeriodicity.subscribe((value) => {
      if (value) {
        this.removeAllPeriodicity();
        this.removeAllPeriods();
      }
    });
  }


  ngOnChanges(changes: SimpleChanges) {
    this.setSelectedPeriodicities(changes.periodicityIds.currentValue);
    this.selectedPeriods.forEach(function (i) { i.selected = false });
    this.selectedPeriods = [];
    changes.periodIds?.currentValue.forEach((i) => {
      this.selectedPeriods.push({ id: i.id, name: i.name, selected: true });
    });
  }

  public PeriodFilter() {
    this.filteredPeriods = this.periodFormCtrl.valueChanges.pipe(
      startWith(''),
      debounceTime(500),
      switchMap((value) => {
        const paramValue = (value === null) ? undefined : value;
        return this.getPeriodsAfterAdjustment(paramValue);
      })
    );
  }

  public removeAllPeriodicity(): void {
    this.selectedPeriodicity = { id: 2, name: 'Weekly', selected: true };
    this.setSelectedPeriodicities(this.selectedPeriodicity);
    this.refreshPeriodicitySuggestionsAndEmptyInput();
  }

  removeAllPeriods(): void {
    this.filterSelectedPeriods().map((period) => (period.selected = false));
    this.selectedPeriods.splice(0, this.selectedPeriods.length);
    this.refreshPeriodSuggestionsAndEmptyInput();
  }

  filterSelectedPeriodicities(): Periodicity[] {
    return this.periodicities.filter((x) => x.id === this.selectedPeriodicity?.id);
  }

  filterSelectedPeriods(): Period[] {
    return this.selectedPeriods;
  }

  filterPeriodicities(): any {
    return this.periodicities.find((x) => x.id === this.selectedPeriodicity?.id);
  }

  public setSelectedPeriodicities(periodicityIds: any): void {
    let found;
    if (periodicityIds)
      found = this.periodicities.find((PPT: Periodicity) => PPT.id === periodicityIds.id);
    else
      found = this.periodicities.find((PPT: Periodicity) => PPT.id === periodicityIds[0].id);

    if (found) {
      found.selected = true;
      this.selectedPeriodicity = found;
      this.periodicityFormCtrl.setValue(this.selectedPeriodicity);
    }
    this.refreshPeriodicitySuggestionsAndEmptyInput();
  }

  getPeriodsAfterAdjustment(value: string | undefined) {
    return this.periodService.getAsync(this.selectedPeriodicity?.id, value).pipe(
      map((response) => {
        this.selectedPeriods.filter(i => i.selected).forEach((s) => {
          response.forEach((r) => {
            if (r.id == s.id) { r.selected = s.selected }
          })
        })
        return response
      }
      )
    );
  }

  removeChipsItem(item: any, filterName: string): void {
    if (filterName == 'Periodicity') {
      this.togglePeriodicitySelection(item);
    } else {
      this.removePeriodSelection(item);
    }
  }

  optionClicked(event: Event, item: any, filterName: string) {
    event.stopPropagation();
    if (filterName == 'Periodicity') {
      this.togglePeriodicitySelection(item);
    } else {
      this.togglePeriodSelection(item);
    }
  }

  removePeriodSelection(item: any) {
    if (item) {
      const index = this.selectedPeriods.findIndex((value) => value.id === item.id);
      this.selectedPeriods.splice(index, 1);
      this.valueChangesPeriod.emit(this.selectedPeriods);
      this.refreshPeriodSuggestionsAndEmptyInput();
    }
  }

  togglePeriodSelection(item: any) {
    item.selected = !item.selected;
    if (item.selected) {
      this.selectedPeriods.push(item);
    } else {
      const index = this.selectedPeriods.findIndex(value => value.id === item.id);
      this.selectedPeriods.splice(index, 1);
    }
    this.refreshPeriodSuggestionsAndEmptyInput();
    this.valueChangesPeriod.emit(this.selectedPeriods);
  }

  togglePeriodicitySelection(item: Periodicity) {
    item.selected = !item.selected;
    this.selectedPeriodicity = { id: 2, name: 'Weekly', selected: true };
    this.setSelectedPeriodicities(this.selectedPeriodicity);
    this.refreshPeriodicitySuggestionsAndEmptyInput();
    this.periodFormCtrl.setValue(null);
  }

  refreshPeriodSuggestionsAndEmptyInput() {
    this.periodFormCtrl.reset();
    if (this.periodInput != undefined) this.periodInput.nativeElement.value = '';
  }

  refreshPeriodicitySuggestionsAndEmptyInput() {
    this.periodicityFormCtrl.reset();
  }

  private fillPeriodicities(): void {
    this.periodicityService.getAsync().subscribe((result) => {
      const merged = merge(keyBy(this.periodicities, 'id'), keyBy(result, 'id'));
      this.periodicities = values(merged);
      const dailyIndex = this.periodicities.findIndex((x) => x.id === 1)
      this.periodicities.splice(dailyIndex, 1);
      this.filteredPeriodicities = of(this.periodicities);

      if (this.periodicityIds === undefined || !this.periodicityIds)
        this.setSelectedPeriodicities({ id: 2, name: 'Weekly', selected: true });
      else
        this.setSelectedPeriodicities(this.periodicityIds);

      this.refreshPeriodicitySuggestionsAndEmptyInput();
    });
  }

  stringIsValid(myString: string): boolean {
    return typeof myString === 'string';
  }

  private _filterPeriodicity(value: string): Periodicity[] {
    const filterValue = value.toLowerCase();
    return this.periodicities.filter((option) => option.name.toLowerCase().indexOf(filterValue) === 0);
  }

  optionSelected(option) {
    this.selectedPeriodicity = option.value;
    this.refreshPeriodicitySuggestionsAndEmptyInput();
  }

  displayFn(displayPeriodicity: Periodicity): string {
    return displayPeriodicity && displayPeriodicity.name ? displayPeriodicity.name : '';
  }

  displayPeriodFn(displayPeriod: Period): string {
    return displayPeriod && displayPeriod.name ? displayPeriod.name : '';
  }
}
