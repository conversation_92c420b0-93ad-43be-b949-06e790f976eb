@use 'sass:map' as map;

$palette: (
  'text-heading': #323842,
  'text': #414752,
  'text-secondary': #5C626E,
  'text-lighter': #6F7582,
  'text-inactive': #9499A3,
  'bg-darkest': #6F7582,
  'bg-dark': #DADDE3,
  'bg-medium': #EBECF0,
  'bg-light': #F5F6F7,
  'bg': #FFFFFF,
  'stroke': #BBC0C9,
  'stroke-light': #DADDE3,
  'orange-dark': #CF4800,
  'orange': #E55A00,
  'orange-light': #FDEFE6,
  'orange-light-super': #FDF6F2,
);

$blue-palette: (
  100 : #DBE0EB,
  200 : #AFBBDB,
  300 : #879BCC,
  400 : #6480C3,
  500 : #4368BD,
  600 : #3C60B3,
  700 : #3456A6,
  800 : #2B4C9B,
  contrast: (
    100: #000000,
    200: #000000,
    300: #000000,
    400: #000000,
    500: #ffffff,
    600: #ffffff,
    700: #ffffff,
    800: #ffffff,
    900: #ffffff,
  )
);
$petrol-palette: (
  100 : #E2FFFF,
  200 : #B2EDEB,
  300 : #81D7D3,
  400 : #63C8C2,
  500 : #54B9B1,
  600 : #4DA9A1,
  700 : #4B9A91,
  800 : #428A81,
  contrast: (
    100: #000000,
    200: #000000,
    300: #000000,
    400: #000000,
    500: #000000,
    600: #000000,
    700: #000000,
    800: #000000,
  ),
);
$yellow-palette: (
  100 : #FFEBCC,
  200 : #FED79A,
  300 : #FDC166,
  400 : #FDB244,
  500 : #FFA52F,
  600 : #FA9A2A,
  700 : #F58A27,
  800 : #EE7C24,
  contrast: (
    100: #000000,
    200: #000000,
    300: #000000,
    400: #000000,
    500: #000000,
    600: #000000,
    700: #000000,
    800: #000000,
  )
);
$magenta-palette: (
  100 : #DDBBC5,
  200 : #DA91A7,
  300 : #D76789,
  400 : #D44A73,
  500 : #D2335F,
  600 : #C02D58,
  700 : #AC2B55,
  800 : #97254F,
  contrast: (
    100: #000000,
    200: #000000,
    300: #000000,
    400: #000000,
    500 : #FFFFFF,
    600 : #FFFFFF,
    700 : #FFFFFF,
    800 : #FFFFFF,
  )
);
$purple-palette: (
  100 : #DDD8E4,
  200 : #BFB0D4,
  300 : #A387C8,
  400 : #8D69BB,
  500 : #7A4CB2,
  600 : #7249AD,
  700 : #673FA2,
  800 : #5C399B,
  contrast: (
    100: #000000,
    200: #000000,
    300: #000000,
    400: #000000,
    500 : #FFFFFF,
    600 : #FFFFFF,
    700 : #FFFFFF,
    800 : #FFFFFF,
  )
);
$grey-palette: (
  100 : #D6D9DC,
  200 : #C0C5C8,
  300 : #9FA7AD,
  400 : #7F888E,
  500 : #6A757C,
  600 : #535D63,
  700 : #3B4145,
  800 : #171A1C,
  contrast: (
    100: #000000,
    200: #000000,
    300: #000000,
    400: #000000,
    500 : #FFFFFF,
    600 : #FFFFFF,
    700 : #FFFFFF,
    800 : #FFFFFF,
  )
);
$red-palette: (
  100 : #CBA6A8,
  200 : #BB7471,
  300 : #B0504C,
  400 : #BA302A,
  500 : #BC1C0E,
  600 : #AD0E0E,
  700 : #9B0009,
  800 : #8F0001,
  contrast: (
    100: #000000,
    200: #000000,
    300 : #FFFFFF,
    400 : #FFFFFF,
    500 : #FFFFFF,
    600 : #FFFFFF,
    700 : #FFFFFF,
    800 : #FFFFFF,
  )
);
$green-palette: (
  100 : #BDE5BD,
  200 : #98D299,
  300 : #73C375,
  400 : #59B75B,
  500 : #3DAD42,
  600 : #329D38,
  700 : #24892C,
  800: #167922,
  contrast: (
    100: #000000,
    200: #000000,
    300: #000000,
    400: #000000,
    500: #000000,
    600: #000000,
    700: #000000,
    800 : #FFFFFF,
  )
);
$orange-palette: (
  100: #f7ceb3,
  200: #f2ad80,
  300: #ed8c4d,
  400: #e97326,
  500: map.get($palette, 'orange'),
  600: #e25200,
  700: #de4800,
  800: #da3f00,
  contrast: (
    100: #000000,
    200: #000000,
    300: #000000,
    400: #000000,
    500: white,
    600: white,
    700: white,
    800: white,
  ),
);
