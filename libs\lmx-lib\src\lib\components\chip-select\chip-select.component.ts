/* eslint-disable @typescript-eslint/no-empty-function */
import { Component, Input, OnInit, forwardRef } from '@angular/core';
import { ControlValueAccessor, FormControl, NG_VALUE_ACCESSOR, ReactiveFormsModule } from '@angular/forms';
import { MatSelectModule } from '@angular/material/select';
import { CommonModule } from '@angular/common';
const materialComponents = [MatSelectModule,CommonModule];
export interface SelectOption {
	label: string;
	value: any;
}

@Component({
	selector: 'lmx-chip-select',
	templateUrl: './chip-select.component.html',
	styleUrls: ['./chip-select.component.scss'],
	providers: [
		{
			provide: NG_VALUE_ACCESSOR,
			useExisting: forwardRef(() => ChipSelectComponent),
			multi: true,
		},
	],
	standalone: true,
	imports: [ ReactiveFormsModule,...materialComponents]
})
export class ChipSelectComponent implements OnInit, ControlValueAccessor {
	@Input() label!: string;
	@Input() options: SelectOption[] | null = [];
	inputFC = new FormControl();
	selectedValue = 'Please Select an Option';
	inputclass = '';

	// These properties are required to implement ControlValueAccessor
	onChange = (_: any) => {};
	onTouched = () => {};

	constructor() {}

	ngOnInit(): void {
		if (this.label) {
			const formattedLabel = this.label.replace(/ /g, '');
			this.inputclass = formattedLabel.charAt(0).toLowerCase() + formattedLabel.slice(1);
		} 
		else {
			// Handle the case when this.label is undefined
			this.inputclass = ''; // or some default value
		}
	}

	optionChange(val: any) {
		this.selectedValue = val;
		this.onChange(val); // Call onChange to notify Angular that the value has changed
		this.inputFC.setValue(val);
		if (!this.selectedValue) {
			this.clearInput();
		}
	}

	// Implement the ControlValueAccessor methods
	writeValue(value: any) {
		this.selectedValue = value;
		this.onChange(value); // Call onChange to notify Angular that the value has changed
		this.inputFC.setValue(value);
		if(this.label=='Without QC Project'){
			if (this.selectedValue==null) {
				this.clearInput();
			}
		}
		else{
			if (!this.selectedValue) {
				this.clearInput();
			}
		}
		
	}

	registerOnChange(fn: any) {
		this.onChange = fn;
	}

	registerOnTouched(fn: any) {
		this.onTouched = fn;
	}
	private clearInput(): void {
		this.inputFC.reset();
	}
}
