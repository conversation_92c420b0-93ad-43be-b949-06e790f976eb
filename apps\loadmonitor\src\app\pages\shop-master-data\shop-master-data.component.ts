import { Component, OnInit, ViewChild, AfterContentInit, Input, HostListener, On<PERSON><PERSON>roy, ChangeDetectorRef } from '@angular/core';
import { MatSort } from '@angular/material/sort';
import { MatTable, MatTableDataSource } from '@angular/material/table';
import { MatPaginator } from '@angular/material/paginator';
import { FormBuilder, FormControl, FormGroup } from '@angular/forms';
import { BehaviorSubject, Observable, Subject, Subscription } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { map, startWith } from 'rxjs/operators';
import { RangedWeightingImportFilter } from '@loadmonitor/shared/interfaces/RangedWeightingImportFilter';
import { TagItem } from '@loadmonitor/shared/interfaces/TagItem';
import { SettingsConstants } from '@loadmonitor/shared/settings.constants';
import { JobTypes } from '@loadmonitor/shared/jobTypes';
import { ShopMasterDataService } from '@loadmonitor/shared/services/shop-master-data.service';
import { ShopMasterData } from '@loadmonitor/shared/interfaces/ShopMasterData';
import { DefaultSnackbarComponent } from '@loadmonitor/shared/components/default-snackbar/default-snackbar.component';
import { MatSnackBar } from '@angular/material/snack-bar';
import { PropertyPage } from '@loadmonitor/shared/interfaces/PropertyPage';
import { HelperService } from '@loadmonitor/shared/services/helper.service';
import { animate, state, style, transition, trigger } from '@angular/animations';
import { ShopMasterDataCache } from '@loadmonitor/shared/interfaces/caching/ShopMasterDataCache';
import { Filter, FilterRecords, FilterService, FilterTrayComponent } from '@dwh/lmx-lib/src';
import { ToggleButtonComponent } from '@gfk/ng-lib';
import { environment } from 'apps/loadmonitor/src/environments/environment';
import { DatePipe } from '@angular/common';
import * as moment from 'moment';
import { FilterTrayCacheService } from '@loadmonitor/shared/services/filter-tray-cache.service';

@Component({
	selector: 'lm-shop-master-data',
	templateUrl: './shop-master-data.component.html',
	styleUrls: ['./shop-master-data.component.scss'],
	animations: [
		trigger('detailExpand', [
			state('collapsed', style({ height: '0px', minHeight: '0' })),
			state('expanded', style({ height: '*' })),
			transition('expanded <=> collapsed', animate('225ms cubic-bezier(0.4, 0.0, 0.2, 1)')),
		]),
	],
})
export class ShopMasterDataComponent implements OnInit, AfterContentInit, OnDestroy {

	/* *********
	 * ViewChild
	 ********* */
	@Input() icon = 'delete';
	@ViewChild(MatSort)
	sort: MatSort = new MatSort();

	@ViewChild(MatTable)
	table!: MatTable<ShopMasterData>;

	@ViewChild(MatPaginator)
	paginator!: MatPaginator;
	@ViewChild(FilterTrayComponent) filterTrayComponent: FilterTrayComponent | undefined;
	@ViewChild(ToggleButtonComponent) togglebuttonComponent: ToggleButtonComponent | undefined;

	/* *********************
	 * Components variables
	 ********************* */
	jobType: JobTypes = JobTypes.shopMasterData;
	isPeriodFilterHidden = false;

	// Progress Spinner visibility
	isLoading = false;
	selectedRow: any;
	public ShopMasterDataForm!: FormGroup;
	public cacheObj: ShopMasterDataCache = {} as ShopMasterDataCache;

  selectedFilters: any;
	cacheValue: BehaviorSubject<any> = new BehaviorSubject<any>({});
	resetForm: Subject<boolean> = new Subject();
	resetPeriodicityFilter: Subject<boolean> = new Subject();

	cacheName = 'cache::ShopMasterData';
	/* *********************
	 * Components parameters
	 ********************* */

	//Mat-Table
	dataSource = new MatTableDataSource<ShopMasterData>();
	filter: RangedWeightingImportFilter | undefined;
	displayedColumns: string[] = ['menu', 'unitId', 'status', 'outletMasterDataName', 'createdBy', 'created', 'started', 'finished', 'errorMessage'];

	//Mat-Expansion-Panel
	panelOpenState = false;

	readonly date_with_time = SettingsConstants.DATE_FORMAT_WITH_TIME;
	readonly only_time = SettingsConstants.FORMAT_ONLY_TIME;

	// Mat-Chips
	statusIdsCtrl = new FormControl();
	filteredStatusIds: Observable<TagItem[]>;
	allStatusIds: TagItem[] = [];

	// Property Pages
	expandedElement!: PropertyPage | null;
	shopMasterDataDetail!: PropertyPage[];

	/* **************
	 * Public Methods
	 ************** */

	/* *********************
	 * Components variables
	 ********************* */
	eventsSubject: Subject<void> = new Subject<void>();
	IsDisabled = false;
	environments = environment.environments;
	IsEnabled = false;
	FilterTrayOpenFlag = true;
	modalshow = false;
	filterCount = '';
	toggleInterval!: any;
	timer = 60;
	EnableRefresh = true;
	cacheInterval!: any;
	defaultoption = localStorage.getItem(this.cacheName + '-LastFilter') || 'Default';
	defaultfilter = JSON.parse(localStorage.getItem(this.cacheName + '-DefaultFilter') || '[]');
	private subscription!: Subscription | undefined;
	currentfilter = JSON.parse(localStorage.getItem('QuickFilter') || '[]').filter(
		(x) => x.user == localStorage.getItem('username') && x.slice == window.location.href.split('/')[3]
	);
	refreshedDateTime!: boolean;
	shopMasterData!: any[];

  @HostListener('document:visibilitychange', ['$event'])
	appVisibility() {
		if (document.hidden) {
			if (this.cacheInterval) {
				clearInterval(this.cacheInterval);
			}
			if (this.toggleInterval) {
				clearInterval(this.toggleInterval);
			}
		} else {
			if (this.cacheObj.isAutoRefresh === true) {
				this.cacheInterval = setInterval(() => {
					if (this.defaultoption != 'Default') {
						this.refresh();
					}
				}, this.timer * 1000);
			} else {
				if (this.cacheInterval !== undefined) clearInterval(this.cacheInterval);
				if (this.toggleInterval !== undefined) clearInterval(this.toggleInterval);
			}
		}
	}
	private destroy$ = new Subject<void>();

	constructor(
		private shopMasterDataService: ShopMasterDataService,
		private formBuilder: FormBuilder,
		private snackBar: MatSnackBar,
		private helperService: HelperService,
		public filterService: FilterService,
		public datepipe: DatePipe,
		private cdRef: ChangeDetectorRef,
		private filterTrayCacheService: FilterTrayCacheService
	) {
		this.filteredStatusIds = this.statusIdsCtrl.valueChanges.pipe(
			startWith(null),
			map((item: TagItem | null) => (item ? this.tagItemsFilter(item.name) : this.allStatusIds.slice()))
		);
	}

	private updateFC(): void {
		if(this.cacheObj){
			this.filterTrayCacheService.getCacheValueData(this.cacheObj);
		}
	}

	ngOnInit(): void {
		if (this.environments.filter((x) => x.location == window.location.origin)[0]) {
			this.IsEnabled = this.environments.filter((x) => x.location == window.location.origin)[0].enabled;
		} else {
			this.IsEnabled = false;
		}
		this.ShopMasterDataForm = this.formBuilder.group({
			isAutoRefresh: this.togglebuttonComponent?.checked,
		});
		this.SetFiltersCount();

	}

	// eslint-disable-next-line @angular-eslint/use-lifecycle-interface
	ngAfterViewInit() {
		this.cdRef.detectChanges();
	  }
	ngAfterContentInit() {
		this.afterChildComponentsLoaded();
		this.CreateFilterSummaryChips();
		this.SetFiltersCount();

		if (this.cacheObj.isAutoRefresh === true) {
			if (this.cacheInterval === undefined) {
				this.cacheInterval = window.setInterval(() => {
					if (this.defaultoption != 'Default') {
						this.refresh();
					}
				}, this.timer * 1000);
			}
		} else {
			if (this.cacheInterval !== undefined) clearInterval(this.cacheInterval);
			if (this.toggleInterval !== undefined) clearInterval(this.toggleInterval);
		}
	}

	afterChildComponentsLoaded() {
		this.cacheObj = JSON.parse(localStorage.getItem(this.cacheName) || '{}');
		this.updateFC();
	}

	refresh(): void {
		this.refreshedDateTime = false;
		this.filterTrayComponent?.showSpinner(true);
		this.isLoading = true;
		this.destroy$.next();
		this.shopMasterDataService
			.getAsync(
				this.selectedFilters.countries?.map((country) => country),
				this.selectedFilters.category?.map((category) => category.id),
				this.selectedFilters.sector?.map((sector) => sector),
				this.selectedFilters.domainProductGroup?.map((domainProductGroup) => domainProductGroup.id),
				this.selectedFilters.channelIds?.map((chanel) => chanel),
				this.selectedFilters.periodIds?.map((period) => period),
				this.selectedFilters.periodicityIds ? [this.selectedFilters.periodicityIds] : [],
				this.selectedFilters.startDate ? this.selectedFilters.startDate : null,
				this.selectedFilters.endDate ? moment(this.selectedFilters.endDate).format().split('T')[0] + 'T23:59:59.999Z' : null,
        		this.selectedFilters.users?.map((users) => users.userName),
				this.selectedFilters.status?.map((status) => status),
        		this.selectedFilters.unitIds
			)
			.pipe(
				takeUntil(this.destroy$)
			)
			.subscribe((result) => {
				this.refreshedDateTime = true;
				if (result.moreRecordsAvailable) {
					this.notify('More than 1000 results exist!');
				}

				if (result.count === 0) {
					this.notify('No result found!');
					this.shopMasterData = [];
				}
				this.isLoading = false;
				this.shopMasterData = result.records;
				this.dataSource = new MatTableDataSource<ShopMasterData>(result.records);
				this.dataSource.sort = this.sort;
				this.paginator.pageIndex = 0;
				this.dataSource.paginator = this.paginator;
				this.table.dataSource = this.dataSource;
				this.filterTrayComponent?.showSpinner(false);
			},
			(error) => {
				if (error.status === 504) {
					this.notify('Gateway Timeout. The server took too long to respond. Please try again later.');
				} 
				else {
					this.notify(`An error occurred: ${error.message}`);
				}
				this.isLoading = false;
				this.filterTrayComponent?.showSpinner(false);
			});
		this.SetFiltersCount();
	}

	private tagItemsFilter(value: string): TagItem[] {
		const filterValue = value.toLowerCase();

		return this.allStatusIds.filter((item) => item.name.toLowerCase().indexOf(filterValue) === 0);
	}

	private notify(message: string) {
		this.snackBar.openFromComponent(DefaultSnackbarComponent, {
			duration: 10 * 1000,
			data: message,
		});
	}

	selectRow(row: ShopMasterData) {
		this.shopMasterDataDetail = [];
		row.isSelected = true;

		this.dataSource.data.forEach((element) => {
			if (row.unitId != element.unitId) {
				element.isExpanded = false;
				element.isSelected = false;
			}
		});

		this.selectedRow = row;
		this.shopMasterDataService.getDetailAsync(row.unitId).subscribe((result) => {
			this.helperService.floatingPanelData$.next({ data: result.records, jobType: JobTypes.shopMasterData });
		});
	}

	clickNextButton() {
		this.selectedRow.isSelected = false;
	}

	//Quick Filter Methods//
	openmodal() {
		this.eventsSubject.next();
	}

	disablesave(status) {
		this.IsDisabled = status;
	}


	setFormValue(autoRefreshStatus) {
		this.ShopMasterDataForm.setValue({
			isAutoRefresh: autoRefreshStatus === undefined || autoRefreshStatus === false ? false : true,
		});
	}

	saveInQuickFilter(autoRefreshStatus, filterName) {
		this.setFormValue(autoRefreshStatus);
		this.currentfilter = JSON.parse(localStorage.getItem('QuickFilter') || '[]');
		this.currentfilter.push({
			data: this.selectedFilters,
			filter: filterName,
			slice: window.location.href.split('/')[3],
			user: localStorage.getItem('username'),
		});
		this.defaultoption = filterName;
		const oldcache = this.cacheObj;
		const olddefault = localStorage.getItem(this.cacheName + '-DefaultFilter');
		this.ConfirmApply(autoRefreshStatus);
		this.cacheObj = oldcache;
		localStorage.setItem(this.cacheName + '-DefaultFilter', olddefault || '[]');
		localStorage.setItem('QuickFilter', JSON.stringify(this.currentfilter));
		localStorage.setItem(this.cacheName + '-LastFilter', filterName);
		this.SetFiltersCount();
	}

	applyfilter(selectedvalue) {
    	this.resetForm.next(true);
		const oldautotoggle = this.cacheObj.isAutoRefresh;
		selectedvalue = selectedvalue.value ? selectedvalue.value : selectedvalue;
		this.defaultoption = selectedvalue;
		localStorage.setItem(this.cacheName + '-LastFilter', this.defaultoption || 'Default');
		if (selectedvalue == 'Default') {
			if (
				JSON.parse(localStorage.getItem(this.cacheName + '-DefaultFilter') || '[]').filter(
					(x) => x.user == localStorage.getItem('username') && x.slice == window.location.href.split('/')[3]
				)[0]
			) {
				this.cacheObj = JSON.parse(localStorage.getItem(this.cacheName + '-DefaultFilter') || '[]').filter(
					(x) => x.user == localStorage.getItem('username') && x.slice == window.location.href.split('/')[3]
				)[0]['data'];
			}

			localStorage.setItem(this.cacheName, JSON.stringify(this.cacheObj));
		} else {
			this.cacheObj = JSON.parse(localStorage.getItem('QuickFilter') || '[]')
				.filter((x) => x.user == localStorage.getItem('username') && x.slice == window.location.href.split('/')[3])
				.filter((x) => x.filter == selectedvalue)[0]['data'];
			localStorage.setItem(this.cacheName, JSON.stringify(this.cacheObj));
		}
		this.cacheObj.isAutoRefresh = oldautotoggle;
		this.CreateFilterSummaryChips();


		if (this.cacheObj.selectedDays == '3') {
			const date = new Date();
			this.cacheObj.startDate = new Date(Date.UTC(date.getFullYear(), date.getMonth(), date.getDate() - 2, 0, 0, 0, 0));
			this.cacheObj.endDate = new Date(Date.UTC(date.getFullYear(), date.getMonth(), date.getDate(), 0, 0, 0, 0));
		} else if (this.cacheObj.selectedDays == '2') {
			const date = new Date();
			this.cacheObj.startDate = new Date(Date.UTC(date.getFullYear(), date.getMonth(), date.getDate(), 0, 0, 0, 0));
			this.cacheObj.endDate = new Date(Date.UTC(date.getFullYear(), date.getMonth(), date.getDate(), 0, 0, 0, 0));
		}
		setTimeout(() => {
			this.updateFC();
			this.refresh();
		}, 200);
		this.selectedFilters = this.cacheObj;
		localStorage.setItem(this.cacheName, JSON.stringify(this.cacheObj));
	}

	saveInCache(autoRefreshStatus, AutoToggle) {
		this.setFormValue(autoRefreshStatus);
		localStorage.setItem(this.cacheName, JSON.stringify(this.selectedFilters));
		this.cacheObj = JSON.parse(localStorage.getItem(this.cacheName) || '{}');
		if (!AutoToggle) {
			this.defaultfilter = [];
			this.defaultfilter.push({
				data: this.selectedFilters,
				slice: window.location.href.split('/')[3],
				user: localStorage.getItem('username'),
			});
			localStorage.setItem(this.cacheName + '-DefaultFilter', JSON.stringify(this.defaultfilter));
			let nofilter = 0;
			let selectedfilter = '';
			this.currentfilter.forEach((i) => {
				// if (i.filter == this.defaultoption) {
				if(JSON.stringify(i.data) == JSON.stringify(this.cacheObj)){
					nofilter++;
					selectedfilter = i.filter;
				}
			});
			if (nofilter == 0) {
				this.defaultoption = 'Default';
			} else {
				this.defaultoption = selectedfilter;
			}
			localStorage.setItem(this.cacheName + '-LastFilter', this.defaultoption);
		}
		this.updateFC();
	}

	toggleChecked(check: any) {
		if (check === true) {
			this.saveInCache(check, true);
			this.toggleInterval = setInterval(() => {
				if (this.defaultoption != 'Default') {
					this.refresh();
				}
			}, this.timer * 1000);
		} else {
			if (this.toggleInterval !== undefined) clearInterval(this.toggleInterval);
			if (this.cacheInterval !== undefined) clearInterval(this.cacheInterval);
			this.saveInCache(check, true);
		}
	}

	CreateFilterSummaryChips() {
		if (this.cacheObj && Object.keys(this.cacheObj).length) {
			this.usersFilterSummary();
			this.statusFilterSummary();
			this.countryFilterSummary();
			this.domainProductGroupIdsFilterSummary();
			this.sectorIdsFilterSummary();
			this.categoryIdsFilterSummary();
			this.periodicityIdsFilterSummary();
			this.periodIdsFilterSummary();
			this.cachedChannelIdsFilterSummary();
			this.unitIdsFilterSummary();
			this.datesFilterSummary(this.cacheObj.startDate, this.cacheObj.endDate);
		}
	}

	ConfirmApply(autoRefreshStatus) {
		this.saveInCache(autoRefreshStatus, false);
		this.CreateFilterSummaryChips();

		let count;
		this.filterService.filtersSelected$.subscribe((selectedFilters: FilterRecords) => {
			count = Number(Object.keys(selectedFilters).length);
		});

		if (count > 0) {
			this.refresh();
		} else {
			if (confirm('Loading results without any filters selected will take some time to load. Are you sure you want to proceed?')) {
				this.refresh();
			}
		}
	}

	public isShownFilterTrayEventHandler($event: boolean) {
		this.filterTrayComponent?.toggleShowTray($event);
		this.FilterTrayOpenFlag = $event;
	}

	getWidth() {
		this.FilterTrayOpenFlag = !this.FilterTrayOpenFlag;
		return '100%';
	}

	filterSummaryChipClear(filteredItems) {
		if (filteredItems == 'ALL') {
			if (confirm('This will remove all of your selected filters and could not be reversed. Are you sure you want to do that?')) {
        		this.resetForm.next(true);
				this.filterTrayCacheService.getCacheNameData(this.cacheName);
		        this.resetPeriodicityFilter.next(true);
			}
		}
		this.saveInCache(this.cacheObj.isAutoRefresh, false);
		this.CreateFilterSummaryChips();
		this.SetFiltersCount();
	}

	SetFiltersCount() {
		let count;
		this.filterService.filtersSelected$.subscribe((selectedFilters: FilterRecords) => {
			count = Number(Object.keys(selectedFilters).length);
		});
		if (count > 0) {
			this.filterCount = '(' + count.toString() + ')';
		} else {
			this.filterCount = '';
		}
	}

	ngOnDestroy() {
		if (this.cacheInterval) {
			clearInterval(this.cacheInterval);
		}

		if (this.toggleInterval) {
			clearInterval(this.toggleInterval);
		}
		this.destroy$.next();
    	this.destroy$.complete();
	}

	countryFilterSummary() {
		this.filterService.setFilters({
			countryIds: {
				name: 'Country',
				values: this.cacheObj.countries,
				slice: window.location.href.split('/')[3],
			} as Filter,
		});
	}

	usersFilterSummary() {
		this.filterService.setFilters({
			userIds: {
				name: 'User',
				values: this.cacheObj.users?.map((i) => i.name),
				slice: window.location.href.split('/')[3],
			} as Filter,
		});
	}

	statusFilterSummary() {
		this.filterService.setFilters({
			statusIds: {
				name: 'Status',
				values: this.cacheObj.status,
				slice: window.location.href.split('/')[3],
			} as Filter,
		});
	}

	domainProductGroupIdsFilterSummary() {
		this.filterService.setFilters({
			domainProductGroupIds: {
				name: 'Domain',
				values: this.cacheObj.domainProductGroup,
				slice: window.location.href.split('/')[3],
			} as Filter,
		});
	}

	sectorIdsFilterSummary() {
		this.filterService.setFilters({
			sectorIds: {
				name: 'sectorIds',
				values: this.cacheObj.sector,
				slice: window.location.href.split('/')[3],
			} as unknown as Filter,
		});
	}

	categoryIdsFilterSummary() {
		this.filterService.setFilters({
			categoryIds: {
				name: 'Operation',
				values: this.cacheObj.category,
				slice: window.location.href.split('/')[3],
			} as Filter,
		});
	}

	periodicityIdsFilterSummary() {
		this.filterService.setFilters({
			periodicityIds: {
				name: 'periodicityIds',
				values: this.cacheObj.periodicityIds? [{ value: this.cacheObj.periodicityIds, label: this.cacheObj.periodicityIds }] : null,
				slice: window.location.href.split('/')[3],
			} as unknown as Filter,
		});
	}

	periodIdsFilterSummary() {
		this.filterService.setFilters({
			periodIds: {
				name: 'Periodicity',
				values: this.cacheObj.periodIds,
				slice: window.location.href.split('/')[3],
			} as Filter,
		});
	}

	cachedChannelIdsFilterSummary() {
		this.filterService.setFilters({
			baseProjectIds: {
				name: 'Periodicity',
				values: this.cacheObj.channels,
				slice: window.location.href.split('/')[3],
			} as Filter,
		});
	}

	unitIdsFilterSummary() {
		this.filterService.setFilters({
			unitids: {
				name: 'unitids',
				values: this.cacheObj.unitIds,
				slice: window.location.href.split('/')[3],
			} as unknown as Filter,
		});
	}

	datesFilterSummary(dateStart: any, dateEnd: any) {
		if (dateStart != null || dateEnd != null) {
			const start = dateStart == 'Invalid Date' || dateStart == null ? '' : this.datepipe.transform(dateStart, 'yyyy-MM-dd');
			const end = dateEnd == 'Invalid Date' || dateEnd == null ? '' : this.datepipe.transform(dateEnd, 'yyyy-MM-dd');
			this.filterService.setFilters({
				dates: {
					name: 'Date',
					values: (start !== undefined && end !== undefined) ? [start, end] : null,
					slice: window.location.href.split('/')[3],
				} as Filter,
			});
		} else {
			this.filterService.setFilters({
				dates: {
					name: 'Date',
					values: null,
					slice: window.location.href.split('/')[3],
				} as unknown as Filter,
			});
		}
	}
	//Quick Filter Methods End//

  getSelectedFilters(selectedFilters: any){
		this.cacheObj = selectedFilters;
		this.selectedFilters = selectedFilters;
	}
}
