
import { UserCache } from '@loadmonitor/shared/interfaces/caching/UserCache';
import { Country } from '../Country';
import { CsvImports } from '../CsvImports';
import { DomainProductGroups } from '../DomainProductGroups';
import { Category } from '../Category';
import { Sector } from '../Sector';

export interface CSVImportCache
{
    baseProjectName: string;
    baseProjectIds: number[];
    unitTypeIds: number[];

    sectorIds: number[];
    categoryIds: number[];
    domainProductGroupIds: number[];
    domainProductGroup: DomainProductGroups[];
    countryIds: number[];
    status: number[];
    selectedDays: string;
    startDate?: Date;
    endDate?: Date;
    users?: UserCache[];

    unitType: CsvImports[];


    periodicityIds: number[];
    periodIds: any;

    jobIds: number[];
    isAutoRefresh?: boolean;
    countries:Country[];
    category:Category[];
    sector:Sector[];
    fileType: string[];
}
