import { Component, OnInit, ViewChild, AfterContentInit, AfterViewInit, ChangeDetectorRef, Input, HostListener, OnDestroy } from '@angular/core';
import { MatSort } from '@angular/material/sort';
import { MatTable, MatTableDataSource } from '@angular/material/table';
import { PublishRepublish } from '@loadmonitor/shared/interfaces/PublishRepublish';
import { MatPaginator } from '@angular/material/paginator';
import { COMMA, ENTER } from '@angular/cdk/keycodes';
import { MatChipInputEvent } from '@angular/material/chips';
import { FormBuilder,  FormGroup } from '@angular/forms';
import { BehaviorSubject,  Subject, Subscription } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { MatSnackBar } from '@angular/material/snack-bar';
import { DefaultSnackbarComponent } from '@loadmonitor/shared/components/default-snackbar/default-snackbar.component';
import { PublishRepublishFilterParams, PublishRepublishService } from '@loadmonitor/shared/services/publish-republish.service';
import { Operation } from '@loadmonitor/shared/interfaces/Operation';
import { JobTypes } from '@loadmonitor/shared/jobTypes';
import { MatDialog } from '@angular/material/dialog';
import { SettingsConstants } from '@loadmonitor/shared/settings.constants';
import { RBPublishCache } from '@loadmonitor/shared/interfaces/caching/RBPublishCache';
import { CreateTicketDialogComponent } from '@loadmonitor/shared/components/create-ticket-dialog/create-ticket-dialog.component';
import { publishRepublishJiraProperties } from '@loadmonitor/shared/interfaces/PublishRepublishJiraProperty';
import { SelectionModel } from '@angular/cdk/collections';
import {  Filter, FilterRecords, FilterService, FilterTrayComponent } from '@dwh/lmx-lib';
import { environment } from 'apps/loadmonitor/src/environments/environment';
import { DatePipe } from '@angular/common';
import { ToggleButtonComponent } from '@gfk/ng-lib';
import { HelperService } from '@loadmonitor/shared/services/helper.service';
import * as moment from 'moment';
import { PropertyPage } from '@loadmonitor/shared/interfaces/PropertyPage';
import { animate, state, style, transition, trigger } from '@angular/animations';
import { PublishRepublishPropertyPage } from '@loadmonitor/shared/interfaces/PublishRepublishPropertyPage';
import { FilterTrayCacheService } from '@loadmonitor/shared/services/filter-tray-cache.service';

@Component({
	selector: 'lm-publish-republish',
	templateUrl: './publish-republish.component.html',
	styleUrls: ['./publish-republish.component.scss'],
	animations: [
		trigger('detailExpand', [
			state('collapsed', style({ height: '0px', minHeight: '0' })),
			state('expanded', style({ height: '*' })),
			transition('expanded <=> collapsed', animate('225ms cubic-bezier(0.4, 0.0, 0.2, 1)')),
		]),
	],
})
export class PublishRepublishComponent implements OnInit, AfterContentInit, AfterViewInit, OnDestroy {
	records!: PublishRepublish[];
	oldrecords!: PublishRepublish[];
	rec: PublishRepublish[] = [];
	reccound=0;

	/* *********
	 * ViewChild
	 ********* */
	@Input() icon = 'delete';

	@ViewChild(FilterTrayComponent) filterTrayComponent: FilterTrayComponent | undefined;
	@ViewChild(ToggleButtonComponent) togglebuttonComponent: ToggleButtonComponent | undefined;

	@ViewChild(MatSort)
	sort: MatSort = new MatSort();

	@ViewChild(MatTable)
	table!: MatTable<PublishRepublish>;

	@ViewChild(MatPaginator)
	paginator!: MatPaginator;


	//Mat-Expansion-Panel
	panelOpenState = false;

	/* *********************
	 * Components variables
	 ********************* */
	eventsSubject: Subject<void> = new Subject<void>();
	jobType: JobTypes = JobTypes.rbPublishUnpublish;
	expandedElement!: PropertyPage | null;
	isPeriodFilterHidden = false;
	IsDisabled = false;
	FilterTrayOpenFlag = true;
	environments = environment.environments;
	IsEnabled = false;
	currentfilter = JSON.parse(localStorage.getItem('QuickFilter') || '[]').filter(
		(x) => x.user == localStorage.getItem('username') && x.slice == window.location.href.split('/')[3]
	);
	modalshow = false;
	filterCount = '';
	toggleInterval!: any;
	timer = 60;
	EnableRefresh = true;
	cacheInterval!: any;

	//Tag-Chip-List
	readonly separatorKeysCodes = [ENTER, COMMA] as const;


	public cacheObj: RBPublishCache = {} as RBPublishCache;

  selectedFilters: any;
	cacheValue: BehaviorSubject<any> = new BehaviorSubject<any>({});
	resetForm: Subject<boolean> = new Subject();
	resetPeriodicityFilter: Subject<boolean> = new Subject();

	cacheName = 'cache::RBPublish';
	defaultoption = localStorage.getItem(this.cacheName + '-LastFilter') || 'Default';
	defaultfilter = JSON.parse(localStorage.getItem(this.cacheName + '-DefaultFilter') || '[]');

	// Progress Spinner visibility
	isLoading = false;

	/* *********************
	 * Components parameters
	 ********************* */

	//Mat-Table
	dataSource = new MatTableDataSource<PublishRepublish>();
	selection = new SelectionModel<PublishRepublish>(true, []);
	IsSelectAll = false;
	displayedColumns: string[] = [
		'details',
		'menu',
		'loadId',
		'status',
		'rbProject',
		'rbProjectId',
		'projectType',
		'country',
		'fromPeriod',
		'toPeriod',
		'createdBy',
		'created',
		'started',
		'finished',
	];

	RbPublishForm!: FormGroup;

	readonly date_with_time = SettingsConstants.DATE_FORMAT_WITH_TIME;
	private subscription!: Subscription | undefined;
	refreshedDateTime!: boolean;
	publishRepublishData!: any[];


	@HostListener('document:visibilitychange', ['$event'])
	appVisibility() {
		if (document.hidden) {
			if (this.cacheInterval) {
				clearInterval(this.cacheInterval);
			}
			if (this.toggleInterval) {
				clearInterval(this.toggleInterval);
			}
		} else {
			if (this.cacheObj.isAutoRefresh === true) {
				this.cacheInterval = setInterval(() => {
					if (this.defaultoption != 'Default') {
						this.refresh();
					}
				}, this.timer * 1000);
			} else {
				if (this.cacheInterval !== undefined) clearInterval(this.cacheInterval);
				if (this.toggleInterval !== undefined) clearInterval(this.toggleInterval);
			}
		}
	}
	private destroy$ = new Subject<void>();

	constructor(
		public dialog: MatDialog,
		private rbPublishService: PublishRepublishService,
		private snackBar: MatSnackBar,
		private formBuilder: FormBuilder,
		private cdRef: ChangeDetectorRef,
		public filterService: FilterService,
		public datepipe: DatePipe,
		private helperService: HelperService,
		private filterTrayCacheService: FilterTrayCacheService
	) {}

	selectRow(row: PublishRepublish) {
		const recs = this.records.filter((j) => j.loadId == row.loadId);
		const mappedRecords: PublishRepublishPropertyPage[] = recs.map((record) => ({
			jobId: record.jobId,
			status: record.status,
			message: record.message,
			operation: record.operation,
			serverName: record.serverName,
			specification: record.specification,
			created: record.created,
			started: record.started,
			finished: record.finished,
		}));

		mappedRecords.sort((a, b) => {
			const operationA = a.operation.toUpperCase();
			const operationB = b.operation.toUpperCase();

			if (operationA < operationB) {
				return -1;
			}
			if (operationA > operationB) {
				return 1;
			}

			return 0;
		});

		this.dataSource.data.forEach((element) => {
			if (row.loadId != element.loadId) {
				element.isExpanded = false;
				element.isSelected = false;
			}
		});
		this.helperService.floatingPanelData$.next({ data: mappedRecords, jobType: JobTypes.rbPublishUnpublish });
		this.records = this.oldrecords;
	}

	private updateFC(): void {
		if(this.cacheObj){
			this.filterTrayCacheService.getCacheValueData(this.cacheObj);
		}
	}

	ngOnInit(): void {
		if (this.environments.filter((x) => x.location == window.location.origin)[0]) {
			this.IsEnabled = this.environments.filter((x) => x.location == window.location.origin)[0].enabled;
		} else {
			this.IsEnabled = false;
		}
		this.RbPublishForm = this.formBuilder.group({
			isAutoRefresh: this.togglebuttonComponent?.checked,
		});

	}

	ngAfterContentInit() {
		this.afterChildComponentsLoaded();
		this.CreateFilterSummaryChips();
		this.SetFiltersCount();
		if (this.cacheObj.isAutoRefresh === true) {
			if (this.cacheInterval === undefined) {
				this.cacheInterval = window.setInterval(() => {
					if (this.defaultoption != 'Default') {
						this.refresh();
					}
				}, this.timer * 1000);
			}
		} else {
			if (this.cacheInterval !== undefined) clearInterval(this.cacheInterval);
			if (this.toggleInterval !== undefined) clearInterval(this.toggleInterval);
		}
	}

	ngAfterViewInit() {
		this.cdRef.detectChanges();
	}

	afterChildComponentsLoaded() {
		this.cacheObj = JSON.parse(localStorage.getItem(this.cacheName) || '{}');
		this.updateFC();
	}
	//Quick Filter Methods//
	openmodal() {
		this.eventsSubject.next();
	}
	disablesave(status) {
		this.IsDisabled = status;
	}

	setFormValue(autoRefreshStatus) {
		this.RbPublishForm.setValue({
			isAutoRefresh: autoRefreshStatus === undefined || autoRefreshStatus === false ? false : true,
		});
	}
	saveInQuickFilter(autoRefreshStatus, filterName) {
		this.setFormValue(autoRefreshStatus);
		this.currentfilter = JSON.parse(localStorage.getItem('QuickFilter') || '[]');
		this.currentfilter.push({
			data: this.selectedFilters,
			filter: filterName,
			slice: window.location.href.split('/')[3],
			user: localStorage.getItem('username'),
		});
		this.defaultoption = filterName;
		const oldcache = this.cacheObj;
		const olddefault = localStorage.getItem(this.cacheName + '-DefaultFilter');
		this.ConfirmApply(autoRefreshStatus);
		this.cacheObj = oldcache;
		localStorage.setItem(this.cacheName + '-DefaultFilter', olddefault || '[]');
		localStorage.setItem('QuickFilter', JSON.stringify(this.currentfilter));
		localStorage.setItem(this.cacheName + '-LastFilter', filterName);
		//this.SetFiltersCount();
	}

	applyfilter(selectedvalue) {
    this.resetForm.next(true);
		const oldautotoggle = this.cacheObj.isAutoRefresh;
		selectedvalue = selectedvalue.value ? selectedvalue.value : selectedvalue;
		this.defaultoption = selectedvalue;
		localStorage.setItem(this.cacheName + '-LastFilter', this.defaultoption || 'Default');
		if (selectedvalue == 'Default') {
			if (
				JSON.parse(localStorage.getItem(this.cacheName + '-DefaultFilter') || '[]').filter(
					(x) => x.user == localStorage.getItem('username') && x.slice == window.location.href.split('/')[3]
				)[0]
			) {
				this.cacheObj = JSON.parse(localStorage.getItem(this.cacheName + '-DefaultFilter') || '[]').filter(
					(x) => x.user == localStorage.getItem('username') && x.slice == window.location.href.split('/')[3]
				)[0]['data'];
			}
			localStorage.setItem(this.cacheName, JSON.stringify(this.cacheObj));
		} else {
			this.cacheObj = JSON.parse(localStorage.getItem('QuickFilter') || '[]')
				.filter((x) => x.user == localStorage.getItem('username') && x.slice == window.location.href.split('/')[3])
				.filter((x) => x.filter == selectedvalue)[0]['data'];
			localStorage.setItem(this.cacheName, JSON.stringify(this.cacheObj));
		}
		this.cacheObj.isAutoRefresh = oldautotoggle;
		this.CreateFilterSummaryChips();

		this.selectedFilters = this.cacheObj;
    if (this.cacheObj.selectedDays == '3') {
			const date = new Date();
			this.cacheObj.startDate = new Date(Date.UTC(date.getFullYear(), date.getMonth(), date.getDate() - 2, 0, 0, 0, 0));
			this.cacheObj.endDate = new Date(Date.UTC(date.getFullYear(), date.getMonth(), date.getDate(), 0, 0, 0, 0));
		} else if (this.cacheObj.selectedDays == '2') {
			const date = new Date();
			this.cacheObj.startDate = new Date(Date.UTC(date.getFullYear(), date.getMonth(), date.getDate(), 0, 0, 0, 0));
			this.cacheObj.endDate = new Date(Date.UTC(date.getFullYear(), date.getMonth(), date.getDate(), 0, 0, 0, 0));
		}
		setTimeout(() => {
			this.updateFC();
			this.refresh();
		}, 200);
		localStorage.setItem(this.cacheName, JSON.stringify(this.cacheObj));
	}
	//Quick Filter Methods End//

	toggleChecked(check: any) {
		if (check === true) {
			this.saveInCache(check, true);
			this.toggleInterval = setInterval(() => {
				if (this.defaultoption != 'Default') {
					this.refresh();
				}
			}, this.timer * 1000);
		} else {
			if (this.toggleInterval !== undefined) clearInterval(this.toggleInterval);
			if (this.cacheInterval !== undefined) clearInterval(this.cacheInterval);
			this.saveInCache(check, true);
		}
	}

	ngOnDestroy() {
		if (this.cacheInterval) {
			clearInterval(this.cacheInterval);
		}

		if (this.toggleInterval) {
			clearInterval(this.toggleInterval);
		}
		this.destroy$.next();
    	this.destroy$.complete();
	}
	SetFiltersCount() {
		let count;
		this.filterService.filtersSelected$.subscribe((selectedFilters: FilterRecords) => {
			count = Number(Object.keys(selectedFilters).length);
		});
		if (count > 0) {
			this.filterCount = '(' + count.toString() + ')';
		} else {
			this.filterCount = '';
		}
	}

	filterSummaryChipClear(filteredItems) {
		if (filteredItems == 'ALL') {
			if (confirm('This will remove all of your selected filters and could not be reversed. Are you sure you want to do that?')) {
				this.resetForm.next(true);
				this.filterTrayCacheService.getCacheNameData(this.cacheName);
				this.resetPeriodicityFilter.next(true);
				this.filterCount = '';
			}
		}
		this.saveInCache(this.cacheObj.isAutoRefresh, false);
		this.CreateFilterSummaryChips();
		this.SetFiltersCount();
	}
	ConfirmApply(autoRefreshStatus) {
		this.saveInCache(autoRefreshStatus, false);
		this.CreateFilterSummaryChips();

		let count;
		this.filterService.filtersSelected$.subscribe((selectedFilters: FilterRecords) => {
			count = Number(Object.keys(selectedFilters).length);
		});

		if (count > 0) {
			this.refresh();
		} else {
			if (confirm('Loading results without any filters selected will take some time to load. Are you sure you want to proceed?')) {
				this.refresh();
			}
		}
	}

	public isShownFilterTrayEventHandler($event: boolean) {
		this.filterTrayComponent?.toggleShowTray($event);
		this.FilterTrayOpenFlag = $event;
	}

	getWidth() {
		this.FilterTrayOpenFlag = !this.FilterTrayOpenFlag;
		return '100%';
	}

	saveInCache(autoRefreshStatus, AutoToggle) {
		this.setFormValue(autoRefreshStatus);

		localStorage.setItem(this.cacheName, JSON.stringify(this.selectedFilters));
		this.cacheObj = JSON.parse(localStorage.getItem(this.cacheName) || '{}');
		if(this.cacheObj.rbProjectName == null){
			this.cacheObj.rbProjectName = '';
		}
		if (!AutoToggle) {
			this.defaultfilter = [];
			this.defaultfilter.push({
				data: this.selectedFilters,
				slice: window.location.href.split('/')[3],
				user: localStorage.getItem('username'),
			});
			localStorage.setItem(this.cacheName + '-DefaultFilter', JSON.stringify(this.defaultfilter));
			let nofilter = 0;
			let selectedfilter = '';
			this.currentfilter.forEach((i) => {
				// if (i.filter == this.defaultoption) {
				if(JSON.stringify(i.data) == JSON.stringify(this.cacheObj)){
					nofilter++;
					selectedfilter = i.filter;
				}
			});
			if (nofilter == 0) {
				this.defaultoption = 'Default';
			} else {
				this.defaultoption = selectedfilter;
			}
		}
		localStorage.setItem(this.cacheName + '-LastFilter', this.defaultoption);
		this.updateFC();
	}


	// eslint-disable-next-line @typescript-eslint/no-unused-vars
	optionClicked(event: Event, item: Operation) {
		event.stopPropagation();
	}


	getFilterFormParams(): PublishRepublishFilterParams {
		const selectedOperations: any = [];
		this.filterTrayCacheService.operationList.forEach((operation: any) => {
			this.selectedFilters.operationIds.forEach((selectedOperation: any) => {
				if(operation.value == selectedOperation){
					selectedOperations.push(operation.label)
				}
			})
		})
		return {
			rbProjectIds: this.selectedFilters.rbProjectIds,
			jeeJobIds: this.selectedFilters.jobIds,
			loadIds:this.selectedFilters.loadIds,
			countryIds: this.selectedFilters.countries?.map((country) => country),
			operations: selectedOperations,
			categoryIds: this.selectedFilters.category?.map((category) => category.id),
			sectorIds: this.selectedFilters.sector?.map((sector) => sector),
			domainProductGroupIds: 		this.selectedFilters.domainProductGroup?.map((domainProductGroup) => domainProductGroup.id),
			periodicityIds: this.selectedFilters.periodicityIds ? [this.selectedFilters.periodicityIds] : [],
			periodIds: this.selectedFilters.periodIds?.map((period) => period),
			rbProjectName: this.selectedFilters.rbProjectName,
			statusIds: this.selectedFilters.status?.map((status) => status),
			projectTypeIds:this.selectedFilters.baseProjectType?.map((base) => base),
			userNames: this.selectedFilters.users?.map((users) => users.userName),
			startDate: this.selectedFilters.startDate ? this.selectedFilters.startDate : null,
			endDate: this.selectedFilters.endDate ? moment(this.selectedFilters.endDate).format().split('T')[0] + 'T23:59:59.999Z' : null

      };
	}

	getFilterByLoadIds(loadid): PublishRepublishFilterParams {
		return {
			rbProjectIds: [],
			jeeJobIds: [],
			loadIds:loadid,
			countryIds: [],
			operations: [],
			categoryIds: [],
			sectorIds: [],
			domainProductGroupIds:[],
			periodicityIds: [],
			periodIds: [],
			rbProjectName: '',
			statusIds: [],
			projectTypeIds: [],
			userNames: [],
			startDate: undefined,
			endDate: null,
		};
	}

	getSmallestCreatedRecord(filteredRecords) {
		let smallestRecord = filteredRecords[0];
		for (let i = 0; i < filteredRecords.length; i++) {
			const currentRecord = filteredRecords[i];
			if (smallestRecord === null || currentRecord.created < smallestRecord.created) {
				smallestRecord = currentRecord;
			}
		}
		return smallestRecord;
	}

	getSmallestStartedRecord(filteredRecords) {
		let smallestRecord = filteredRecords[0];
		for (let i = 0; i < filteredRecords.length; i++) {
			const currentRecord = filteredRecords[i];
			if (smallestRecord === null || currentRecord.started < smallestRecord.started) {
				smallestRecord = currentRecord;
			}
		}
		return smallestRecord;
	}

	getHighestFinishedRecord(filteredRecords) {
		let smallestRecord = filteredRecords[0];
		for (let i = 0; i < filteredRecords.length; i++) {
			const currentRecord = filteredRecords[i];
			if (smallestRecord === null || currentRecord.finished > smallestRecord.finished) {
				smallestRecord = currentRecord;
			}
		}
		return smallestRecord;
	}

	refresh(filterParams?: PublishRepublishFilterParams): void {
		this.rec = [];
		this.reccound=0;
		this.refreshedDateTime = false;
		this.isLoading = true;
		this.destroy$.next();
		this.filterTrayComponent?.showSpinner(true);

		this.rbPublishService.getAsync(filterParams || this.getFilterFormParams())
		.pipe(
			takeUntil(this.destroy$)
		)
		.subscribe((result) => {
			//Deployment time Work
			this.records = result.records;
			//staging
			this.records.forEach((x) => {
				if (
					x.serverName == '-' &&
					x.operation != 'REPUBLISH' &&
					x.operation != 'PUBLISH' &&
					x.operation != 'UNPUBLISH'  &&
					x.operation != 'REWORK'
				) {
					x['loadstatus'] = 'staging';
				} else if (
					x.serverName != '-'
				) {
					x['loadstatus'] = 'completed';
				}
			});
			this.oldrecords = this.records;
			const distinctLoadIds = [...new Set(this.records.map((x) => x.loadId))];
			distinctLoadIds.forEach((i) => {

				let jeejobs = this.records.filter((j) => j.loadId === i);
				let foundRecord = JSON.parse(JSON.stringify(this.records));
				foundRecord = foundRecord.find((j) => j.loadId === i);
				if (foundRecord) {
					if (jeejobs.find((x) => x['loadstatus'] == 'completed')?.status) {
						if (jeejobs.find((x) => x['loadstatus'] == 'completed' && x.status == 'Error')) {
							foundRecord.status = 'Error';
						} else if (
							jeejobs.find(
								(x) =>
									x['loadstatus'] == 'completed' &&
									(x.status == 'Created' || x.status == 'Waiting' || x.status == 'Scheduled' || x.status == 'Processing')
							)
						) {
							foundRecord.status = 'Processing';
						} else if (jeejobs.find((x) => x['loadstatus'] == 'completed' && x.status == 'Canceled')) {
							foundRecord.status = 'Canceled';
						} else if (jeejobs.find((x) => x['loadstatus'] == 'completed' && x.status == 'Finished')) {
							foundRecord.status = 'Finished';
						}
					} else if (jeejobs.find((x) => x['loadstatus'] == 'staging')?.status) {
						if (jeejobs.find((x) => x['loadstatus'] == 'staging' && x.status == 'Error')) {
							foundRecord.status = 'Error';
						}
						else if (
							jeejobs.find(
								(x) =>
									x['loadstatus'] == 'staging' &&
									(x.status == 'Created' ||
										x.status == 'Waiting' ||
										x.status == 'Scheduled' ||
										x.status == 'Processing' ||
										x.status == 'Finished')
							)
						) {
							foundRecord.status = 'Processing';
						}
						else if (jeejobs.find((x) => x['loadstatus'] == 'staging' && x.status == 'Canceled')) {
							foundRecord.status = 'Canceled';
						}
						else if (jeejobs.find((x) => x['loadstatus'] == 'staging' && x.status == 'Finished')) {
							foundRecord.status = 'Finished';
						}
					} else {
						this.rbPublishService.getAsync(filterParams || this.getFilterByLoadIds([i])).subscribe((result) => {

							result.records.forEach((x) => {
								if (
									x.serverName == '-' &&
									x.operation != 'REPUBLISH' &&
									x.operation != 'PUBLISH' &&
									x.operation != 'UNPUBLISH'  &&
									x.operation != 'REWORK'
								) {
									x['loadstatus'] = 'staging';
								} else if (
									x.serverName != '-'
								) {
									x['loadstatus'] = 'completed';
								}

							});

							jeejobs = result.records;

								if (jeejobs.find((x) => x['loadstatus'] == 'completed')?.status) {
									if (jeejobs.find((x) => x['loadstatus'] == 'completed' && x.status == 'Error')) {
										foundRecord.status = 'Error';
									} else if (
										jeejobs.find(
											(x) =>
												x['loadstatus'] == 'completed' &&
												(x.status == 'Created' || x.status == 'Waiting' || x.status == 'Scheduled' || x.status == 'Processing')
										)
									) {
										foundRecord.status = 'Processing';
									} else if (jeejobs.find((x) => x['loadstatus'] == 'completed' && x.status == 'Canceled')) {
										foundRecord.status = 'Canceled';
									} else if (jeejobs.find((x) => x['loadstatus'] == 'completed' && x.status == 'Finished')) {
										foundRecord.status = 'Finished';
									}
								} else if (jeejobs.find((x) => x['loadstatus'] == 'staging')?.status) {
									if (jeejobs.find((x) => x['loadstatus'] == 'staging' && x.status == 'Error')) {
										foundRecord.status = 'Error';
									}
									else if (
										jeejobs.find(
											(x) =>
												x['loadstatus'] == 'staging' &&
												(x.status == 'Created' ||
													x.status == 'Waiting' ||
													x.status == 'Scheduled' ||
													x.status == 'Processing' ||
													x.status == 'Finished')
										)
									) {
										foundRecord.status = 'Processing';
									}
									else if (jeejobs.find((x) => x['loadstatus'] == 'staging' && x.status == 'Canceled')) {
										foundRecord.status = 'Canceled';
									}
									else if (jeejobs.find((x) => x['loadstatus'] == 'staging' && x.status == 'Finished')) {
										foundRecord.status = 'Finished';
									}
								}


						})
					}
				}
				setTimeout(()=>{

				if (this.selectedFilters.status?.length > 0) {
					const statuses = this.selectedFilters.status.map((x) => x);
					const statusesName: any = [];
					statuses.forEach((status: any) => {
						this.filterTrayCacheService.statusList.forEach((cacheStatus: any) => {
							if(status == cacheStatus.value){
								statusesName.push(cacheStatus.label);
							}
						})
					});
					statusesName.forEach((i) => {
						if (foundRecord.status == i) {
							const smallestCreatedRecord = this.getSmallestCreatedRecord(jeejobs);
							const smallestStartedRecord = this.getSmallestStartedRecord(jeejobs);
							const highestFinishedRecord = this.getHighestFinishedRecord(jeejobs);

							if (foundRecord !== undefined) {
								const updatedRecord = Object.assign({}, foundRecord);
								updatedRecord.created = smallestCreatedRecord.created;
								updatedRecord.started = smallestStartedRecord.started;
								updatedRecord.finished = highestFinishedRecord.finished;
								this.rec.push(updatedRecord);
								this.reccound++;
							}
						}
					});
				} else {
					const smallestCreatedRecord = this.getSmallestCreatedRecord(jeejobs);
					const smallestStartedRecord = this.getSmallestStartedRecord(jeejobs);
					const highestFinishedRecord = this.getHighestFinishedRecord(jeejobs);

					if (foundRecord !== undefined) {
						const updatedRecord = Object.assign({}, foundRecord);
						updatedRecord.created = smallestCreatedRecord.created;
						updatedRecord.started = smallestStartedRecord.started;
						updatedRecord.finished = highestFinishedRecord.finished;

						this.rec.push(updatedRecord);
					}
				}
				},2000)
			});
			setTimeout(()=>{
				this.refreshedDateTime = true;
				if (this.selectedFilters.status?.length > 0) {
					if (this.reccound== 1000) {
						this.notify('Alert: More than 1000 Jee Job results exist that may limit Load IDs.');
					}

					if (this.reccound=== 0) {
						this.notify('No result found!');
						this.publishRepublishData = [];
					}
				}
				else{
					if (this.records.length == 1000) {
						this.notify('Alert: More than 1000 Jee Job results exist that may limit Load IDs.');
					}

					if (this.records.length === 0) {
						this.notify('No result found!');
					}
				}

				this.isLoading = false;
				this.publishRepublishData = this.rec;
				// add elements to detail menu
				this.records.forEach((element) => {
					element.detailMenuConfig = [];
					if (this.checkVisibility(element)) {
						element.detailMenuConfig.push({
							menuText: 'ShowPublishLog',
							menuIcon: 'open_in_new',
							service: 'showpublishlog',
							params: { loadid: element.loadId },
						});
					}
				});
				this.dataSource = new MatTableDataSource<PublishRepublish>(this.rec);
				this.dataSource.sort = this.sort;
				this.paginator.pageIndex = 0;
				this.dataSource.paginator = this.paginator;
				this.table.dataSource = this.dataSource;
				this.filterTrayComponent?.showSpinner(false);
			},2500)
		},
		(error) => {
			if (error.status === 504) {
				this.notify('Gateway Timeout. The server took too long to respond. Please try again later.');
			} 
			else {
				this.notify(`An error occurred: ${error.message}`);
			}
			this.isLoading = false;
			this.filterTrayComponent?.showSpinner(false);
		});

		this.SetFiltersCount();
	}

	debug(): void {
		this.refresh({
			startDate: new Date('2020-09-09T00:00:00.000Z'),
			endDate: new Date('2021-09-09T00:00:00.000Z'),
		});
	}

	checkVisibility(element: PublishRepublish) {
		const isStatusFinished = element.status.toLowerCase().trim() === 'finished';
		const isProjectTypeIdasDST = element.projectType.toLowerCase().trim() === 'idas dst';
		const isOperationPublishUnpublishComplete =
			element.operation.toLowerCase().trim().startsWith('publish complete') ||
			element.operation.toLowerCase().trim().startsWith('republish complete');
		return isStatusFinished && isProjectTypeIdasDST && isOperationPublishUnpublishComplete;
	}

	addChips(event: MatChipInputEvent, tags: number[]): void {
		const value = (event.value || '').trim();
		if (!Number(value)) {
			return;
		}

		if (value) {
			//eslint-disable-next-line radix
			tags.push(Number.parseInt(value));
		}
		event.chipInput?.clear();
	}

	removeChips(item: number, tags: number[]): void {
		const index = tags.indexOf(item);

		if (index >= 0) {
			tags.splice(index, 1);
		}
	}



	private notify(message: string) {
		this.snackBar.openFromComponent(DefaultSnackbarComponent, {
			duration: 10 * 1000,
			data: message,
		});
	}

	rowToggleSelection(ev, item) {
		item.isChecked = ev.checked;
	}

	checkAtleastOneRowChecked() {
		return this.dataSource.data.filter((i) => i.isChecked).length > 0;
	}

	openJiraTicket(): void {
		const checkedList = this.dataSource.data.filter((i) => i.isChecked);
		const publishRepublishJiraProperties: Array<publishRepublishJiraProperties> = new Array<publishRepublishJiraProperties>();

		checkedList.map(function (value) {
			publishRepublishJiraProperties.push({
				rbProjectId: value.rbProjectId,
				jobId: value.jobId,
				status: value.status,
				message: value.message,
				loadId: value.loadId,
			});
		});

		const dialogRef = this.dialog.open(CreateTicketDialogComponent, {
			width: '1000px',
			height: '487px',
			data: {
				publishRepublishJiraProperties: publishRepublishJiraProperties,
				jobType: this.jobType,
				title: 'PublishRepublish',
			},
		});

		dialogRef.afterClosed().subscribe((resSubmit) => {
			if (resSubmit === true) {
				this.dataSource.data.forEach((i) => (i.isChecked = false));
				this.selection.clear();
				this.IsSelectAll = false;
			}
		});
	}

	selectRows(selectrow) {
		this.IsSelectAll = !this.IsSelectAll;
		let endIndex: number;
		const page = this.dataSource.paginator !== null ? this.dataSource.paginator.pageSize : 0;
		const pageindex = this.dataSource.paginator !== null ? this.dataSource.paginator.pageIndex : 0;

		if (!selectrow.checked) {
			this.selection.clear();
			if (this.dataSource.data.length > (pageindex + 1) * page) {
				endIndex = (pageindex + 1) * page;
			} else {
				endIndex = this.dataSource.data.length;
			}

			for (let index = pageindex * page; index < endIndex; index++) {
				const row = this.dataSource.data[index];
				this.selection.deselect(row);
				this.dataSource.data[index].isChecked = false;
			}
		} else {
			if (this.dataSource.data.length > (pageindex + 1) * page) {
				endIndex = (pageindex + 1) * page;
			} else {
				endIndex = this.dataSource.data.length;
			}
			for (let index = pageindex * page; index < endIndex; index++) {
				const row = this.dataSource.sortData(this.dataSource.data, this.sort)[index];
				this.selection.select(row);
				this.dataSource.sortData(this.dataSource.data, this.sort)[index].isChecked = true;
			}
		}
	}

	isSelectedPage() {
		const numSelected = this.selection.selected.length;
		const page = this.dataSource.paginator !== null ? this.dataSource.paginator.pageSize : 0;
		const pageindex = this.dataSource.paginator !== null ? this.dataSource.paginator.pageIndex : 0;
		let endIndex: number;
		if (this.dataSource.data.length > (pageindex + 1) * page) {
			endIndex = (pageindex + 1) * page;
		} else {
			endIndex = this.dataSource.data.length - pageindex * page;
		}
		return numSelected === endIndex;
	}

	countryFilterSummary() {
		this.filterService.setFilters({
			countryIds: {
				name: 'Country',
				values: this.cacheObj.countries,
				slice: window.location.href.split('/')[3],
			} as Filter,
		});
	}
	sectorFilterSummary() {
		this.filterService.setFilters({
			sectorids: {
				name: 'Sector',
				values: this.cacheObj.sector,
				slice: window.location.href.split('/')[3],
			} as Filter,
		});
	}
	categoryFilterSummary() {
		this.filterService.setFilters({
			categoryIds: {
				name: 'Category',
				values: this.cacheObj.category,
				slice: window.location.href.split('/')[3],
			} as Filter,
		});
	}

	domainFilterSummary() {
		this.filterService.setFilters({
			domainProductGroupIds: {
				name: 'Domain',
				values: this.cacheObj.domainProductGroup,
				slice: window.location.href.split('/')[3],
			} as Filter,
		});
	}

	operationFilterSummary() {
		this.filterService.setFilters({
			operationIds: {
				name: 'Operation',
				values: this.cacheObj.operationIds,
				slice: window.location.href.split('/')[3],
			} as Filter,
		});
	}

	usersFilterSummary() {
		this.filterService.setFilters({
			userIds: {
				name: 'User',
				values: this.cacheObj.users?.map((i) => i.name),
				slice: window.location.href.split('/')[3],
			} as Filter,
		});
	}

	periodicityFilterSummary() {
		this.filterService.setFilters({
			periodicityIds: {
				name: 'Periodicity',
				values: this.cacheObj.periodicityIds? [{ value: this.cacheObj.periodicityIds, label: this.cacheObj.periodicityIds }] : null,
				slice: window.location.href.split('/')[3],
			} as Filter,
		});
	}

	periodFilterSummary() {
		this.filterService.setFilters({
			periodIds: {
				name: 'Period',
				values: this.cacheObj.periodIds,
				slice: window.location.href.split('/')[3],
			} as Filter,
		});
	}

	statusFilterSummary() {
		this.filterService.setFilters({
			statusIds: {
				name: 'Status',
				values: this.cacheObj.status,
				slice: window.location.href.split('/')[3],
			} as Filter,
		});
	}

	loadidsFilterSummary() {
		this.filterService.setFilters({
			loadIds: {
				name: 'LoadIDs',
				values: this.cacheObj.loadIds,
				slice: window.location.href.split('/')[3],
			} as Filter,
		});
	}

	jobidsFilterSummary() {
		this.filterService.setFilters({
			jobIds: {
				name: 'JobIDs',
				values: this.cacheObj.jobIds,
				slice: window.location.href.split('/')[3],
			} as Filter,
		});
	}

	rbIdFilterSummary() {
		this.filterService.setFilters({
			rbProjectIds: {
				name: 'RBID',
				values: this.cacheObj.rbProjectIds,
				slice: window.location.href.split('/')[3],
			} as Filter,
		});
	}

	productionProjectTypeFilterSummary() {
		this.filterService.setFilters({
			bpTypeIds: {
				name: 'bpTypeIds',
				values: this.cacheObj.baseProjectType,
				slice: window.location.href.split('/')[3],
			} as Filter,
		});
	}

	datesFilterSummary(dateStart: any, dateEnd: any) {
		if (dateStart != null || dateEnd != null) {
			const start = dateStart == 'Invalid Date' || dateStart == null ? '' : this.datepipe.transform(dateStart, 'yyyy-MM-dd');
			const end = dateEnd == 'Invalid Date' || dateEnd == null ? '' : this.datepipe.transform(dateEnd, 'yyyy-MM-dd');
			this.filterService.setFilters({
				dates: {
					name: 'Date',
					values: (start !== undefined && end !== undefined) ? [start, end] : null,
					slice: window.location.href.split('/')[3],
				} as Filter,
			});
		} else {
			this.filterService.setFilters({
				dates: {
					name: 'Date',
					values: null,
					slice: window.location.href.split('/')[3],
				} as unknown as Filter,
			});
		}
	}

	CreateFilterSummaryChips() {
		if (this.cacheObj && Object.keys(this.cacheObj).length) {
			this.countryFilterSummary();
			this.usersFilterSummary();
			this.sectorFilterSummary();
			this.categoryFilterSummary();
			this.domainFilterSummary();
			this.operationFilterSummary();
			this.periodicityFilterSummary();
			this.periodFilterSummary();
			this.statusFilterSummary();
			this.loadidsFilterSummary();
			this.jobidsFilterSummary();
			this.rbIdFilterSummary();
			this.productionProjectTypeFilterSummary();
			this.datesFilterSummary(this.cacheObj.startDate, this.cacheObj.endDate);
		}
	}

  getSelectedFilters(selectedFilters: any) {
		this.cacheObj = selectedFilters;
		this.selectedFilters = selectedFilters;
	}
}
