import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { CoverageImportsRoutingModule } from './coverage-imports-routing.module';
import { CoverageImportsComponent } from './coverage-imports.component';
import { MatTableModule } from '@angular/material/table';
import { MatIconModule } from '@angular/material/icon';
import { MatSortModule } from '@angular/material/sort';
import { MatPaginatorModule } from '@angular/material/paginator';
import { MatFormFieldModule } from '@angular/material/form-field';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatInputModule } from '@angular/material/input';
import { MatChipsModule } from '@angular/material/chips';
import { MatSelectModule } from '@angular/material/select';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { MatSnackBarModule } from '@angular/material/snack-bar';
import { MatButtonModule } from '@angular/material/button';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { SharedModule } from '@loadmonitor/shared/shared.module';
import { MatDialogModule } from '@angular/material/dialog';
import { LmxLibModule } from '@dwh/lmx-lib';
@NgModule({
  declarations: [
    CoverageImportsComponent,
  ],
  imports: [
    CommonModule,
    SharedModule,
    CoverageImportsRoutingModule,

    FormsModule,
    ReactiveFormsModule,
    LmxLibModule,
    MatTableModule,
    MatIconModule,
    MatSortModule,
    MatPaginatorModule,
    MatFormFieldModule,
    MatExpansionModule,
    MatInputModule,
    MatChipsModule,
    MatSelectModule,
    MatAutocompleteModule,
    MatSnackBarModule,
    MatButtonModule,
    MatDatepickerModule,
    MatNativeDateModule,
    MatDialogModule,
  ],
})
export class CoverageImportsModule {
}

