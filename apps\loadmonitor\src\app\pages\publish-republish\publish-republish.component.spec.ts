import { ComponentFixture, TestBed } from '@angular/core/testing';
import { PublishRepublishComponent } from './publish-republish.component';
import { MatDialog } from '@angular/material/dialog';
import { PublishRepublishService } from '@loadmonitor/shared/services/publish-republish.service';
import { FormBuilder } from '@angular/forms';
import { MatSnackBar } from '@angular/material/snack-bar';
import { HelperService } from '@loadmonitor/shared/services/helper.service';
import { FilterService } from '@dwh/lmx-lib';
import { BehaviorSubject } from 'rxjs';
import { DatePipe } from '@angular/common';
import { FilterTrayCacheService } from '@loadmonitor/shared/services/filter-tray-cache.service';

describe('PublishRepublishComponent', () => {
  let component: PublishRepublishComponent;
  let fixture: ComponentFixture<PublishRepublishComponent>;
  let mockPublishRepublishService: jest.Mocked<PublishRepublishService>;
  let mockSnackBar: jest.Mocked<MatSnackBar>;
  let mockHelperService: jest.Mocked<HelperService>;
  let mockFilterService: jest.Mocked<FilterService>;
  let mockFilterTrayCacheService: jest.Mocked<FilterTrayCacheService>;

  beforeEach(async () => {

  await TestBed.configureTestingModule({
      declarations: [PublishRepublishComponent],
      providers: [
        { provide: MatDialog, useValue: {} },
        { provide: PublishRepublishService, useValue: mockPublishRepublishService },
        { provide: MatSnackBar, useValue: mockSnackBar },
        { provide: FormBuilder, useValue: new FormBuilder() },
        { provide: HelperService, useValue: mockHelperService },
        { provide: FilterService, useValue: mockFilterService },
        { provide: DatePipe, useValue: new DatePipe('en-US') },
        { provide: FilterTrayCacheService, useValue: mockFilterTrayCacheService },
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(PublishRepublishComponent);
    component = fixture.componentInstance;
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  // Additional test cases for more functionalities
});
