<lm-navigation></lm-navigation>

<div class="each-slice-top-headbar" [ngClass]="FilterTrayOpenFlag ? 'filter-tray-open' : 'filter-tray-close'">
	<h3 class="alignleft">Csv Import</h3>
	<div class="flexdiv-buttons">
		<button testId="lmx-button" gfk-button type="primary" class="jira-button-margin-right btn-secondary gfk-btn" (click)="openJiraTicket()" [disabled]="!checkAtleastOneRowChecked()">
			Create JIRA Ticket
		</button>
		<lmx-filter-button showFilterCount="{{ filterCount }}" (isShownFilterTray)="isShownFilterTrayEventHandler($event)"></lmx-filter-button>
	</div>
</div>

<lmx-filter-tray
	(ButtonShowHideEvent)="this.getWidth()"
	(clickApplyButton)="ConfirmApply(true)"
	(clickSaveButton)="openmodal()"
	[IsEnabled]="IsEnabled"
	[IsDisabled]="IsDisabled"
>
<lm-filter-tray-form
	    [sliceName]="'CSVImport'"
		[resetForm]="resetForm"
		(selectedFilters)="getSelectedFilters($event)"
		(fileTypeData)="getFileTypeList($event)"
    [isPeriodHidden]="isPeriodFilterHidden"
	>
	</lm-filter-tray-form>
</lmx-filter-tray>

<div class="filter-area" [ngClass]="FilterTrayOpenFlag ? 'filter-tray-open' : 'filter-tray-close'">
	<div class="toggle-button">
		<div>
			<lm-quick-filter
				[selectedfilter]="defaultoption"
				class="p-3.5"
				[events]="eventsSubject.asObservable()"
				*ngIf="IsEnabled"
				[modalshow]="modalshow"
				(saveInQuickFilter)="saveInQuickFilter(cacheObj.isAutoRefresh, $event)"
				(applyquickfilter)="applyfilter($event)"
				(disabledbutton)="disablesave($event)">
			</lm-quick-filter>
			<lm-filter-summary (resetFilters)="filterSummaryChipClear($event)"></lm-filter-summary>
		</div>
		<div class="refreshedTime">
			<lm-refreshed-date-time *ngIf="refreshedDateTime" class="pr-3 d-inline-block"></lm-refreshed-date-time>
			<gfk-toggle-button
				*ngIf="EnableRefresh && IsEnabled"
				[checked]="cacheObj.isAutoRefresh || false"
				title="Auto Refresh"
				class="autoRefresh d-inline-block"
				[disabled]="defaultoption === 'Default'"
				(onChange)="toggleChecked($event)">
			</gfk-toggle-button>
		</div>
	</div>
</div>

<article class="table-grid">
	<div class="mat-elevation-z8 tab-container" [ngClass]="(csvImportsData?.length) ? 'table-height' : ''">
		<table mat-table [dataSource]="dataSource" matSort class="full-width-table js-tbl-csv-imports-result" multiTemplateDataRows>
			<ng-container matColumnDef="details">
				<th mat-header-cell *matHeaderCellDef></th>
				<td mat-cell *matCellDef="let element">
					<mat-icon (click)="element.isExpanded = element.isExpanded; element.isExpanded = !element.isExpanded" *ngIf="element.isExpanded"
						>keyboard_arrow_down</mat-icon
					>
					<mat-icon (click)="element.isExpanded = !element.isExpanded; element.isExpanded = element.isExpanded" *ngIf="!element.isExpanded"
						>chevron_right</mat-icon
					>
				</td>
			</ng-container>

			<ng-container matColumnDef="menu">
				<th mat-header-cell *matHeaderCellDef class="selectAllPad">
					<mat-checkbox (change)="selectRows($event)" [checked]="this.IsSelectAll" [indeterminate]="this.IsSelectAll"></mat-checkbox>
				</th>
				<td mat-cell *matCellDef="let element">
					<div class="flexdiv">
						<mat-checkbox class="matcheckbox" [checked]="element.isChecked" (change)="rowToggleSelection($event, element)"></mat-checkbox>
						<lm-detail-menu [configuration]="[]"></lm-detail-menu>
					</div>
				</td>
			</ng-container>

			<ng-container matColumnDef="id">
				<th mat-header-cell *matHeaderCellDef mat-sort-header>ID</th>
				<td mat-cell *matCellDef="let element" class="js-result-csv-imports-id">{{ element.id }}</td>
			</ng-container>

			<ng-container matColumnDef="unitType">
				<th mat-header-cell *matHeaderCellDef mat-sort-header>Unit Type</th>
				<td mat-cell *matCellDef="let element" class="js-result-unit-type">{{ element.unitType }}</td>
			</ng-container>

			<ng-container matColumnDef="status">
				<th mat-header-cell *matHeaderCellDef mat-sort-header>Status</th>
				<td mat-cell *matCellDef="let element" class="js-result-csv-imports-status">{{ element.status }}</td>
			</ng-container>

			<ng-container matColumnDef="fileName">
				<th mat-header-cell *matHeaderCellDef mat-sort-header>FileName</th>
				<td mat-cell *matCellDef="let element" class="js-result-csv-imports-fileName">{{ element.fileName }}</td>
			</ng-container>

			<ng-container matColumnDef="fileType">
				<th mat-header-cell *matHeaderCellDef mat-sort-header>FileType</th>
				<td mat-cell *matCellDef="let element" class="js-result-csv-imports-fileName">{{ element?.fileType?.toUpperCase() }}</td>
			</ng-container>

			<ng-container matColumnDef="project">
				<th mat-header-cell *matHeaderCellDef mat-sort-header>Base Project</th>
				<td mat-cell *matCellDef="let element">{{ element.project }}</td>
			</ng-container>

			<ng-container matColumnDef="productGroup">
				<th mat-header-cell *matHeaderCellDef mat-sort-header>Product Group</th>
				<td mat-cell *matCellDef="let element" class="js-result-product-group">{{ element.productGroup }}</td>
			</ng-container>

			<ng-container matColumnDef="productGroupId">
				<th mat-header-cell *matHeaderCellDef mat-sort-header>Product Group ID</th>
				<td mat-cell *matCellDef="let element">
					{{ element.productGroupId }}
				</td>
			</ng-container>

			<ng-container matColumnDef="period">
				<th mat-header-cell *matHeaderCellDef mat-sort-header>Period</th>
				<td mat-cell *matCellDef="let element">{{ element.period }}</td>
			</ng-container>

			<ng-container matColumnDef="createdBy">
				<th mat-header-cell *matHeaderCellDef mat-sort-header>Created By</th>
				<td mat-cell *matCellDef="let element" class="js-result-created-by">{{ element.createdBy }}</td>
			</ng-container>

			<ng-container matColumnDef="created">
				<th mat-header-cell *matHeaderCellDef mat-sort-header>Created On</th>
				<td mat-cell *matCellDef="let element" class="js-result-created">
					{{ element.created | date: date_with_time }}
				</td>
			</ng-container>

			<ng-container matColumnDef="started">
				<th mat-header-cell *matHeaderCellDef mat-sort-header>Started On</th>
				<td mat-cell *matCellDef="let element" class="js-result-started">
					{{ element.started | date: date_with_time }}
				</td>
			</ng-container>

			<ng-container matColumnDef="finished">
				<th mat-header-cell *matHeaderCellDef mat-sort-header>Finished On</th>
				<td mat-cell *matCellDef="let element" class="js-result-finished">
					{{ element.finished | date: date_with_time }}
				</td>
			</ng-container>

			<ng-container matColumnDef="runtime">
				<th mat-header-cell *matHeaderCellDef mat-sort-header>Runtime</th>
				<td mat-cell *matCellDef="let element">
					{{ element.runtime }}
				</td>
			</ng-container>

			<ng-container matColumnDef="jeeJobId">
				<th mat-header-cell *matHeaderCellDef mat-sort-header>JEE Job Id</th>
				<td mat-cell *matCellDef="let element" class="js-result-jee-job-id">{{ element.jeeJobId }}</td>
			</ng-container>

			<ng-container matColumnDef="jeeJobInfo">
				<th mat-header-cell *matHeaderCellDef>JEE Job Info</th>
				<td mat-cell *matCellDef="let element">
					<lm-jee-job-info jobId="{{ element.jeeJobId }}"></lm-jee-job-info>
				</td>
			</ng-container>

			<ng-container matColumnDef="message">
				<th mat-header-cell *matHeaderCellDef mat-sort-header>message</th>
				<td mat-cell *matCellDef="let element" class="js-result-message">{{ element.message }}</td>
			</ng-container>

			<ng-container matColumnDef="expandedDetail">
				<!-- <td mat-cell *matCellDef="let row" [attr.colspan]="displayedColumns.length">
          <div class="expanded-element-detail" [@detailExpand]="row.isSelected ? 'expanded' : 'collapsed'">
            <lm-floating-panel *ngIf='row.isSelected'></lm-floating-panel>
          </div>
        </td> -->
				<td mat-cell *matCellDef="let element" [attr.colspan]="displayedColumns.length">
					<div class="expanded-element-detail" [@detailExpand]="element.isExpanded ? 'expanded' : 'collapsed'">
						<lm-floating-panel *ngIf="element.isExpanded"></lm-floating-panel>
					</div>
				</td>
			</ng-container>

			<tr mat-header-row *matHeaderRowDef="displayedColumns; sticky: true" class="columnHeader"></tr>
			<tr
				mat-row
				*matRowDef="let element; columns: displayedColumns"
				[class.selected-row-background]="element.isSelected"
				[class.expanded-row]="expandedElement === element"
				(click)="selectRow(element); expandedElement = expandedElement === element ? null : element"
			></tr>

			<tr mat-row *matRowDef="let row; columns: ['expandedDetail']" class="expanded-detail-row"></tr>
		</table>
	</div>
	<mat-paginator [pageSizeOptions]="[10, 20, 50, 100]" (page)="clickNextButton()" showFirstLastButtons></mat-paginator>
</article>
<lm-version></lm-version>