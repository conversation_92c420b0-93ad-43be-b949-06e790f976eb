import { DatePipe } from '@angular/common';
import { TestBed } from '@angular/core/testing';

import { HelperService } from './helper.service';

describe('HelperService', () => {
  let service: HelperService;

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [DatePipe]
    });
    service = TestBed.inject(HelperService);
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  // I don't want to test createLink Method because it will be removed soon,
  // and I was not able to read from clipboard, so I decided the effort
  // is higher than the value

  describe('calculateDuration() [METHOD]', () => {
    let returnValue: string | null;
    let startDate: Date;
    let finishDate: Date;

    beforeEach(() => {
      jest.spyOn(service, 'calculateDuration');
    })

    it('should exist', () => {
      expect(service.calculateDuration).toBeTruthy();
    })

    it('should return did not start', () => {
      returnValue = service.calculateDuration('', '', true);

      expect(returnValue).toBe('Did not start');
    });

    it('should return did not finish', () => {
      returnValue = service.calculateDuration(new Date(), '', true);

      expect(returnValue).toBe('Did not finish');
    });

    it('should return more than 24h and parameters are of type date', () => {
      startDate = new Date(2022, 1, 1, 0, 0);
      finishDate = new Date(2022, 1, 5, 0, 0);

      returnValue = service.calculateDuration(startDate, finishDate, true);

      expect(returnValue).toBe('More than 24h');
    });

    it('should return more than 24 and parameters are of type string', () => {
      let startDateString: any = '2022-01-01 12:00:00';
      let finishDateString = '2022-01-02 12:00:00';

      returnValue = service.calculateDuration(startDateString, finishDateString, true);

      expect(returnValue).toBe('More than 24h');
    });

    it('should return date and time for 1d 12h', () => {
      startDate = new Date(2022, 1, 1, 0, 0);
      finishDate = new Date(2022, 1, 2, 12, 0);

      returnValue = service.calculateDuration(startDate, finishDate, false);

      expect(returnValue).toBe('1d 12:00:00');
    });

    it('should return runtime 15h', () => {
      startDate = new Date(2022, 1, 1, 0, 0);
      finishDate = new Date(2022, 1, 1, 15, 0);

      returnValue = service.calculateDuration(startDate, finishDate, false);

      expect(returnValue).toBe('15:00:00');
    });

  });
});
