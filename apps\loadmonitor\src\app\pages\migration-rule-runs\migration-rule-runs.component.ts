import { Component, OnInit, ViewChild, AfterContentInit, Input, HostListener, OnD<PERSON>roy } from '@angular/core';
import { MatTable, MatTableDataSource } from '@angular/material/table';
import { MatSort } from '@angular/material/sort';
import { MatPaginator } from '@angular/material/paginator';
import { MigrationRuleRuns } from '@loadmonitor/shared/interfaces/MigrationRuleRuns';
import { JobTypes } from '@loadmonitor/shared/jobTypes';
import { COMMA, ENTER } from '@angular/cdk/keycodes';
import { FormBuilder, FormGroup } from '@angular/forms';
import { SettingsConstants } from '@loadmonitor/shared/settings.constants';
import { MigrationRuleRunsService } from '@loadmonitor/shared/services/migration-rule-runs.service';
import { MatSnackBar } from '@angular/material/snack-bar';
import { DefaultSnackbarComponent } from '@loadmonitor/shared/components/default-snackbar/default-snackbar.component';
import { animate, state, style, transition, trigger } from '@angular/animations';
import { PropertyPage } from '@loadmonitor/shared/interfaces/PropertyPage';
import { HelperService } from '@loadmonitor/shared/services/helper.service';
import { MigrationRuleRunCache } from '@loadmonitor/shared/interfaces/caching/MigrationRuleRunCache';
import { BehaviorSubject, Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { environment } from 'apps/loadmonitor/src/environments/environment';
import { Filter, FilterRecords, FilterService, FilterTrayComponent } from '@dwh/lmx-lib';
import { ToggleButtonComponent } from '@gfk/ng-lib';
import { DatePipe } from '@angular/common';
import { UserService } from '@loadmonitor/shared/services/user.service';
import { StatusService } from '@loadmonitor/shared/services/status.service';
import { StatusSelectService } from '@loadmonitor/shared/services/status-select.service';
import * as moment from 'moment';
import { FilterTrayCacheService } from '@loadmonitor/shared/services/filter-tray-cache.service';

@Component({
	selector: 'lm-migration-rule-runs',
	templateUrl: './migration-rule-runs.component.html',
	styleUrls: ['./migration-rule-runs.component.scss'],
	animations: [
		trigger('detailExpand', [
			state('collapsed', style({ height: '0px', minHeight: '0' })),
			state('expanded', style({ height: '*' })),
			transition('expanded <=> collapsed', animate('225ms cubic-bezier(0.4, 0.0, 0.2, 1)')),
		]),
	],
})
export class MigrationRuleRunsComponent implements OnInit, AfterContentInit, OnDestroy {

	@Input() icon = 'delete';

	@ViewChild(FilterTrayComponent) filterTrayComponent: FilterTrayComponent | undefined;

	@ViewChild(ToggleButtonComponent) togglebuttonComponent: ToggleButtonComponent | undefined;

	@ViewChild(MatSort)
	sort: MatSort = new MatSort();

	@ViewChild(MatTable)
	table!: MatTable<MigrationRuleRuns>;

	@ViewChild(MatPaginator)
	paginator!: MatPaginator;

	// Progress Spinner visibility
	isLoading = false;
	public MigrationRuleRunsForm!: FormGroup;
	isPeriodFilterHidden = false;
	public cacheObj: MigrationRuleRunCache = {} as MigrationRuleRunCache;
	cacheName = 'cache::MigrationRuleRun';
	//Mat-Expansion-Panel
	panelOpenState = false;

	jobType: JobTypes = JobTypes.migrationRuleRuns;
	scopeRefersTo!: string;
	selectedRow: any;

	// Property Pages
	expandedElement!: PropertyPage | null;
	migrationRuleRunDetail!: PropertyPage[];

	readonly separatorKeysCodes = [ENTER, COMMA] as const;

	//Mat-Table
	dataSource = new MatTableDataSource<MigrationRuleRuns>();
	displayedColumns: string[] = [
		'details',
		'menu',
		'runId',
		'status',
		'ruleSetName',
		'ruleSetId',
		'scopeType',
		'sourceComponentId',
		'targetComponentId',
		'createdBy',
		'created',
		'started',
		'finished',
		'message',
	];

	readonly date_with_time = SettingsConstants.DATE_FORMAT_WITH_TIME;

	/* *********************
	 * Components variables
	 ********************* */
	eventsSubject: Subject<void> = new Subject<void>();
	IsDisabled = false;
	environments = environment.environments;
	IsEnabled = false;
	FilterTrayOpenFlag = true;
	currentfilter = JSON.parse(localStorage.getItem('QuickFilter') || '[]').filter(
		(x) => x.user == localStorage.getItem('username') && x.slice == window.location.href.split('/')[3]
	);
	modalshow = false;
	filterCount = '';
	toggleInterval!: any;
	timer = 60;
	EnableRefresh = true;
	cacheInterval!: any;
	defaultoption = localStorage.getItem(this.cacheName + '-LastFilter') || 'Default';
	defaultfilter = JSON.parse(localStorage.getItem(this.cacheName + '-DefaultFilter') || '[]');
	refreshedDateTime!: boolean;

  selectedFilters: any;
	cacheValue: BehaviorSubject<any> = new BehaviorSubject<any>({});
	resetForm: Subject<boolean> = new Subject();
	migrationRuleRunsData!: any[];

  @HostListener('document:visibilitychange', ['$event'])
	appVisibility() {
		if (document.hidden) {
			if (this.cacheInterval) {
				clearInterval(this.cacheInterval);
			}
			if (this.toggleInterval) {
				clearInterval(this.toggleInterval);
			}
		} else {
			if (this.cacheObj.isAutoRefresh === true) {
				this.cacheInterval = setInterval(() => {
					if (this.defaultoption != 'Default') {
						this.refresh();
					}
				}, this.timer * 1000);
			} else {
				if (this.cacheInterval !== undefined) clearInterval(this.cacheInterval);
				if (this.toggleInterval !== undefined) clearInterval(this.toggleInterval);
			}
		}
	}
	private destroy$ = new Subject<void>();

	constructor(
		private migrationRuleRunsService: MigrationRuleRunsService,
		private snackBar: MatSnackBar,
		private formBuilder: FormBuilder,
		private helperService: HelperService,
		public filterService: FilterService,
		public datepipe: DatePipe,
		private filterTrayCacheService: FilterTrayCacheService
	) {}

	private updateFC(): void {
		if(this.cacheObj){
			this.filterTrayCacheService.getCacheValueData(this.cacheObj);
		}
  	}


	ngOnInit(): void {
		if (this.environments.filter((x) => x.location == window.location.origin)[0]) {
			this.IsEnabled = this.environments.filter((x) => x.location == window.location.origin)[0].enabled;
		} else {
			this.IsEnabled = false;
		}
		this.MigrationRuleRunsForm = this.formBuilder.group({
			isAutoRefresh: this.togglebuttonComponent?.checked,
		});
		this.updateFC();
	}

	ngAfterContentInit() {
		this.afterChildComponentsLoaded();
		this.CreateFilterSummaryChips();
		this.SetFiltersCount();
		this.updateFC();
		if (this.cacheObj.isAutoRefresh === true) {
			if (this.cacheInterval === undefined) {
				this.cacheInterval = window.setInterval(() => {
					if (this.defaultoption != 'Default') {
						this.refresh();
					}
				}, this.timer * 1000);
			}
		} else {
			if (this.cacheInterval !== undefined) clearInterval(this.cacheInterval);
			if (this.toggleInterval !== undefined) clearInterval(this.toggleInterval);
		}
	}
	afterChildComponentsLoaded() {
		this.cacheObj = JSON.parse(localStorage.getItem(this.cacheName) || '{}');
	}

	//Quick Filter Methods//
	openmodal() {
		this.eventsSubject.next();
	}

	disablesave(status) {
		this.IsDisabled = status;
	}


	setFormValue(autoRefreshStatus) {
		this.MigrationRuleRunsForm.setValue({
			isAutoRefresh: autoRefreshStatus === undefined || autoRefreshStatus === false ? false : true,
		});
	}

	saveInQuickFilter(autoRefreshStatus, filterName) {
		this.setFormValue(autoRefreshStatus);
		this.currentfilter = JSON.parse(localStorage.getItem('QuickFilter') || '[]');
		this.currentfilter.push({
			data: this.selectedFilters,
			filter: filterName,
			slice: window.location.href.split('/')[3],
			user: localStorage.getItem('username'),
		});
		this.defaultoption = filterName;
		const oldcache = this.cacheObj;
		const olddefault = localStorage.getItem(this.cacheName + '-DefaultFilter');
		this.ConfirmApply(autoRefreshStatus);
		this.cacheObj = oldcache;
		localStorage.setItem(this.cacheName + '-DefaultFilter', olddefault || '[]');
		localStorage.setItem('QuickFilter', JSON.stringify(this.currentfilter));
		localStorage.setItem(this.cacheName + '-LastFilter', filterName);
	}

	applyfilter(selectedvalue) {
		this.resetForm.next(true);
		const oldautotoggle = this.cacheObj.isAutoRefresh;
		selectedvalue = selectedvalue.value ? selectedvalue.value : selectedvalue;
		this.defaultoption = selectedvalue;
		localStorage.setItem(this.cacheName + '-LastFilter', this.defaultoption || 'Default');
		if (selectedvalue == 'Default') {
			if (
				JSON.parse(localStorage.getItem(this.cacheName + '-DefaultFilter') || '[]').filter(
					(x) => x.user == localStorage.getItem('username') && x.slice == window.location.href.split('/')[3]
				)[0]
			) {
				this.cacheObj = JSON.parse(localStorage.getItem(this.cacheName + '-DefaultFilter') || '[]').filter(
					(x) => x.user == localStorage.getItem('username') && x.slice == window.location.href.split('/')[3]
				)[0]['data'];
			}

			localStorage.setItem(this.cacheName, JSON.stringify(this.cacheObj));
		} else {
			this.cacheObj = JSON.parse(localStorage.getItem('QuickFilter') || '[]')
				.filter((x) => x.user == localStorage.getItem('username') && x.slice == window.location.href.split('/')[3])
				.filter((x) => x.filter == selectedvalue)[0]['data'];
			localStorage.setItem(this.cacheName, JSON.stringify(this.cacheObj));
		}
		this.cacheObj.isAutoRefresh = oldautotoggle;
		this.CreateFilterSummaryChips();
		this.selectedFilters = this.cacheObj;
    if (this.cacheObj.selectedDays == '3') {
			const date = new Date();
			this.cacheObj.startDate = new Date(Date.UTC(date.getFullYear(), date.getMonth(), date.getDate() - 2, 0, 0, 0, 0));
			this.cacheObj.endDate = new Date(Date.UTC(date.getFullYear(), date.getMonth(), date.getDate(), 0, 0, 0, 0));
		} else if (this.cacheObj.selectedDays == '2') {
			const date = new Date();
			this.cacheObj.startDate = new Date(Date.UTC(date.getFullYear(), date.getMonth(), date.getDate(), 0, 0, 0, 0));
			this.cacheObj.endDate = new Date(Date.UTC(date.getFullYear(), date.getMonth(), date.getDate(), 0, 0, 0, 0));
		}
		setTimeout(() => {
			this.updateFC();
			this.refresh();
		}, 200);
		localStorage.setItem(this.cacheName, JSON.stringify(this.cacheObj));
	}

	CreateFilterSummaryChips() {
		if (this.cacheObj && Object.keys(this.cacheObj).length) {
			this.usersFilterSummary();
			this.statusFilterSummary();
			this.scopeContainsFilterSummary();
			this.scopeTypeFilterSummary();
			this.ruleSetFilterSummary();
			this.ruleSetIDFilterSummary();
			this.runIDFilterSummary();
			this.srcComponentFilterSummary();
			this.targetComponentFilterSummary();
			this.datesFilterSummary(this.cacheObj.startDate, this.cacheObj.endDate);
		}
	}

	ConfirmApply(autoRefreshStatus) {
		this.saveInCache(autoRefreshStatus, false);
		this.CreateFilterSummaryChips();

		let count;
		this.filterService.filtersSelected$.subscribe((selectedFilters: FilterRecords) => {
			count = Number(Object.keys(selectedFilters).length);
		});

		if (count > 0) {
			this.refresh();
		} else {
			if (confirm('Loading results without any filters selected will take some time to load. Are you sure you want to proceed?')) {
				this.refresh();
			}
		}
	}

	public isShownFilterTrayEventHandler($event: boolean) {
		this.filterTrayComponent?.toggleShowTray($event);
		this.FilterTrayOpenFlag = $event;
	}

	getWidth() {
		this.FilterTrayOpenFlag = !this.FilterTrayOpenFlag;
		return '100%';
	}

	filterSummaryChipClear(filteredItems) {
		if (filteredItems == 'ALL') {
			if (confirm('This will remove all of your selected filters and could not be reversed. Are you sure you want to do that?')) {
				this.resetForm.next(true);
				this.filterTrayCacheService.getCacheNameData(this.cacheName);
				this.filterCount = '';
			}
		}
		this.saveInCache(this.cacheObj.isAutoRefresh, false);
		this.CreateFilterSummaryChips();
		this.SetFiltersCount();
	}

	toggleChecked(check: any) {
		if (check === true) {
			this.saveInCache(check, true);
			this.toggleInterval = setInterval(() => {
				if (this.defaultoption != 'Default') {
					this.refresh();
				}
			}, this.timer * 1000);
		} else {
			if (this.toggleInterval !== undefined) clearInterval(this.toggleInterval);
			if (this.cacheInterval !== undefined) clearInterval(this.cacheInterval);
			this.saveInCache(check, true);
		}
	}

	SetFiltersCount() {
		let count;
		this.filterService.filtersSelected$.subscribe((selectedFilters: FilterRecords) => {
			count = Number(Object.keys(selectedFilters).length);
		});
		if (count > 0) {
			this.filterCount = '(' + count.toString() + ')';
		} else {
			this.filterCount = '';
		}
	}

	ngOnDestroy() {
		if (this.cacheInterval) {
			clearInterval(this.cacheInterval);
		}

		if (this.toggleInterval) {
			clearInterval(this.toggleInterval);
		}
		this.destroy$.next();
    	this.destroy$.complete();
	}
	//Quick Filter Methods End//

	saveInCache(autoRefreshStatus, AutoToggle) {
		this.MigrationRuleRunsForm.setValue({
			isAutoRefresh: autoRefreshStatus === undefined || autoRefreshStatus === false ? false : true,
		});

		localStorage.setItem(this.cacheName, JSON.stringify(this.selectedFilters));

		this.cacheObj = JSON.parse(localStorage.getItem(this.cacheName) || '{}');
		if(this.cacheObj.ruleSetName == null){
			this.cacheObj.ruleSetName = '';
		}
		if(this.cacheObj.scope == null){
			this.cacheObj.scope = '';
		}
		if (!AutoToggle) {
			this.defaultfilter = [];
			this.defaultfilter.push({
				data: this.selectedFilters,
				slice: window.location.href.split('/')[3],
				user: localStorage.getItem('username'),
			});
			localStorage.setItem(this.cacheName + '-DefaultFilter', JSON.stringify(this.defaultfilter));
			let nofilter = 0;
			let selectedfilter = '';
			this.currentfilter.forEach((i) => {
				// if (i.filter == this.defaultoption) {
				if(JSON.stringify(i.data) == JSON.stringify(this.cacheObj)){
					nofilter++;
					selectedfilter = i.filter;
				}
			});
			console.log(nofilter)
			if (nofilter == 0) {
				this.defaultoption = 'Default';
			} else {
				this.defaultoption = selectedfilter;
			}
		}
		localStorage.setItem(this.cacheName + '-LastFilter', this.defaultoption);
		this.updateFC();
	}

	refresh(): void {
		this.refreshedDateTime = false;
		this.isLoading = true;
		this.destroy$.next();
		this.filterTrayComponent?.showSpinner(true);
		this.migrationRuleRunsService
			.getAsync(
		        this.selectedFilters.startDate ? this.selectedFilters.startDate : null,
				this.selectedFilters.endDate ? moment(this.selectedFilters.endDate).format().split('T')[0] + 'T23:59:59.999Z' : null,
				this.selectedFilters.users?.map((users) => users.userName),
				this.selectedFilters.status?.map((status) => status),
				this.selectedFilters.ruleSetName,
				this.selectedFilters.scopeTypeId ? (this.selectedFilters.scopeTypeId == '1' ? 0 : -1) : null,
				this.selectedFilters.scope,
				this.selectedFilters.sourceComponentIds,
				this.selectedFilters.targetComponentIds,
				this.selectedFilters.runIds,
				this.selectedFilters.ruleSetIds,
			)
			.pipe(
				takeUntil(this.destroy$)
			)
			.subscribe((result) => {
				this.refreshedDateTime = true;
				if (result.moreRecordsAvailable) {
					this.notify('More than 1000 results exist!');
				}

				if (result.count === 0) {
					this.notify('No result found!');
					this.migrationRuleRunsData = [];
				}
				this.isLoading = false;
				this.migrationRuleRunsData = result.records;
				// add elements to detail menu
				result.records.forEach((element) => {
					element.detailMenuConfig = [
						{
							menuText: 'Edit Migration Rule',
							menuIcon: 'open_in_new',
							service: 'editmigrationrule',
							params: { rulesetid: element.ruleSetId },
						},
					];
				});

				this.dataSource = new MatTableDataSource<MigrationRuleRuns>(result.records);
				this.dataSource.sort = this.sort;
				this.paginator.pageIndex = 0;
				this.dataSource.paginator = this.paginator;
				this.table.dataSource = this.dataSource;
				this.filterTrayComponent?.showSpinner(false);
			},
			(error) => {
				if (error.status === 504) {
					this.notify('Gateway Timeout. The server took too long to respond. Please try again later.');
				} 
				else {
					this.notify(`An error occurred: ${error.message}`);
				}
				this.isLoading = false;
				this.filterTrayComponent?.showSpinner(false);
			});
		this.SetFiltersCount();
	}

	public translateScopeId(scopeId: number): string {
		if (scopeId === 0) {
			return 'Reporting Groups / LOBs';
		} else {
			return 'Reporting projects';
		}
	}

	/**
	 * JUST FOR DEBUG! send a request with a preset data and fill the explorer data source
	 */
	debugSubmit(): void {
		// if (this.history === undefined) {
		// 	this.history = {} as HistoryFilterComponent;
		// }
		// this.history.startDate = new Date('05/09/2018');
		// this.history.endDate = new Date('06/09/2018');

		this.refresh();
	}

	private notify(message: string) {
		this.snackBar.openFromComponent(DefaultSnackbarComponent, {
			duration: 10 * 1000,
			data: message,
		});
	}

	selectRow(row: MigrationRuleRuns) {
		this.migrationRuleRunDetail = [];
		row.isSelected = true;

		this.dataSource.data.forEach((element) => {
			if (row.runId != element.runId) {
				element.isSelected = false;
				element.isExpanded = false;
			}
		});

		this.selectedRow = row;
		this.migrationRuleRunsService.getDetailAsync(row.runId).subscribe((result) => {
			this.helperService.floatingPanelData$.next({ data: result, jobType: JobTypes.migrationRuleRuns });
		});
	}

	clickNextButton() {
		this.selectedRow.isSelected = false;
	}

	usersFilterSummary() {
		this.filterService.setFilters({
			userIds: {
				name: 'User',
				values: this.cacheObj.users?.map((i) => i.name),
				slice: window.location.href.split('/')[3],
			} as Filter,
		});
	}

	statusFilterSummary() {
		this.filterService.setFilters({
			statusIds: {
				name: 'Status',
				values: this.cacheObj.status,
				slice: window.location.href.split('/')[3],
			} as Filter,
		});
	}

	scopeTypeFilterSummary() {
		this.filterService.setFilters({
			scopeTypeId: {
				name: 'ScopeType',
				values: this.cacheObj.scopeTypeId != null ? [{ value: this.cacheObj.scopeTypeId, label: this.cacheObj.scopeTypeId }] : null,
				slice: window.location.href.split('/')[3],
			} as unknown as Filter,
		});
	}

	scopeContainsFilterSummary() {
		this.filterService.setFilters({
			scope: {
				name: 'ScopeContains',
				values: this.cacheObj.scope,
				slice: window.location.href.split('/')[3],
			} as unknown as Filter,
		});
	}

	ruleSetFilterSummary() {
		this.filterService.setFilters({
			ruleSetName: {
				name: 'RuleSet',
				values: this.cacheObj.ruleSetName,
				slice: window.location.href.split('/')[3],
			} as unknown as Filter,
		});
	}

	runIDFilterSummary() {
		this.filterService.setFilters({
			runIds: {
				name: 'RunIDs',
				values: this.cacheObj.runIds,
				slice: window.location.href.split('/')[3],
			} as Filter,
		});
	}

	ruleSetIDFilterSummary() {
		this.filterService.setFilters({
			ruleSetIds: {
				name: 'RuleSetID',
				values: this.cacheObj.ruleSetIds,
				slice: window.location.href.split('/')[3],
			} as Filter,
		});
	}

	srcComponentFilterSummary() {
		this.filterService.setFilters({
			sourceComponentIds: {
				name: 'SrcComponentID',
				values: this.cacheObj.sourceComponentIds,
				slice: window.location.href.split('/')[3],
			} as Filter,
		});
	}

	targetComponentFilterSummary() {
		this.filterService.setFilters({
			targetComponentIds: {
				name: 'targetComponentID',
				values: this.cacheObj.targetComponentIds,
				slice: window.location.href.split('/')[3],
			} as Filter,
		});
	}

	datesFilterSummary(dateStart: any, dateEnd: any) {
		if (dateStart != null || dateEnd != null) {
			const start = dateStart == 'Invalid Date' || dateStart == null ? '' : this.datepipe.transform(dateStart, 'yyyy-MM-dd');
			const end = dateEnd == 'Invalid Date' || dateEnd == null ? '' : this.datepipe.transform(dateEnd, 'yyyy-MM-dd');
			this.filterService.setFilters({
				dates: {
					name: 'Date',
					values: (start !== undefined && end !== undefined) ? [start, end] : null,
					slice: window.location.href.split('/')[3],
				} as Filter,
			});
		} else {
			this.filterService.setFilters({
				dates: {
					name: 'Date',
					values: null,
					slice: window.location.href.split('/')[3],
				} as unknown as Filter,
			});
		}
	}

  getSelectedFilters(selectedFilters: any) {
		this.cacheObj = selectedFilters;
		this.selectedFilters = selectedFilters;
	}
}
