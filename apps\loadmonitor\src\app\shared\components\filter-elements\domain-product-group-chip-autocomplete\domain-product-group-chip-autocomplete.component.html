<mat-form-field class="chip-list" appearance="outline">
  <mat-label>Domain Product Group </mat-label>
  <mat-chip-list class="chip-list-wrapper" #chipListDomainProductGroup aria-label="Domain Product Group selection">
    <mat-chip *ngFor="let item of domainProductGroupIds | async" [selectable]="true" [removable]="true"
      (removed)="removeDomainProductGroupChips(item)">
      {{ item.name }} ({{ item.id }})
      <mat-icon matChipRemove>cancel</mat-icon>
    </mat-chip>
    <input placeholder="New ..." #domainProductGroupIdsInput [formControl]="domainProductGroupIdsCtrl"
      [matAutocomplete]="autoDomainPG" [matChipInputFor]="chipListDomainProductGroup"
      [matChipInputSeparatorKeyCodes]="separatorKeysCodes" class="js-filter-inp-domain-product-group" />
  </mat-chip-list>
  <mat-autocomplete #autoDomainPG="matAutocomplete"
    [autoActiveFirstOption]="true"  (optionSelected)="selectedDomainProductGroupChips($event)">
    <mat-option *ngFor="let item of filteredDomainProductGroupIds | async" [value]="item">
      <div (click)="optionClicked($event, item)">
        <mat-checkbox class="option-checkbox" [checked]="item.selected" (change)="toggleSelection(item)"
          (click)="$event.stopPropagation()">

          {{ item.name }} ({{ item.id }})
        </mat-checkbox>
      </div>
    </mat-option>
  </mat-autocomplete>
</mat-form-field>
