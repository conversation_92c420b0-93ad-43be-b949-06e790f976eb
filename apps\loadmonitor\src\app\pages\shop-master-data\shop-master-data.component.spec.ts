import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ShopMasterDataComponent } from './shop-master-data.component';
import { MatSnackBar } from '@angular/material/snack-bar';
import { FormBuilder } from '@angular/forms';
import { ShopMasterDataService } from '@loadmonitor/shared/services/shop-master-data.service';
import { FilterService } from '@dwh/lmx-lib/src';
import { BehaviorSubject } from 'rxjs';
import { NO_ERRORS_SCHEMA } from '@angular/core';
import { DatePipe } from '@angular/common';
import { ToggleButtonComponent } from '@gfk/ng-lib';
import { FilterTrayComponent } from '@dwh/lmx-lib/src/lib/components/filter-tray/filter-tray.component';

describe('ShopMasterDataComponent', () => {
  let component: ShopMasterDataComponent;
  let fixture: ComponentFixture<ShopMasterDataComponent>;

  const mockSnackBar = {
    openFromComponent: jest.fn(),
  };

  const mockShopMasterDataService = {
    getAsync: jest.fn(),
    getDetailAsync: jest.fn(),
  };

  const mockFilterService = {
    filtersSelected$: new BehaviorSubject({}),
    setFilters: jest.fn(),
  };

  const mockToggleButtonComponent = {
    checked: false,
  };

  const mockFilterTrayComponent = {
    showSpinner: jest.fn(),
    toggleShowTray: jest.fn(),
  };

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ShopMasterDataComponent, ToggleButtonComponent, FilterTrayComponent],
      providers: [
        FormBuilder,
        { provide: MatSnackBar, useValue: mockSnackBar },
        { provide: ShopMasterDataService, useValue: mockShopMasterDataService },
        { provide: FilterService, useValue: mockFilterService },
        DatePipe,
      ],
      schemas: [NO_ERRORS_SCHEMA],
    }).compileComponents();

    fixture = TestBed.createComponent(ShopMasterDataComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

});
