<form [formGroup]="filterFG" class="eds-input">
	<!-- BCRReworks -->
	<ng-template [ngIf]="sliceName === 'BCRReworks'">

		<div class="mb-5">
			<eds-select
				formControlName="countries"
				[required]="false"
				[options]="countriesList"
				[placeholder]="'Country'"
				[value]="filterFG.get('countries')?.value"
				[edsId]="'country'"
				[attr.data-test-id]="'country'"
				[multiple]="true">
			</eds-select>
		</div>

		<div class="mb-4">
			<eds-select
				formControlName="sector"
				[required]="false"
				[options]="sectorsList"
				[placeholder]="'Sector'"
				[value]="filterFG.get('sector')?.value"
				[edsId]="'sector'"
				[attr.data-test-id]="'sector'"
				[multiple]="true">
			</eds-select>
		</div>

		<lmx-chip-autocomplete 
			class="filters-align" 
			formControlName="category" 
			[options]="category$ | async"
			[placeholder]="'Category'"
			[label]="'Category'">
		</lmx-chip-autocomplete>

		<lmx-chip-autocomplete
			class="filters-align"
			formControlName="domainProductGroup"
			[options]="domainProductGroup$ | async"
			[placeholder]="'Domain Product Group'"
			[label]="'Domain Product Group'">
		</lmx-chip-autocomplete>

		<div class="mb-5">
			<eds-select
				formControlName="channelIds"
				[required]="false"
				[options]="channelList"
				[placeholder]="'Source Channel'"
				[value]="filterFG.get('channelIds')?.value"
				[edsId]="'channel'"
				[attr.data-test-id]="'channel'"
				[multiple]="true">
			</eds-select>
		</div>

		<div class="mb-3">
			<eds-select
				formControlName="baseChannelIds"
				[required]="false"
				[options]="baseChannelList"
				[placeholder]="'Target/ Virtual Base Channel'"
				[value]="filterFG.get('baseChannelIds')?.value"
				[edsId]="'targetVirtualBaseChannel'"
				[attr.data-test-id]="'targetVirtualBaseChannel'"
				[multiple]="true">
			</eds-select>
		</div>

		<lmx-chip-autocomplete
			class="filters-align"
			(val)="userkey($event)"
			formControlName="users"
			[options]="users$ | async"
			[id]="'user-input'"
			[placeholder]="'Users'"
			(resetUserList)="userkey('')"
			[label]="'Users'">
		</lmx-chip-autocomplete>

		<div class="mb-3">
			<eds-select
				formControlName="status"
				[required]="false"
				[options]="statusList"
				[placeholder]="'Status'"
				[value]="filterFG.get('status')?.value"
				[edsId]="'status'"
				[attr.data-test-id]="'status'"
				[multiple]="true">
			</eds-select>
		</div>

		<lmx-chip-numeric [placeholder]="'JEE Job IDs'" [id]="'jeeJobIds-input'" formControlName="jobIds"></lmx-chip-numeric>

		<lmx-chip-numeric [placeholder]="'Load IDs'" [id]="'loadIds-input'" formControlName="loadIds"></lmx-chip-numeric>

		<lmx-chip-numeric [placeholder]="'BCR Rearrange Ids'" [id]="'bcrRearrangeIds-input'" formControlName="bcRearrangedIds"></lmx-chip-numeric>

		<div class="mb-6">
			<input
				formControlName="feature"
				gfk-input
				type="text"
				name="feature"
				[placeholder]="'Feature'"
				[value]="filterFG.get('feature')?.value"
				[id]="'feature-input'"
				[testId]="'feature-input'" />
		</div>

		<lmx-date-range-autocomplete
			class="filters-align"
			[selected]="cacheObj.selectedDays || '1'"
			[startDate]="cacheObj.startDate"
			[endDate]="cacheObj.endDate"
			(selectedDate)="selectedDateFilter($event)">
		</lmx-date-range-autocomplete>
	</ng-template>

	<!-- FeatureMove -->
	<ng-template [ngIf]="sliceName === 'FeatureMove'">
		<lmx-date-range-autocomplete
			class="filters-align"
			[selected]="cacheObj.selectedDays"
			[startDate]="cacheObj.startDate"
			[endDate]="cacheObj.endDate"
			(selectedDate)="selectedDateFilter($event)">
		</lmx-date-range-autocomplete>

		<lmx-chip-autocomplete
			class="filters-align"
			(val)="userkey($event)"
			formControlName="users"
			[options]="users$ | async"
			[id]="'user-input'"
			[placeholder]="'Users'"
			(resetUserList)="userkey('')"
			[label]="'Users'">
		</lmx-chip-autocomplete>

		<div class="mb-5">
			<eds-select
				formControlName="status"
				[required]="false"
				[options]="statusList"
				[placeholder]="'Status'"
				[value]="filterFG.get('status')?.value"
				[edsId]="'status'"
				[attr.data-test-id]="'status'"
				[multiple]="true">
			</eds-select>
		</div>

		<lmx-chip-numeric [placeholder]="'Load IDs'" [id]="'loadIds-input'" formControlName="loadIds"></lmx-chip-numeric>

		<lmx-chip-numeric [placeholder]="'JEE Job IDs'" [id]="'jeeJobIds-input'" formControlName="jobIds"></lmx-chip-numeric>
	</ng-template>

	<!-- CoverageImports -->
	<ng-template [ngIf]="sliceName === 'CoverageImports'">
		<lmx-chip-autocomplete
			class="filters-align"
			(val)="userkey($event)"
			formControlName="users"
			[options]="users$ | async"
			[id]="'user-input'"
			[placeholder]="'Users'"
			(resetUserList)="userkey('')"
			[label]="'Users'">
		</lmx-chip-autocomplete>

		<lmx-chip-numeric [placeholder]="'JEE Job IDs'" [id]="'jeeJobIds-input'" formControlName="jobIds"></lmx-chip-numeric>

		<lmx-chip-numeric [placeholder]="'Export IDs'" [id]="'exportIds-input'" formControlName="exportIds"></lmx-chip-numeric>

		<div class="mb-4">
			<eds-select
				formControlName="status"
				[required]="false"
				[options]="statusList"
				[placeholder]="'Status'"
				[value]="filterFG.get('status')?.value"
				[edsId]="'status'"
				[attr.data-test-id]="'status'"
				[multiple]="true">
			</eds-select>
		</div>

		<div class="mb-5">
			<eds-select
				formControlName="workspaceIds"
				[required]="false"
				[options]="workSpaceList"
				[placeholder]="'Workspace'"
				[value]="filterFG.get('workspaceIds')?.value"
				[edsId]="'workSpace'"
				[attr.data-test-id]="'workSpace'"
				[multiple]="true">
			</eds-select>
		</div>

		<lmx-date-range-autocomplete
			class="filters-align"
			[selected]="cacheObj.selectedDays || '1'"
			[startDate]="cacheObj.startDate"
			[endDate]="cacheObj.endDate"
			(selectedDate)="selectedDateFilter($event)">
		</lmx-date-range-autocomplete>
	</ng-template>

	<!-- CSVImport -->
	<ng-template [ngIf]="sliceName === 'CSVImport'">
		<lmx-date-range-autocomplete
			class="filters-align"
			[selected]="cacheObj.selectedDays || '1'"
			[startDate]="cacheObj.startDate"
			[endDate]="cacheObj.endDate"
			(selectedDate)="selectedDateFilter($event)">
		</lmx-date-range-autocomplete>

		<lmx-chip-autocomplete 
			class="filters-align" 
			(val)="userkey($event)" 
			formControlName="users" 
			[options]="users$ | async" 
			[id]="'user-input'"
			[placeholder]="'Users'"
			(resetUserList)="userkey('')"
			[label]="'Users'">
		</lmx-chip-autocomplete>
		
		<div class="mb-5">
			<eds-select
				formControlName="status"
				[required]="false"
				[options]="statusList"
				[placeholder]="'Status'"
				[value]="filterFG.get('status')?.value"
				[edsId]="'status'"
				[attr.data-test-id]="'status'"
				[multiple]="true">
			</eds-select>
		</div>
		
		<div class="mb-5">
			<eds-select
				formControlName="countries"
				[required]="false"
				[options]="countriesList"
				[placeholder]="'Country'"
				[value]="filterFG.get('countries')?.value"
				[edsId]="'country'"
				[attr.data-test-id]="'country'"
				[multiple]="true">
			</eds-select>
		</div>
		
		<div class="mb-5">
			<eds-select
				formControlName="sector"
				[required]="false"
				[options]="sectorsList"
				[placeholder]="'Sector'"
				[value]="filterFG.get('sector')?.value"
				[edsId]="'sector'"
				[attr.data-test-id]="'sector'"
				[multiple]="true">
			</eds-select>
		</div>

		<lmx-chip-autocomplete 
			class="filters-align" 
			formControlName="category" 
			[options]="category$ | async"
			[placeholder]="'Category'"
			[label]="'Category'">
		</lmx-chip-autocomplete>

		<lmx-chip-autocomplete
			class="filters-align"
			formControlName="domainProductGroup"
			[options]="domainProductGroup$ | async"
			[placeholder]="'Domain Product Group'"
			[label]="'Domain Product Group'">
		</lmx-chip-autocomplete>

		<div class="mb-5">
			<eds-select
				formControlName="periodicityIds"
				[required]="false"
				[options]="periodicityList"
				[placeholder]="'Periodicity'"
				[value]="filterFG.get('periodicityIds')?.value"
				[edsId]="'periodicity'"
				[attr.data-test-id]="'periodicity'"
				[multiple]="false">
			</eds-select>
		</div>

		<div class="mb-5">
			<eds-select
				formControlName="periodIds"
				[required]="false"
				[options]="periodList"
				[placeholder]="'Period'"
				[value]="filterFG.get('periodIds')?.value"
				[edsId]="'period'"
				[attr.data-test-id]="'period'"
				[multiple]="true">
			</eds-select>
		</div>

		<lmx-chip-numeric [placeholder]="'Base Project IDs'" [id]="'baseProjectIds-input'" formControlName="baseProjectIds"></lmx-chip-numeric>

		<div class="mb-6">
			<input
				formControlName="baseProjectName"
				gfk-input
				type="text"
				name="baseProjectName"
				[placeholder]="'Base Project Name'"
				[value]="filterFG.get('baseProjectName')?.value"
				[id]="'baseProjectName-input'"
				[testId]="'baseProjectName-input'" />
		</div>

		<div class="mb-5">
			<eds-select
				formControlName="unitType"
				[required]="false"
				[options]="unitTypeList"
				[placeholder]="'Unit Type'"
				[value]="filterFG.get('unitType')?.value"
				[edsId]="'unitType'"
				[attr.data-test-id]="'unitType'"
				[multiple]="true">
			</eds-select>
		</div>

		<lmx-chip-numeric [placeholder]="'JEE Job IDs'" [id]="'jeeJobIds-input'" formControlName="jobIds"></lmx-chip-numeric>

		<div class="mb-5">
			<eds-select
				formControlName="fileType"
				[required]="false"
				[options]="fileTypeList"
				[placeholder]="'File Type'"
				[value]="filterFG.get('fileType')?.value"
				[edsId]="'fileType'"
				[attr.data-test-id]="'fileType'"
				[multiple]="true">
			</eds-select>
		</div>
	</ng-template>

	<!-- DataOrder -->
	<ng-template [ngIf]="sliceName === 'DataOrder'">
		<lmx-date-range-autocomplete
			[selected]="cacheObj.selectedDays || '1'"
			[startDate]="cacheObj.startDate"
			[endDate]="cacheObj.endDate"
			(selectedDate)="selectedDateFilter($event)">
		</lmx-date-range-autocomplete>

		<lmx-chip-autocomplete
			class="filters-align"
			(val)="userkey($event)"
			formControlName="users"
			[options]="users$ | async"
			[id]="'user-input'"
			[placeholder]="'Users'"
			(resetUserList)="userkey('')"
			[label]="'Users'">
		</lmx-chip-autocomplete>

		<div class="mb-5">
			<eds-select
				formControlName="status"
				[required]="false"
				[options]="statusList"
				[placeholder]="'Status'"
				[value]="filterFG.get('status')?.value"
				[edsId]="'status'"
				[attr.data-test-id]="'status'"
				[multiple]="true">
			</eds-select>
		</div>
		
		<div class="mb-5">
			<eds-select
				formControlName="countries"
				[required]="false"
				[options]="countriesList"
				[placeholder]="'Country'"
				[value]="filterFG.get('countries')?.value"
				[edsId]="'country'"
				[attr.data-test-id]="'country'"
				[multiple]="true">
			</eds-select>
		</div>

		<lmx-chip-numeric [placeholder]="'Production Project IDs'" [id]="'productionProjectIds-input'" formControlName="productionProjectIds"></lmx-chip-numeric>

		<div class="mb-5">
			<eds-select
				formControlName="periodicityIds"
				[required]="false"
				[options]="periodicityList"
				[placeholder]="'Periodicity'"
				[value]="filterFG.get('periodicityIds')?.value"
				[edsId]="'periodicity'"
				[attr.data-test-id]="'periodicity'"
				[multiple]="false">
			</eds-select>
		</div>

		<div class="mb-5">
			<eds-select
				formControlName="periodIds"
				[required]="false"
				[options]="periodList"
				[placeholder]="'Period'"
				[value]="filterFG.get('periodIds')?.value"
				[edsId]="'period'"
				[attr.data-test-id]="'period'"
				[multiple]="true">
			</eds-select>
		</div>

		<div class="mb-5">
			<eds-select
				formControlName="productionProjectType"
				[required]="false"
				[options]="productionProjectTypeList"
				[placeholder]="'Production Project Type'"
				[value]="filterFG.get('productionProjectType')?.value"
				[edsId]="'productionProjectType'"
				[attr.data-test-id]="'productionProjectType'"
				[multiple]="true">
			</eds-select>
		</div>

		<div class="mb-6">
			<input
				formControlName="productionProjectName"
				gfk-input
				type="text"
				name="productionProjectName"
				[placeholder]="'Production Project Name'"
				[value]="filterFG.get('productionProjectName')?.value"
				[id]="'productionProjectName-input'"
				[testId]="'productionProjectName-input'" />
		</div>

		<lmx-chip-numeric [placeholder]="'Data Order IDs'" [id]="'dataOrderIds-input'" formControlName="dataOrderIds"></lmx-chip-numeric>
	</ng-template>

  <!-- DWHQC -->
	<ng-template [ngIf]="sliceName === 'DWHQC'">
		<lmx-date-range-autocomplete
			class="filters-align"
			[selected]="cacheObj.selectedDays || '1'"
			[startDate]="cacheObj.startDate"
			[endDate]="cacheObj.endDate"
			(selectedDate)="selectedDateFilter($event)">
		</lmx-date-range-autocomplete>

		<lmx-chip-autocomplete
			class="filters-align"
			(val)="userkey($event)"
			formControlName="users"
			[options]="users$ | async"
			[id]="'user-input'"
			[placeholder]="'Users'"
			(resetUserList)="userkey('')"
			[label]="'Users'">
		</lmx-chip-autocomplete>

		<div class="mb-5">
			<eds-select
				formControlName="countries"
				[required]="false"
				[options]="countriesList"
				[placeholder]="'Country'"
				[value]="filterFG.get('countries')?.value"
				[edsId]="'country'"
				[attr.data-test-id]="'country'"
				[multiple]="true">
			</eds-select>
		</div>

		<lmx-chip-numeric [placeholder]="'Base Project IDs'" [id]="'baseProjectIds-input'" formControlName="baseProjectIds"></lmx-chip-numeric>

		<lmx-chip-numeric [placeholder]="'QC Project IDs'" [id]="'qcProjectIds-input'" formControlName="qcProjectIds"> </lmx-chip-numeric>

		<div class="mb-5">
			<eds-select
				formControlName="sector"
				[required]="false"
				[options]="sectorsList"
				[placeholder]="'Sector'"
				[value]="filterFG.get('sector')?.value"
				[edsId]="'sector'"
				[attr.data-test-id]="'sector'"
				[multiple]="true">
			</eds-select>
		</div>

		<lmx-chip-autocomplete 
			class="filters-align" 
			formControlName="category" 
			[options]="category$ | async"
			[placeholder]="'Category'"
			[label]="'Category'">
		</lmx-chip-autocomplete>

		<lmx-chip-autocomplete
			class="filters-align"
			formControlName="domainProductGroup"
			[options]="domainProductGroup$ | async"
			[placeholder]="'Domain Product Group'"
			[label]="'Domain Product Group'">
		</lmx-chip-autocomplete>

		<div class="mb-5">
			<eds-select
				formControlName="periodicityIds"
				[required]="false"
				[options]="periodicityList"
				[placeholder]="'Periodicity'"
				[value]="filterFG.get('periodicityIds')?.value"
				[edsId]="'periodicity'"
				[attr.data-test-id]="'periodicity'"
				[multiple]="false">
			</eds-select>
		</div>

		<div class="mb-5">
			<eds-select
				formControlName="periodIds"
				[required]="false"
				[options]="periodList"
				[placeholder]="'Period'"
				[value]="filterFG.get('periodIds')?.value"
				[edsId]="'period'"
				[attr.data-test-id]="'period'"
				[multiple]="true">
			</eds-select>
		</div>

		<div class="mb-5">
			<input
				formControlName="baseProjectName"
				gfk-input
				type="text"
				name="baseProjectName"
				[placeholder]="'Base Project Name'"
				[value]="filterFG.get('baseProjectName')?.value"
				[id]="'baseProjectName-input'"
				[testId]="'baseProjectName-input'" />
		</div>

		<div class="mb-5">
			<eds-select
				formControlName="baseProjectType"
				[required]="false"
				[options]="baseProjectTypeList"
				[placeholder]="'Baseproject Type'"
				[value]="filterFG.get('baseProjectType')?.value"
				[edsId]="'baseProjectType'"
				[attr.data-test-id]="'baseProjectType'"
				[multiple]="true">
			</eds-select>
		</div>

		<div class="mb-5">
			<eds-select
				formControlName="qcProjectflag"
				[required]="false"
				[options]="withOutQCProject"
				[placeholder]="'Without QC Project'"
				[value]="filterFG.get('qcProjectflag')?.value"
				[edsId]="'withoutQcProject'"
				[attr.data-test-id]="'withoutQcProject'"
				[multiple]="false">
			</eds-select>
		</div>
	</ng-template>

  <!-- DWHRelease -->
	<ng-template [ngIf]="sliceName === 'DWHRelease'">
		<lmx-date-range-autocomplete
			class="filters-align"
			[selected]="cacheObj.selectedDays || '1'"
			[startDate]="cacheObj.startDate"
			[endDate]="cacheObj.endDate"
			(selectedDate)="selectedDateFilter($event)">
		</lmx-date-range-autocomplete>

		<lmx-chip-autocomplete 
			class="filters-align" 
			(val)="userkey($event)" 
			formControlName="users" 
			[options]="users$ | async" 
			[id]="'user-input'"
			[placeholder]="'Users'"
			(resetUserList)="userkey('')"
			[label]="'Users'">
		</lmx-chip-autocomplete>

		<div class="mb-5">
			<eds-select
				formControlName="status"
				[required]="false"
				[options]="statusList"
				[placeholder]="'Status'"
				[value]="filterFG.get('status')?.value"
				[edsId]="'status'"
				[attr.data-test-id]="'status'"
				[multiple]="true">
			</eds-select>
		</div>
		
		<div class="mb-5">
			<eds-select
				formControlName="countries"
				[required]="false"
				[options]="countriesList"
				[placeholder]="'Country'"
				[value]="filterFG.get('countries')?.value"
				[edsId]="'country'"
				[attr.data-test-id]="'country'"
				[multiple]="true">
			</eds-select>
		</div>
		
		<div class="mb-5">
			<eds-select
				formControlName="sector"
				[required]="false"
				[options]="sectorsList"
				[placeholder]="'Sector'"
				[value]="filterFG.get('sector')?.value"
				[edsId]="'sector'"
				[attr.data-test-id]="'sector'"
				[multiple]="true">
			</eds-select>
		</div>

		<lmx-chip-autocomplete 
			class="filters-align" 
			formControlName="category" 
			[options]="category$ | async"
			[placeholder]="'Category'"
			[label]="'Category'">
		</lmx-chip-autocomplete>

		<lmx-chip-autocomplete
			class="filters-align"
			formControlName="domainProductGroup"
			[options]="domainProductGroup$ | async"
			[placeholder]="'Domain Product Group'"
			[label]="'Domain Product Group'">
		</lmx-chip-autocomplete>

		<div class="mb-5">
			<eds-select
				formControlName="periodicityIds"
				[required]="false"
				[options]="periodicityList"
				[placeholder]="'Periodicity'"
				[value]="filterFG.get('periodicityIds')?.value"
				[edsId]="'periodicity'"
				[attr.data-test-id]="'periodicity'"
				[multiple]="false">
			</eds-select>
		</div>

		<div class="mb-5">
			<eds-select
				formControlName="periodIds"
				[required]="false"
				[options]="periodList"
				[placeholder]="'Period'"
				[value]="filterFG.get('periodIds')?.value"
				[edsId]="'period'"
				[attr.data-test-id]="'period'"
				[multiple]="true">
			</eds-select>
		</div>

		<lmx-chip-numeric [placeholder]="'Load IDs'" [id]="'loadIds-input'" formControlName="loadIds"></lmx-chip-numeric>

		<lmx-chip-numeric [placeholder]="'Base Project IDs'" [id]="'baseProjectIds-input'" formControlName="baseProjectIds"></lmx-chip-numeric>

		<lmx-chip-numeric [placeholder]="'QC Project IDs'" [id]="'qcProjectIds-input'" formControlName="qcProjectIds"></lmx-chip-numeric>

		<div class="mb-5">
			<eds-select
				formControlName="jobType"
				[required]="false"
				[options]="jobType"
				[placeholder]="'Job Type'"
				[value]="filterFG.get('jobType')?.value"
				[edsId]="'jobType'"
				[attr.data-test-id]="'jobType'"
				[multiple]="true">
			</eds-select>
		</div>

		<div class="mb-5">
			<eds-select
				formControlName="baseProjectType"
				[required]="false"
				[options]="baseProjectTypeList"
				[placeholder]="'Baseproject Type'"
				[value]="filterFG.get('baseProjectType')?.value"
				[edsId]="'baseProjectType'"
				[attr.data-test-id]="'baseProjectType'"
				[multiple]="true">
			</eds-select>
		</div>

		<div class="mb-6">
			<input
				formControlName="qcProjectName"
				gfk-input
				type="text"
				name="qcProjectName"
				[placeholder]="'QC Project Name'"
				[value]="filterFG.get('qcProjectName')?.value"
				[id]="'qcProjectNames-input'"
				[testId]="'qcProjectNames-input'" />
		</div>

		<lmx-chip-numeric [placeholder]="'JEE Job IDs'" [id]="'jeeJobIds-input'" formControlName="jobIds"></lmx-chip-numeric>
	</ng-template>

	<!-- Exports -->
	<ng-template [ngIf]="sliceName === 'Exports'">
		<lmx-date-range-autocomplete
			class="filters-align"
			[selected]="cacheObj.selectedDays || '1'"
			[startDate]="cacheObj.startDate"
			[endDate]="cacheObj.endDate"
			(selectedDate)="selectedDateFilter($event)">
		</lmx-date-range-autocomplete>

		<lmx-chip-autocomplete
			class="filters-align"
			(val)="userkey($event)"
			formControlName="users"
			[options]="users$ | async"
			[id]="'user-input'"
			[placeholder]="'Users'"
			(resetUserList)="userkey('')"
			[label]="'Users'">
		</lmx-chip-autocomplete>

		<div class="mb-5">
			<eds-select
				formControlName="status"
				[required]="false"
				[options]="statusExportsList"
				[placeholder]="'Status'"
				[value]="filterFG.get('status')?.value"
				[edsId]="'status'"
				[attr.data-test-id]="'status'"
				[multiple]="true">
			</eds-select>
		</div>

		<lmx-chip-numeric [placeholder]="'JEE Job IDs'" [id]="'jeeJobIds-input'" formControlName="jobIds"> </lmx-chip-numeric>

		<lmx-chip-numeric [placeholder]="'Reporting Project IDs'" [id]="'reportingProjectIds-input'" formControlName="reportingProjectIds"> </lmx-chip-numeric>

		<div class="mb-5">
			<eds-select
				formControlName="countries"
				[required]="false"
				[options]="countriesList"
				[placeholder]="'Country'"
				[value]="filterFG.get('countries')?.value"
				[edsId]="'country'"
				[attr.data-test-id]="'country'"
				[multiple]="true">
			</eds-select>
		</div>

    	<lmx-chip-numeric [placeholder]="'Export IDs'" [id]="'exportIds-input'" formControlName="exportIds"></lmx-chip-numeric>

		<div class="mb-6">
			<input
				formControlName="exportName"
				gfk-input
				type="text"
				name="exportName"
				[placeholder]="'Export Name'"
				[value]="filterFG.get('exportName')?.value"
				[id]="'exportName-input'"
				[testId]="'exportName-input'" />
		</div>

		<div class="mb-6">
			<input
				formControlName="reportingProjectName"
				gfk-input
				type="text"
				name="reportingProjectName"
				[placeholder]="'Reporting Project Name'"
				[value]="filterFG.get('reportingProjectName')?.value"
				[id]="'reportProjectName-input'"
				[testId]="'reportProjectName-input'" />
		</div>

		<div class="mb-5">
			<eds-select
				formControlName="exportFormatIds"
				[required]="false"
				[options]="exportFormatList"
				[placeholder]="'Export Formats'"
				[value]="filterFG.get('exportFormatIds')?.value"
				[edsId]="'exportFormat'"
				[attr.data-test-id]="'exportFormat'"
				[multiple]="true">
			</eds-select>
		</div>

		<lmx-chip-numeric [placeholder]="'Reporting Group IDs'" [id]="'reportingGroupIds-input'" formControlName="reportingGroupIds"></lmx-chip-numeric>

		<lmx-chip-numeric [placeholder]="'CDM Delivery IDs'" [id]="'cdmDeliveryIds-input'" formControlName="cdmDeliveryIds"></lmx-chip-numeric>
	</ng-template>

	<!-- ItemMasterData -->
	<ng-template [ngIf]="sliceName === 'ItemMasterData'">
		<lmx-date-range-autocomplete
			class="filters-align"
			[selected]="cacheObj.selectedDays || '1'"
			[startDate]="cacheObj.startDate"
			[endDate]="cacheObj.endDate"
			(selectedDate)="selectedDateFilter($event)">
		</lmx-date-range-autocomplete>

		<lmx-chip-autocomplete 
			class="filters-align" 
			(val)="userkey($event)" 
			formControlName="users" 
			[options]="users$ | async" 
			[id]="'user-input'"
			[placeholder]="'Users'"
			(resetUserList)="userkey('')"
			[label]="'Users'">
		</lmx-chip-autocomplete>

		<div class="mb-5">
			<eds-select
				formControlName="status"
				[required]="false"
				[options]="statusList"
				[placeholder]="'Status'"
				[value]="filterFG.get('status')?.value"
				[edsId]="'status'"
				[attr.data-test-id]="'status'"
				[multiple]="true">
			</eds-select>
		</div>

		<div class="mb-5">
			<eds-select
				formControlName="countries"
				[required]="false"
				[options]="countriesList"
				[placeholder]="'Country'"
				[value]="filterFG.get('countries')?.value"
				[edsId]="'country'"
				[attr.data-test-id]="'country'"
				[multiple]="true">
			</eds-select>
		</div>
		
		<div class="mb-5">
			<eds-select
				formControlName="sector"
				[required]="false"
				[options]="sectorsList"
				[placeholder]="'Sector'"
				[value]="filterFG.get('sector')?.value"
				[edsId]="'sector'"
				[attr.data-test-id]="'sector'"
				[multiple]="true">
			</eds-select>
		</div>

		<lmx-chip-autocomplete 
			class="filters-align" 
			formControlName="category" 
			[options]="category$ | async"
			[placeholder]="'Category'"
			[label]="'Category'">
		</lmx-chip-autocomplete>

		<lmx-chip-autocomplete
			class="filters-align"
			formControlName="domainProductGroup"
			[options]="domainProductGroup$ | async"
			[placeholder]="'Domain Product Group'"
			[label]="'Domain Product Group'">
		</lmx-chip-autocomplete>

		<lmx-chip-numeric [placeholder]="'JEE Job IDs'" [id]="'jeeJobIds-input'" formControlName="jobIds"> </lmx-chip-numeric>

		<div class="mb-5">
			<eds-select
				formControlName="jobType"
				[required]="false"
				[options]="jobTypeList"
				[placeholder]="'Job Type'"
				[value]="filterFG.get('jobType')?.value"
				[edsId]="'jobType'"
				[attr.data-test-id]="'jobType'"
				[multiple]="true">
			</eds-select>
		</div>

		<div class="mb-5">
			<eds-select
				formControlName="domainProductGroupType"
				[required]="false"
				[options]="domainProductGroupType"
				[placeholder]="'Domain Product Group Type'"
				[value]="filterFG.get('domainProductGroupType')?.value"
				[edsId]="'domainProductGroupType'"
				[attr.data-test-id]="'domainProductGroupType'"
				[multiple]="false">
			</eds-select>
		</div>

		<lmx-chip-numeric [placeholder]="'Load Unit IDs'" [id]="'loadUnitIds-input'" formControlName="loadUnitIds"> </lmx-chip-numeric>

		<lmx-chip-numeric [placeholder]="'Prep Unit IDs'" [id]="'prepUnitIds-input'" formControlName="prepUnitIds"> </lmx-chip-numeric>
	</ng-template>

	<!--  LoggingQC -->
	<ng-template [ngIf]="sliceName === 'LoggingQC'">
		<lmx-date-range-autocomplete
			class="filters-align"
			[selected]="cacheObj.selectedDays || '1'"
			[startDate]="cacheObj.startDate"
			[endDate]="cacheObj.endDate"
			(selectedDate)="selectedDateFilter($event)">
		</lmx-date-range-autocomplete>

		<lmx-chip-autocomplete
			class="filters-align"
			(val)="userkey($event)"
			formControlName="users"
			[options]="users$ | async"
			[id]="'user-input'"
			[placeholder]="'Users'"
			(resetUserList)="userkey('')"
			[label]="'Users'">
		</lmx-chip-autocomplete>

		<div class="mb-5">
			<eds-select
				formControlName="status"
				[required]="false"
				[options]="statusList"
				[placeholder]="'Status'"
				[value]="filterFG.get('status')?.value"
				[edsId]="'status'"
				[attr.data-test-id]="'status'"
				[multiple]="true">
			</eds-select>
		</div>

		<lmx-chip-numeric [placeholder]="'JEE Job IDs'" [id]="'jeeJobIds-input'" formControlName="jobIds"></lmx-chip-numeric>

		<lmx-chip-numeric [placeholder]="'Export IDs'" [id]="'exportIds-input'" formControlName="exportIds"></lmx-chip-numeric>
	</ng-template>

	<!-- ShopMasterData -->
	<ng-template [ngIf]="sliceName === 'ShopMasterData'">
		<lmx-date-range-autocomplete
			class="filters-align"
			[selected]="cacheObj.selectedDays || '1'"
			[startDate]="cacheObj.startDate"
			[endDate]="cacheObj.endDate"
			(selectedDate)="selectedDateFilter($event)">
		</lmx-date-range-autocomplete>

		<lmx-chip-autocomplete
			class="filters-align"
			(val)="userkey($event)"
			formControlName="users"
			[options]="users$ | async"
			[id]="'user-input'"
			[placeholder]="'Users'"
			(resetUserList)="userkey('')"
			[label]="'Users'">
		</lmx-chip-autocomplete>

		<div class="mb-5">
			<eds-select
				formControlName="status"
				[required]="false"
				[options]="statusList"
				[placeholder]="'Status'"
				[value]="filterFG.get('status')?.value"
				[edsId]="'status'"
				[attr.data-test-id]="'status'"
				[multiple]="true">
			</eds-select>
		</div>

		<div class="mb-5">
			<eds-select
				formControlName="countries"
				[required]="false"
				[options]="countriesList"
				[placeholder]="'Country'"
				[value]="filterFG.get('countries')?.value"
				[edsId]="'country'"
				[attr.data-test-id]="'country'"
				[multiple]="true">
			</eds-select>
		</div>
		
		<div class="mb-5">
			<eds-select
				formControlName="sector"
				[required]="false"
				[options]="sectorsList"
				[placeholder]="'Sector'"
				[value]="filterFG.get('sector')?.value"
				[edsId]="'sector'"
				[attr.data-test-id]="'sector'"
				[multiple]="true">
			</eds-select>
		</div>

		<lmx-chip-autocomplete 
			class="filters-align" 
			formControlName="category" 
			[options]="category$ | async"
			[placeholder]="'Category'"
			[label]="'Category'">
		</lmx-chip-autocomplete>

		<lmx-chip-autocomplete
			class="filters-align"
			formControlName="domainProductGroup"
			[options]="domainProductGroup$ | async"
			[placeholder]="'Domain Product Group'"
			[label]="'Domain Product Group'">
		</lmx-chip-autocomplete>

		<div class="mb-5">
			<eds-select
				formControlName="periodicityIds"
				[required]="false"
				[options]="periodicityList"
				[placeholder]="'Periodicity'"
				[value]="filterFG.get('periodicityIds')?.value"
				[edsId]="'periodicity'"
				[attr.data-test-id]="'periodicity'"
				[multiple]="false">
			</eds-select>
		</div>

		<div class="mb-5">
			<eds-select
				formControlName="periodIds"
				[required]="false"
				[options]="periodList"
				[placeholder]="'Period'"
				[value]="filterFG.get('periodIds')?.value"
				[edsId]="'period'"
				[attr.data-test-id]="'period'"
				[multiple]="true">
			</eds-select>
		</div>

		<div class="mb-5">
			<eds-select
				formControlName="channelIds"
				[required]="false"
				[options]="channelList"
				[placeholder]="'Source Channel'"
				[value]="filterFG.get('channelIds')?.value"
				[edsId]="'channel'"
				[attr.data-test-id]="'channel'"
				[multiple]="true">
			</eds-select>
		</div>

		<lmx-chip-numeric [placeholder]="'Unit IDs'" [id]="'unitIds-input'" formControlName="unitIds"> </lmx-chip-numeric>
	</ng-template>

	<!-- RangedWeightingImport -->
	<ng-template [ngIf]="sliceName === 'RangedWeightingImport'">
		<lmx-date-range-autocomplete
			class="filters-align"
			[selected]="cacheObj.selectedDays || '1'"
			[startDate]="cacheObj.startDate"
			[endDate]="cacheObj.endDate"
			(selectedDate)="selectedDateFilter($event)">
		</lmx-date-range-autocomplete>

		<lmx-chip-autocomplete
			class="filters-align"
			(val)="userkey($event)"
			formControlName="users"
			[options]="users$ | async"
			[id]="'user-input'"
			[placeholder]="'Users'"
			(resetUserList)="userkey('')"
			[label]="'Users'">
		</lmx-chip-autocomplete>

		<div class="mb-5">
			<eds-select
				formControlName="status"
				[required]="false"
				[options]="statusList"
				[placeholder]="'Status'"
				[value]="filterFG.get('status')?.value"
				[edsId]="'status'"
				[attr.data-test-id]="'status'"
				[multiple]="true">
			</eds-select>
		</div>

		<lmx-chip-numeric [placeholder]="'JEE Job IDs'" [id]="'jeeJobIds-input'" formControlName="jobIds"></lmx-chip-numeric>

		<lmx-chip-numeric [placeholder]="'QC Project IDs'" [id]="'qcProjectIds-input'" formControlName="qcProjectIds"></lmx-chip-numeric>
		
		<div class="mb-6">
			<input
				formControlName="qcProjectName"
				gfk-input
				type="text"
				name="qcProjectName"
				[placeholder]="'QC Project Name'"
				[value]="filterFG.get('qcProjectName')?.value"
				[id]="'qcProjectNames-input'"
				[testId]="'qcProjectNames-input'" />
		</div>

		<lmx-chip-numeric [placeholder]="'Import IDs'" [id]="'importIds-input'" formControlName="importIds" class="filters-align"> </lmx-chip-numeric>
	</ng-template>

	<!-- MigrationRuleRuns -->

	<ng-template [ngIf]="sliceName === 'MigrationRuleRuns'">
		<lmx-date-range-autocomplete
			class="filters-align"
			[selected]="cacheObj.selectedDays || '1'"
			[startDate]="cacheObj.startDate"
			[endDate]="cacheObj.endDate"
			(selectedDate)="selectedDateFilter($event)">
		</lmx-date-range-autocomplete>

		<lmx-chip-autocomplete
			class="filters-align"
			(val)="userkey($event)"
			formControlName="users"
			[options]="users$ | async"
			[id]="'user-input'"
			[placeholder]="'Users'"
			(resetUserList)="userkey('')"
			[label]="'Users'">
		</lmx-chip-autocomplete>

		<div class="mb-5">
			<eds-select
				formControlName="status"
				[required]="false"
				[options]="statusList"
				[placeholder]="'Status'"
				[value]="filterFG.get('status')?.value"
				[edsId]="'status'"
				[attr.data-test-id]="'status'"
				[multiple]="true">
			</eds-select>
		</div>

		<div class="mb-6">
			<input
				formControlName="ruleSetName"
				gfk-input
				type="text"
				name="ruleSetName"
				[placeholder]="'Rule Set Name contains'"
				[value]="filterFG.get('ruleSetName')?.value"
				[id]="'ruleSetNameContains-input'"
				[testId]="'ruleSetNameContains-input'" />
		</div>

		<div class="mb-5">
			<eds-select
				formControlName="scopeTypeId"
				[required]="false"				
				[options]="scopeTypeId"
				[placeholder]="'Scope Type'"
				[value]="filterFG.get('scopeTypeId')?.value"
				[edsId]="'scopeType'"
				[attr.data-test-id]="'scopeType'"
				[multiple]="false">
			</eds-select>
		</div>

		<div class="mb-6">
			<input
				formControlName="scope"
				gfk-input
				type="text"
				name="scope"
				[placeholder]="'Scope contains'"
				[value]="filterFG.get('scope')?.value"
				[id]="'scopeContains-input'"
				[testId]="'scopeContains-input'" />
		</div>

		<lmx-chip-numeric [placeholder]="'Source Component IDs'" [id]="'sourceComponentIds-input'" formControlName="sourceComponentIds" class="filters-align"> </lmx-chip-numeric>

		<lmx-chip-numeric [placeholder]="'Target Component IDs'" [id]="'targetComponentIds-input'" formControlName="targetComponentIds" class="filters-align"> </lmx-chip-numeric>

		<lmx-chip-numeric [placeholder]="'Run IDs'" [id]="'runIds-input'" formControlName="runIds" class="filters-align"> </lmx-chip-numeric>

		<lmx-chip-numeric [placeholder]="'Rule Set IDs'" [id]="'ruleSetIds-input'" formControlName="ruleSetIds" class="filters-align"> </lmx-chip-numeric>
	</ng-template>

	<!-- LDGeneral -->
	<ng-template [ngIf]="sliceName === 'LDGeneral'">
		
		<div class="mb-5">
			<eds-select
				formControlName="countries"
				[required]="false"				
				[options]="countriesList"
				[placeholder]="'Country'"
				[value]="filterFG.get('countries')?.value"
				[edsId]="'country'"
				[attr.data-test-id]="'country'"
				[multiple]="true">
			</eds-select>
		</div>

		<lmx-chip-autocomplete 
			class="filters-align" 
			(val)="userkey($event)" 
			formControlName="users" 
			[options]="users$ | async" 
			[id]="'user-input'"
			[placeholder]="'Users'"
			(resetUserList)="userkey('')"
			[label]="'Users'">
		</lmx-chip-autocomplete>

		<lmx-chip-numeric [placeholder]="'Load Definition IDs'" [id]="'loadDefinitionIds-input'" formControlName="loadDefinitionIds"> </lmx-chip-numeric>

		<lmx-date-range-autocomplete
			[selected]="cacheObj.selectedDays || '1'"
			[startDate]="cacheObj.startDate"
			[endDate]="cacheObj.endDate"
			(selectedDate)="selectedDateFilter($event)">
		</lmx-date-range-autocomplete>

		<div class="mb-5">
			<eds-select
				formControlName="status"
				[required]="false"				
				[options]="statusList"
				[placeholder]="'Status'"
				[value]="filterFG.get('status')?.value"
				[edsId]="'status'"
				[attr.data-test-id]="'status'"
				[multiple]="true">
			</eds-select>
		</div>

		<div class="mb-5">
			<eds-select
				formControlName="periodicityIds"
				[required]="false"				
				[options]="periodicityList"
				[placeholder]="'Periodicity'"
				[value]="filterFG.get('periodicityIds')?.value"
				[edsId]="'periodicity'"
				[attr.data-test-id]="'periodicity'"
				[multiple]="false">
			</eds-select>
		</div>

		<div class="mb-5">
			<eds-select
				formControlName="periodIds"
				[required]="false"
				[options]="periodList"
				[placeholder]="'Period'"
				[value]="filterFG.get('periodIds')?.value"
				[edsId]="'period'"
				[attr.data-test-id]="'period'"
				[multiple]="true">
			</eds-select>
		</div>

		<div class="mb-5">
			<eds-select
				formControlName="productionProjectType"
				[required]="false"				
				[options]="productionProjectTypeList"
				[placeholder]="'Production Project Type'"
				[value]="filterFG.get('productionProjectType')?.value"
				[edsId]="'productionProjectType'"
				[attr.data-test-id]="'productionProjectType'"
				[multiple]="true">
			</eds-select>
		</div>

		<div class="mb-6">
			<input
				formControlName="productionProjectName"
				gfk-input
				type="text"
				name="productionProjectName"
				[placeholder]="'Production Project Name'"
				[value]="filterFG.get('productionProjectName')?.value"
				[id]="'productionProjectName-input'"
				[testId]="'productionProjectName-input'" />
		</div>

		<lmx-chip-numeric [placeholder]="'Production Project IDs'" [id]="'productionProjectIds-input'" formControlName="productionProjectIds"> </lmx-chip-numeric>

	</ng-template>

	<!-- RBPublish -->
	<ng-template [ngIf]="sliceName === 'RBPublish'">

		<div class="mb-5">
			<eds-select
				formControlName="countries"
				[required]="false"				
				[options]="countriesList"
				[placeholder]="'Country'"
				[value]="filterFG.get('countries')?.value"
				[edsId]="'country'"
				[attr.data-test-id]="'country'"
				[multiple]="true">
			</eds-select>
		</div>
		
		<div class="mb-5">
			<eds-select
				formControlName="sector"
				[required]="false"				
				[options]="sectorsList"
				[placeholder]="'Sector'"
				[value]="filterFG.get('sector')?.value"
				[edsId]="'sector'"
				[attr.data-test-id]="'sector'"
				[multiple]="true">
			</eds-select>
		</div>

		<lmx-chip-autocomplete 
			class="filters-align" 
			formControlName="category" 
			[options]="category$ | async"
			[placeholder]="'Category'"
			[label]="'Category'">
		</lmx-chip-autocomplete>

		<lmx-chip-autocomplete
			class="filters-align"
			formControlName="domainProductGroup"
			[options]="domainProductGroup$ | async"
			[placeholder]="'Domain Product Group'"
			[label]="'Domain Product Group'">
		</lmx-chip-autocomplete>

		<div class="mb-5">
			<eds-select
				formControlName="operationIds"
				[required]="false"				
				[options]="operationList"
				[placeholder]="'Operation'"
				[value]="filterFG.get('operationIds')?.value"
				[edsId]="'operations'"
				[attr.data-test-id]="'operations'"
				[multiple]="true">
			</eds-select>
		</div>

		<lmx-chip-autocomplete
			class="filters-align"
			(val)="userkey($event)"
			formControlName="users"
			[options]="users$ | async"
			[id]="'user-input'"
			[placeholder]="'Users'"
			(resetUserList)="userkey('')"
			[label]="'Users'">
		</lmx-chip-autocomplete>

		<div class="mb-5">
			<eds-select
				formControlName="periodicityIds"
				[required]="false"				
				[options]="periodicityList"
				[placeholder]="'Periodicity'"
				[value]="filterFG.get('periodicityIds')?.value"
				[edsId]="'periodicity'"
				[attr.data-test-id]="'periodicity'"
				[multiple]="false">
			</eds-select>
		</div>

		<div class="mb-5">
			<eds-select
				formControlName="periodIds"
				[required]="false"
				[options]="periodList"
				[placeholder]="'Period'"
				[value]="filterFG.get('periodIds')?.value"
				[edsId]="'period'"
				[attr.data-test-id]="'period'"
				[multiple]="true">
			</eds-select>
		</div>

		<div class="mb-5">
			<eds-select
				formControlName="status"
				[required]="false"				
				[options]="statusList"
				[placeholder]="'Status'"
				[value]="filterFG.get('status')?.value"
				[edsId]="'status'"
				[attr.data-test-id]="'status'"
				[multiple]="true">
			</eds-select>
		</div>

		<lmx-chip-numeric [placeholder]="'JEE Job IDs'" [id]="'jeeJobIds-input'" formControlName="jobIds"> </lmx-chip-numeric>

		<lmx-chip-numeric [placeholder]="'Load IDs'" [id]="'loadIds-input'" formControlName="loadIds"> </lmx-chip-numeric>

		<lmx-chip-numeric [placeholder]="'RB Project IDs'" [id]="'rbProjectId-input'" formControlName="rbProjectIds" class="filters-align"> </lmx-chip-numeric>

		<div class="mb-6">
			<input
				formControlName="rbProjectName"
				gfk-input
				type="text"
				name="rbProjectName"
				[placeholder]="'RB Project Name'"
				[value]="filterFG.get('rbProjectName')?.value"
				[id]="'rbProjectName-input'"
				[testId]="'rbProjectName-input'" />
		</div>

		<div class="mb-5">
			<eds-select
				formControlName="baseProjectType"
				[required]="false"				
				[options]="baseProjectTypeList"
				[placeholder]="'Baseproject Type'"
				[value]="filterFG.get('baseProjectType')?.value"
				[edsId]="'baseProjectType'"
				[attr.data-test-id]="'baseProjectType'"
				[multiple]="true">
			</eds-select>
		</div>

		<lmx-date-range-autocomplete
			class="filters-align"
			[selected]="cacheObj.selectedDays || '1'"
			[startDate]="cacheObj.startDate"
			[endDate]="cacheObj.endDate"
			(selectedDate)="selectedDateFilter($event)">
		</lmx-date-range-autocomplete>
	</ng-template>
</form>