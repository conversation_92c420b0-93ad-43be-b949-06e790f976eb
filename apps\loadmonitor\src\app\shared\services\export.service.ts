import { Injectable } from "@angular/core";
import { Observable } from 'rxjs';
import { HttpClient } from '@angular/common/http';
import { ConfigService } from './config.service';
import { Jobs } from '@loadmonitor/shared/interfaces/Jobs';
import { Exports } from "../interfaces/Export";
import { ExportPropertyPage } from '../interfaces/ExportPropertyPage';

@Injectable({
  providedIn: 'root'
})
export class ExportService {
  private url!:string;
  //private url = 'http://localhost:5000/api/v1/Exports';

  constructor(private http: HttpClient, private config: ConfigService) {
    this.url = `${this.config.getApiUrl()}/Exports`;
  }

  getAsync(
    countryIds?: number[],
    userIds?: number[],
    statusIds?: number[],
    exportIds?: number[],
    exportName?: string,
    reportingProjectIds?: number[],
    reportingProjectName?: string,
    jobIds?: number[],
    exportFormats?: string[],
    processStages?: (number | undefined)[],
    startDate?: Date,
    endDate?: any,
    reportGroupIDs?: number[],
    DeliveryIds?: number[]
  ): Observable<Jobs<Exports>> {
    const body = {
      countryIds,
      userIds,
      statusIds,
      exportIds,
      exportName,
      reportingProjectIds,
      reportingProjectName,
      jobIds,
      exportFormats,
      processStages,
      startDate,
      endDate,
      reportGroupIDs,
      DeliveryIds
    };

    //return this.http.get<Jobs<Exports>>('/assets/response/export.json');
    return this.http.post<Jobs<Exports>>(this.url, body);
  }

    getDetailAsync(
    exportId?: number,
    toolFormat?: string
  ): Observable<ExportPropertyPage[]> {
    return this.http.get<ExportPropertyPage[]>(`${this.url}/${exportId}/format/${toolFormat}`);
  }
}
