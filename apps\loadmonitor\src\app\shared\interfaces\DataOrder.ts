export interface DataOrder {
    dataOrderId: number;
    countryIds: number;
    periodIds: number;
    periodicityIds: number;
    productionProjectIds: number;
    productionProjecttName: string;
    productionProjectType: number;
    statusIds: number;
    userNames: string;
    startDate: Date;
    endDate: Date;
    started: Date;
    finished: Date;
    runtime: Date;
    isSelected?: boolean;
    isExpanded?: boolean;
    isChecked?: boolean;
  }