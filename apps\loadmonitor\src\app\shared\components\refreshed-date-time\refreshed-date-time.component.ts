import { Component, OnInit } from '@angular/core';
import * as moment from 'moment-timezone';

@Component({
	selector: 'lm-refreshed-date-time',
	templateUrl: './refreshed-date-time.component.html',
	styleUrls: ['./refreshed-date-time.component.scss'],
})
export class RefreshedDateTimeComponent implements OnInit {

	date!: Date;
	refreshedDateTime!: string;

	ngOnInit(): void {
		this.calculateRefreshedDateTime();
	}

	calculateRefreshedDateTime(){
		const cetTime:any = moment()?.tz("Europe/Berlin").format('HH:mm');
		this.date = new Date();
		this.date.setHours(cetTime.split(':')[0]);
		this.date.setMinutes(cetTime.split(':')[1]);
		this.refreshedDateTime = cetTime + ' CET';// + timeZoneFormatted;
    this.date.setHours(this.date.getHours() + 3);
	}

}
