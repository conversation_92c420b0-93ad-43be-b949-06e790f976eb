import { DatePipe } from '@angular/common';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ProgressSpinnerComponent } from '../progress-spinner/progress-spinner.component';

import { FloatingPanelComponent } from './floating-panel.component';

describe('FloatingPanelComponent', () => {
  let component: FloatingPanelComponent;
  let fixture: ComponentFixture<FloatingPanelComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [FloatingPanelComponent, ProgressSpinnerComponent],
      imports: [HttpClientTestingModule],
      providers: [DatePipe],
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(FloatingPanelComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
