import { Component, OnInit, ViewChild, AfterContentInit, Input, HostListener, On<PERSON><PERSON>roy, ChangeDetectorRef, AfterViewInit } from '@angular/core';
import { MatPaginator } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import { MatTable, MatTableDataSource } from '@angular/material/table';
import { COMMA, ENTER } from '@angular/cdk/keycodes';
import { FeatureMove } from '@loadmonitor/shared/interfaces/FeatureMove';
import { SettingsConstants } from '@loadmonitor/shared/settings.constants';
import { FormBuilder, FormGroup } from '@angular/forms';
import { FeatureMoveService } from '@loadmonitor/shared/services/feature-move.service';
import { MatSnackBar } from '@angular/material/snack-bar';
import { DefaultSnackbarComponent } from '@loadmonitor/shared/components/default-snackbar/default-snackbar.component';
import { JobTypes } from '@loadmonitor/shared/jobTypes';
import { HelperService } from '@loadmonitor/shared/services/helper.service';
import { animate, state, style, transition, trigger } from '@angular/animations';
import { PropertyPage } from '@loadmonitor/shared/interfaces/PropertyPage';
import { FeatureMoveCache } from '@loadmonitor/shared/interfaces/caching/FeatureMoveCache';
import { BehaviorSubject, Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { environment } from 'apps/loadmonitor/src/environments/environment';
import { DatePipe } from '@angular/common';
import { ToggleButtonComponent } from '@gfk/ng-lib';
import * as moment from 'moment';
import { Filter, FilterRecords, FilterService, FilterTrayComponent } from '@dwh/lmx-lib/src';
import { FilterTrayCacheService } from '@loadmonitor/shared/services/filter-tray-cache.service';

@Component({
  selector: 'lm-feature-move',
  templateUrl: './feature-move.component.html',
  styleUrls: ['./feature-move.component.scss'],
  animations: [
    trigger('detailExpand', [
      state('collapsed', style({ height: '0px', minHeight: '0' })),
      state('expanded', style({ height: '*' })),
      transition('expanded <=> collapsed', animate('225ms cubic-bezier(0.4, 0.0, 0.2, 1)')),
    ]),
  ],
})
export class FeatureMoveComponent implements OnInit, AfterContentInit, AfterViewInit, OnDestroy {
  @Input() icon = 'delete';
  @ViewChild(FilterTrayComponent) filterTrayComponent: FilterTrayComponent | undefined;
  @ViewChild(ToggleButtonComponent) togglebuttonComponent: ToggleButtonComponent | undefined;

  @ViewChild(MatSort)
  sort: MatSort = new MatSort();

  @ViewChild(MatTable)
  table!: MatTable<FeatureMove>;

  @ViewChild(MatPaginator)
  paginator!: MatPaginator;

  // Progress Spinner visibility
  isLoading = false;
  public FeatureMoveForm!: FormGroup;


  public cacheObj: FeatureMoveCache = {} as FeatureMoveCache;

  selectedFilters: any;
  cacheValue: BehaviorSubject<any> = new BehaviorSubject<any>({});
  resetForm: Subject<boolean> = new Subject();
  cacheName = 'cache::FeatureMove';
  //Mat-Expansion-Panel
  panelOpenState = false;

  jobType: JobTypes = JobTypes.featureMove;
  selectedRow: any;

  readonly separatorKeysCodes = [ENTER, COMMA] as const;
  readonly date_with_time = SettingsConstants.DATE_FORMAT_WITH_TIME;

  //Mat-Table
  dataSource = new MatTableDataSource<FeatureMove>();
  displayedColumns: string[] = [
    'details',
    'moveId',
    'status',
    'productGroupFeatureName',
    'jobId',
    'jeeJobInfo',
    'createdBy',
    'created',
    'started',
    'finished',
    'runtime',
    'message',
  ];

  readonly only_time = SettingsConstants.FORMAT_ONLY_TIME;

  // Property Pages
  expandedElement!: PropertyPage | null;
  featureMoveDetail!: PropertyPage[];
  eventsSubject: Subject<void> = new Subject<void>();
  filterCount = '';
  IsDisabled = false;
  environments = environment.environments;
  IsEnabled = false;
  FilterTrayOpenFlag = true;
  modalshow = false;
  EnableRefresh = true;
  toggleInterval!: any;
  timer = 60;
  cacheInterval!: any;
  defaultoption = localStorage.getItem(this.cacheName + '-LastFilter') || 'Default';
  defaultfilter = JSON.parse(localStorage.getItem(this.cacheName + '-DefaultFilter') || '[]');
  currentfilter = JSON.parse(localStorage.getItem('QuickFilter') || '[]').filter(
    (x) => x.user == localStorage.getItem('username') && x.slice == window.location.href.split('/')[3]
  );
  refreshedDateTime!: boolean;
  featureMoveData!: any[];

  @HostListener('document:visibilitychange', ['$event'])
  appVisibility() {
    if (document.hidden) {
      if (this.cacheInterval) {
        clearInterval(this.cacheInterval);
      }
      if (this.toggleInterval) {
        clearInterval(this.toggleInterval);
      }
    } else {
      if (this.cacheObj.isAutoRefresh === true) {
        this.cacheInterval = setInterval(() => {
          if (this.defaultoption != 'Default') {
            this.refresh();
          }
        }, this.timer * 1000);
      } else {
        if (this.cacheInterval !== undefined) clearInterval(this.cacheInterval);
        if (this.toggleInterval !== undefined) clearInterval(this.toggleInterval);
      }
    }
  }
  private destroy$ = new Subject<void>();

  constructor(
    private featureMoveDataService: FeatureMoveService,
    private snackBar: MatSnackBar,
    private formBuilder: FormBuilder,
    private helperService: HelperService,
    public filterService: FilterService,
    public datepipe: DatePipe,
    private cdRef: ChangeDetectorRef,
    private filterTrayCacheService: FilterTrayCacheService
  ) {
  }

  ngOnDestroy() {
    if (this.cacheInterval) {
      clearInterval(this.cacheInterval);
    }

    if (this.toggleInterval) {
      clearInterval(this.toggleInterval);
    }
    this.destroy$.next();
    this.destroy$.complete();
  }

  private updateFC(): void {
    if(this.cacheObj){
			this.filterTrayCacheService.getCacheValueData(this.cacheObj);
		}
  }

  ngOnInit(): void {
    if (this.environments.filter((x) => x.location == window.location.origin)[0]) {
      this.IsEnabled = this.environments.filter((x) => x.location == window.location.origin)[0].enabled;
    } else {
      this.IsEnabled = false;
    }
    this.FeatureMoveForm = this.formBuilder.group({
      isAutoRefresh: this.togglebuttonComponent?.checked,
    });
    this.SetFiltersCount();
  }

  ngAfterViewInit() {
    this.cdRef.detectChanges();
  }

  ngAfterContentInit() {
    this.afterChildComponentsLoaded();
    this.CreateFilterSummaryChips();
    this.SetFiltersCount();

    if (this.cacheObj.isAutoRefresh === true) {
      if (this.cacheInterval === undefined) {
        this.cacheInterval = window.setInterval(() => {
          if (this.defaultoption != 'Default') {
            this.refresh();
          }
        }, this.timer * 1000);
      }
    } else {
      if (this.cacheInterval !== undefined) clearInterval(this.cacheInterval);
      if (this.toggleInterval !== undefined) clearInterval(this.toggleInterval);
    }
  }

  afterChildComponentsLoaded() {
    this.cacheObj = JSON.parse(localStorage.getItem(this.cacheName) || '{}');
    this.updateFC();
  }

  refresh(): void {
    this.refreshedDateTime = false;
    this.filterTrayComponent?.showSpinner(true);
    this.isLoading = true;
    this.destroy$.next();
    this.featureMoveDataService
      .getAsync(
        this.selectedFilters.users?.map((users) => users.userName),
        this.selectedFilters.status?.map((status) => status),
        this.selectedFilters.loadIds,
        this.selectedFilters.jobIds,
        this.selectedFilters.startDate ? this.selectedFilters.startDate : null,
        this.selectedFilters.endDate ? moment(this.selectedFilters.endDate).format().split('T')[0] + 'T23:59:59.999Z' : null
      )
      .pipe(
				takeUntil(this.destroy$)
			)
      .subscribe((result) => {
        this.refreshedDateTime = true;
        if (result.moreRecordsAvailable) {
          this.notify('More than 1000 results exist!');
        }

        if (result.count === 0) {
          this.notify('No result found!');
          this.featureMoveData = [];
        }
        this.isLoading = false;
        this.featureMoveData = result.records;
        // set runtime column
        result.records.forEach((element) => {
          element.runtime = this.helperService.calculateDuration(element.started, element.finished, true);
        });

        this.dataSource = new MatTableDataSource<FeatureMove>(result.records);
        this.dataSource.sort = this.sort;
        this.paginator.pageIndex = 0;
        this.dataSource.paginator = this.paginator;
        this.table.dataSource = this.dataSource;
        this.filterTrayComponent?.showSpinner(false);
      },
			(error) => {
				if (error.status === 504) {
					this.notify('Gateway Timeout. The server took too long to respond. Please try again later.');
				} 
				else {
					this.notify(`An error occurred: ${error.message}`);
				}
				this.isLoading = false;
				this.filterTrayComponent?.showSpinner(false);
			});
    this.SetFiltersCount();
  }

  /**
   * JUST FOR DEBUG! send a request with a preset data and fill the explorer data source
   */
  debugSubmit(): void {
    // if (this.history === undefined) {
    // 	this.history = {} as HistoryFilterComponent;
    // }
    // this.history.startDate = new Date('05/09/2018');
    // this.history.endDate = new Date('06/09/2018');

    this.refresh();
  }

  private notify(message: string) {
    this.snackBar.openFromComponent(DefaultSnackbarComponent, {
      duration: 10 * 1000,
      data: message,
    });
  }

  selectRow(row: FeatureMove) {
    this.featureMoveDetail = [];
    row.isSelected = true;

    this.dataSource.data.forEach((element) => {
      if (row.moveId != element.moveId) {
        element.isExpanded = false;
        element.isSelected = false;
      }
    });

    this.selectedRow = row;
    this.featureMoveDataService.getDetailAsync(row.moveId).subscribe((result) => {
      this.helperService.floatingPanelData$.next({
        data: result,
        jobType: JobTypes.featureMove,
      });
    });
  }

  clickNextButton() {
    this.selectedRow.isSelected = false;
  }

  setFormValue(autoRefreshStatus) {
    this.FeatureMoveForm.setValue({
      isAutoRefresh: autoRefreshStatus === undefined || autoRefreshStatus === false ? false : true,
    });
  }

  saveInQuickFilter(autoRefreshStatus, filterName) {
    this.setFormValue(autoRefreshStatus);
    this.currentfilter = JSON.parse(localStorage.getItem('QuickFilter') || '[]');
    this.currentfilter.push({
      data: this.selectedFilters,
      filter: filterName,
      slice: window.location.href.split('/')[3],
      user: localStorage.getItem('username'),
    });
    this.defaultoption = filterName;
    const oldcache = this.cacheObj;
    const olddefault = localStorage.getItem(this.cacheName + '-DefaultFilter');
    this.ConfirmApply(autoRefreshStatus);
    this.cacheObj = oldcache;
    localStorage.setItem(this.cacheName + '-DefaultFilter', olddefault || '[]');
    localStorage.setItem('QuickFilter', JSON.stringify(this.currentfilter));
    localStorage.setItem(this.cacheName + '-LastFilter', filterName);
  }

  applyfilter(selectedvalue) {
    this.resetForm.next(true);
    const oldautotoggle = this.cacheObj.isAutoRefresh;
    selectedvalue = selectedvalue.value ? selectedvalue.value : selectedvalue;
    this.defaultoption = selectedvalue;
    localStorage.setItem(this.cacheName + '-LastFilter', this.defaultoption || 'Default');
    if (selectedvalue == 'Default') {
      if (
        JSON.parse(localStorage.getItem(this.cacheName + '-DefaultFilter') || '[]').filter(
          (x) => x.user == localStorage.getItem('username') && x.slice == window.location.href.split('/')[3]
        )[0]
      ) {
        this.cacheObj = JSON.parse(localStorage.getItem(this.cacheName + '-DefaultFilter') || '[]').filter(
          (x) => x.user == localStorage.getItem('username') && x.slice == window.location.href.split('/')[3]
        )[0]['data'];
      }

      localStorage.setItem(this.cacheName, JSON.stringify(this.cacheObj));
    } else {
      this.cacheObj = JSON.parse(localStorage.getItem('QuickFilter') || '[]')
        .filter((x) => x.user == localStorage.getItem('username') && x.slice == window.location.href.split('/')[3])
        .filter((x) => x.filter == selectedvalue)[0]['data'];
      localStorage.setItem(this.cacheName, JSON.stringify(this.cacheObj));
    }
    this.cacheObj.isAutoRefresh = oldautotoggle;
    this.CreateFilterSummaryChips();


    if (this.cacheObj.selectedDays == '3') {
      const date = new Date();
      this.cacheObj.startDate = new Date(Date.UTC(date.getFullYear(), date.getMonth(), date.getDate() - 2, 0, 0, 0, 0));
      this.cacheObj.endDate = new Date(Date.UTC(date.getFullYear(), date.getMonth(), date.getDate(), 0, 0, 0, 0));
    } else if (this.cacheObj.selectedDays == '2') {
      const date = new Date();
      this.cacheObj.startDate = new Date(Date.UTC(date.getFullYear(), date.getMonth(), date.getDate(), 0, 0, 0, 0));
      this.cacheObj.endDate = new Date(Date.UTC(date.getFullYear(), date.getMonth(), date.getDate(), 0, 0, 0, 0));
    }
    setTimeout(() => {
      this.updateFC();
      this.refresh();
    }, 200);
    this.selectedFilters = this.cacheObj;
    localStorage.setItem(this.cacheName, JSON.stringify(this.cacheObj));
  }

  saveInCache(autoRefreshStatus, AutoToggle) {
    this.setFormValue(autoRefreshStatus);
    localStorage.setItem(this.cacheName, JSON.stringify(this.selectedFilters));
    this.cacheObj = JSON.parse(localStorage.getItem(this.cacheName) || '{}');
    if (!AutoToggle) {
      this.defaultfilter = [];
      this.defaultfilter.push({
        data: this.selectedFilters,
        slice: window.location.href.split('/')[3],
        user: localStorage.getItem('username'),
      });
      localStorage.setItem(this.cacheName + '-DefaultFilter', JSON.stringify(this.defaultfilter));
      let nofilter = 0;
      let selectedfilter = '';
      this.currentfilter.forEach((i) => {
        // if (i.filter == this.defaultoption) {
        if(JSON.stringify(i.data) == JSON.stringify(this.cacheObj)){
          nofilter++;
          selectedfilter = i.filter;
        }
      });
      if (nofilter == 0) {
        this.defaultoption = 'Default';
      } else {
        this.defaultoption = selectedfilter;
      }
      localStorage.setItem(this.cacheName + '-LastFilter', this.defaultoption);
    }
    this.updateFC();
  }

  toggleChecked(check: any) {
    if (check === true) {
      this.saveInCache(check, true);
      this.toggleInterval = setInterval(() => {
        if (this.defaultoption != 'Default') {
          this.refresh();
        }
      }, this.timer * 1000);
    } else {
      if (this.toggleInterval !== undefined) clearInterval(this.toggleInterval);
      if (this.cacheInterval !== undefined) clearInterval(this.cacheInterval);
      this.saveInCache(check, true);
    }
  }

  public isShownFilterTrayEventHandler($event: boolean) {
    this.filterTrayComponent?.toggleShowTray($event);
    this.FilterTrayOpenFlag = $event;
  }

  getWidth() {
    this.FilterTrayOpenFlag = !this.FilterTrayOpenFlag;
    return '100%';
  }

  openmodal() {
    this.eventsSubject.next();
  }

  disablesave(status) {
    this.IsDisabled = status;
  }

  ConfirmApply(autoRefreshStatus) {
    this.saveInCache(autoRefreshStatus, false);
    this.CreateFilterSummaryChips();
    let count;
    this.filterService.filtersSelected$.subscribe((selectedFilters: FilterRecords) => {
      count = Number(Object.keys(selectedFilters).length);
    });

    if (count > 0) {
      this.refresh();
    } else {
      if (confirm('Loading results without any filters selected will take some time to load. Are you sure you want to proceed?')) {
        this.refresh();
      }
    }
  }

  filterSummaryChipClear() {
    if (confirm('This will remove all of your selected filters and could not be reversed. Are you sure you want to do that?')) {
      this.resetForm.next(true);
      this.filterTrayCacheService.getCacheNameData(this.cacheName);
      this.saveInCache(this.cacheObj.isAutoRefresh, false);
      this.CreateFilterSummaryChips();
      this.SetFiltersCount();
    }
  }

  CreateFilterSummaryChips() {
    if (this.cacheObj && Object.keys(this.cacheObj).length) {
      this.usersFilterSummary();
      this.statusFilterSummary();
      this.jeeJobIdsFilterSummary();
      this.loadIdsFilterSummary();
      this.datesFilterSummary(this.cacheObj.startDate, this.cacheObj.endDate);
    }
  }

  SetFiltersCount() {
    let count;
    this.filterService.filtersSelected$.subscribe((selectedFilters: FilterRecords) => {
      count = Number(Object.keys(selectedFilters).length);
    });
    if (count > 0) {
      this.filterCount = '(' + count.toString() + ')';
    } else {
      this.filterCount = '';
    }
  }

  usersFilterSummary() {
    this.filterService.setFilters({
      userIds: {
        name: 'User',
        values: this.cacheObj.users?.map((i) => i.name),
        slice: window.location.href.split('/')[3],
      } as Filter,
    });
  }

  statusFilterSummary() {
    this.filterService.setFilters({
      statusIds: {
        name: 'Status',
        values: this.cacheObj.status,
        slice: window.location.href.split('/')[3],
      } as Filter,
    });
  }

  loadIdsFilterSummary() {
    this.filterService.setFilters({
      loadUnitIds: {
        name: 'loadIds',
        values: this.cacheObj.loadIds,
        slice: window.location.href.split('/')[3],
      } as Filter,
    });
  }

  jeeJobIdsFilterSummary() {
    this.filterService.setFilters({
      jeeJobIds: {
        name: 'jeeJobIds',
        values: this.cacheObj.jobIds,
        slice: window.location.href.split('/')[3],
      } as Filter,
    });
  }

  datesFilterSummary(dateStart: any, dateEnd: any) {
    if (dateStart != null || dateEnd != null) {
      const start = dateStart == 'Invalid Date' || dateStart == null ? '' : this.datepipe.transform(dateStart, 'yyyy-MM-dd');
      const end = dateEnd == 'Invalid Date' || dateEnd == null ? '' : this.datepipe.transform(dateEnd, 'yyyy-MM-dd');
      this.filterService.setFilters({
        dates: {
          name: 'Date',
          values: (start !== undefined && end !== undefined) ? [start, end] : null,
          slice: window.location.href.split('/')[3],
        } as Filter,
      });
    } else {
      this.filterService.setFilters({
        dates: {
          name: 'Date',
          values: null,
          slice: window.location.href.split('/')[3],
        } as unknown as Filter,
      });
    }
  }

  getSelectedFilters(selectedFilters: any) {
    this.cacheObj = selectedFilters;
    this.selectedFilters = selectedFilters;
  }

}
