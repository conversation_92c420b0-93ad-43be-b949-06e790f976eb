import { ComponentFixture, TestBed } from '@angular/core/testing';
import { SharedModule } from '@loadmonitor/shared/shared.module';

import { BasicBooleanComponent } from './basic-boolean.component';

describe('BasicBooleanComponent', () => {
  let component: BasicBooleanComponent;
  let fixture: ComponentFixture<BasicBooleanComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [BasicBooleanComponent],
      imports: [SharedModule],
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(BasicBooleanComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
