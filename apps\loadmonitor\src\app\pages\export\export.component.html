<lm-navigation></lm-navigation>

<div class="each-slice-top-headbar" [ngClass]="FilterTrayOpenFlag ? 'filter-tray-open' : 'filter-tray-close'">
	<h3 class="alignleft">Export</h3>
	<div class="flexdiv-buttons">
		<button testId="lmx-button"
			gfk-button
			type="primary"
			class="jira-button-margin-right btn-secondary gfk-btn"
			(click)="openJiraTicket()"
			[disabled]="!checkAtleastOneRowChecked()"
		>
			Create JIRA Ticket
		</button>
		<lmx-filter-button showFilterCount="{{ filterCount }}" (isShownFilterTray)="isShownFilterTrayEventHandler($event)"></lmx-filter-button>
	</div>
</div>

<lmx-filter-tray
	(ButtonShowHideEvent)="this.getWidth()"
	(clickApplyButton)="ConfirmApply(true)"
	(clickSaveButton)="openmodal()"
	[IsEnabled]="IsEnabled"
	[IsDisabled]="IsDisabled"
>
	<lm-filter-tray-form
		[sliceName]="'Exports'"
		[resetForm]="resetForm"
		(selectedFilters)="getSelectedFilters($event)"
	>
	</lm-filter-tray-form>
</lmx-filter-tray>

<div class="filter-area" [ngClass]="FilterTrayOpenFlag ? 'filter-tray-open' : 'filter-tray-close'">
	<div class="toggle-button">
		<div>
			<lm-quick-filter
				[selectedfilter]="defaultoption"
				class="p-3.5"
				[events]="eventsSubject.asObservable()"
				*ngIf="IsEnabled"
				[modalshow]="modalshow"
				(saveInQuickFilter)="saveInQuickFilter(cacheObj.isAutoRefresh, $event)"
				(applyquickfilter)="applyfilter($event)"
				(disabledbutton)="disablesave($event)"
			>
			</lm-quick-filter>
			<lm-filter-summary (resetFilters)="filterSummaryChipClear($event)"></lm-filter-summary>
		</div>
		<div class="refreshedTime">
			<lm-refreshed-date-time *ngIf="refreshedDateTime" class="pr-3 d-inline-block"></lm-refreshed-date-time>
			<gfk-toggle-button
				*ngIf="EnableRefresh && IsEnabled"
				[checked]="cacheObj.isAutoRefresh || false"
				title="Auto Refresh"
				class="autoRefresh d-inline-block"
				[disabled]="defaultoption === 'Default'"
				(onChange)="toggleChecked($event)"
			>
			</gfk-toggle-button>
		</div>
	</div>
</div>

<article class="table-grid">
	<div class="mat-elevation-z8 tab-container" [ngClass]="(exportData?.length) ? 'table-height' : ''">
		<table mat-table [dataSource]="dataSource" matSort class="full-width-table js-tbl-export-result" multiTemplateDataRows>
			<ng-container matColumnDef="details">
				<th mat-header-cell *matHeaderCellDef></th>
				<td mat-cell *matCellDef="let element">
					<mat-icon (click)="element.isExpanded = element.isExpanded; element.isExpanded = !element.isExpanded" *ngIf="element.isExpanded"
						>keyboard_arrow_down</mat-icon
					>
					<mat-icon (click)="element.isExpanded = !element.isExpanded; element.isExpanded = element.isExpanded" *ngIf="!element.isExpanded"
						>chevron_right</mat-icon
					>
				</td>
			</ng-container>

			<ng-container matColumnDef="menu">
				<th mat-header-cell *matHeaderCellDef class="selectAllPad">
					<mat-checkbox (change)="selectRows($event)" [checked]="this.IsSelectAll" [indeterminate]="this.IsSelectAll"></mat-checkbox>
				</th>
				<td mat-cell *matCellDef="let element">
					<div class="flexdiv">
						<mat-checkbox class="matcheckbox" [checked]="element.isChecked" (change)="rowToggleSelection($event, element)"></mat-checkbox>
						<lm-detail-menu [configuration]="element.detailMenuConfig"></lm-detail-menu>
					</div>
				</td>
			</ng-container>

			<ng-container matColumnDef="exportId">
				<th mat-header-cell *matHeaderCellDef mat-sort-header>Export ID</th>
				<td mat-cell *matCellDef="let element">{{ element.exportId }}</td>
			</ng-container>

			<ng-container matColumnDef="warningLevel">
				<th mat-header-cell *matHeaderCellDef mat-sort-header></th>
				<td mat-cell *matCellDef="let element">
					<ng-container [ngSwitch]="element.warningLevel">
						<ng-container *ngSwitchCase="'Level1'">
							<p class="red-R">R</p>
						</ng-container>
						<ng-container *ngSwitchCase="'Level2'">
							<mat-icon class="red-icon">warning</mat-icon>
						</ng-container>
						<ng-container *ngSwitchDefault> </ng-container>
					</ng-container>
				</td>
			</ng-container>

			<ng-container matColumnDef="exportName">
				<th mat-header-cell *matHeaderCellDef mat-sort-header>Export Name</th>
				<td mat-cell *matCellDef="let element">{{ element.exportName }}</td>
			</ng-container>

			<ng-container matColumnDef="reportProjectName">
				<th mat-header-cell *matHeaderCellDef mat-sort-header>Rep. Project Name</th>
				<td mat-cell *matCellDef="let element">{{ element.reportProjectName }}</td>
			</ng-container>

			<ng-container matColumnDef="reportProjectId">
				<th mat-header-cell *matHeaderCellDef mat-sort-header>Rep. Project ID</th>
				<td mat-cell *matCellDef="let element">{{ element.reportProjectId }}</td>
			</ng-container>

			<ng-container matColumnDef="reportGroupId">
				<th mat-header-cell *matHeaderCellDef mat-sort-header>Rep. Group ID</th>
				<td mat-cell *matCellDef="let element">{{ element.reportGroupId }}</td>
			</ng-container>

			<ng-container matColumnDef="deliveryId">
				<th mat-header-cell *matHeaderCellDef mat-sort-header>CDM Delivery ID</th>
				<td mat-cell *matCellDef="let element">{{ element.deliveryId }}</td>
			</ng-container>

			<ng-container matColumnDef="deliveryTemplateId">
				<th mat-header-cell *matHeaderCellDef mat-sort-header>Delivery Template ID</th>
				<td mat-cell *matCellDef="let element">{{ element.deliveryTemplateId }}</td>
			</ng-container>

			<ng-container matColumnDef="toolFormat">
				<th mat-header-cell *matHeaderCellDef mat-sort-header>Export Format</th>
				<td mat-cell *matCellDef="let element">{{ element.toolFormat }}</td>
			</ng-container>

			<ng-container matColumnDef="status">
				<th mat-header-cell *matHeaderCellDef mat-sort-header>Status</th>
				<td mat-cell *matCellDef="let element">{{ element.status }}</td>
			</ng-container>

			<ng-container matColumnDef="message">
				<th mat-header-cell *matHeaderCellDef mat-sort-header>Message</th>
				<td mat-cell *matCellDef="let element">{{ element.message }}</td>
			</ng-container>

			<ng-container matColumnDef="expectedExportCount">
				<th mat-header-cell *matHeaderCellDef mat-sort-header># Exported Exports/ # Allocated Exports</th>
				<td mat-cell *matCellDef="let element">{{ element.expectedExportCount }}/{{ element.allocatedExportCount }}</td>
			</ng-container>

			<ng-container matColumnDef="period">
				<th mat-header-cell *matHeaderCellDef mat-sort-header>Period</th>
				<td mat-cell *matCellDef="let element">{{ element.period }}</td>
			</ng-container>

			<ng-container matColumnDef="jobId">
				<th mat-header-cell *matHeaderCellDef mat-sort-header>JEE Job Id</th>
				<td mat-cell *matCellDef="let element" name="js-result-job-id">{{ element.jobId }}</td>
			</ng-container>

			<ng-container matColumnDef="jobPriority">
				<th mat-header-cell *matHeaderCellDef mat-sort-header>Job Priority</th>
				<td mat-cell *matCellDef="let element" name="js-result-job-id">{{ element.jobPriority }}</td>
			</ng-container>

			<ng-container matColumnDef="favoriteId">
				<th mat-header-cell *matHeaderCellDef mat-sort-header>Favorite ID</th>
				<td mat-cell *matCellDef="let element">{{ element.favoriteId }}</td>
			</ng-container>

			<ng-container matColumnDef="favoriteName">
				<th mat-header-cell *matHeaderCellDef mat-sort-header>Favorite Name</th>
				<td mat-cell *matCellDef="let element">{{ element.favoriteName }}</td>
			</ng-container>

			<ng-container matColumnDef="scope">
				<th mat-header-cell *matHeaderCellDef mat-sort-header>Scope</th>
				<td mat-cell *matCellDef="let element">{{ element.scope }}</td>
			</ng-container>

			<ng-container matColumnDef="profileId">
				<th mat-header-cell *matHeaderCellDef mat-sort-header>Profile ID</th>
				<td mat-cell *matCellDef="let element">{{ element.profileId }}</td>
			</ng-container>

			<ng-container matColumnDef="profileName">
				<th mat-header-cell *matHeaderCellDef mat-sort-header>Profile Name</th>
				<td mat-cell *matCellDef="let element">{{ element.profileName }}</td>
			</ng-container>

			<ng-container matColumnDef="createdBy">
				<th mat-header-cell *matHeaderCellDef mat-sort-header>Created By</th>
				<td mat-cell *matCellDef="let element">{{ element.createdBy }}</td>
			</ng-container>

			<ng-container matColumnDef="created">
				<th mat-header-cell *matHeaderCellDef mat-sort-header>Created On</th>
				<td mat-cell *matCellDef="let element">{{ element.created | date: date_with_time }}</td>
			</ng-container>

			<ng-container matColumnDef="started">
				<th mat-header-cell *matHeaderCellDef mat-sort-header>Started On</th>
				<td mat-cell *matCellDef="let element">{{ element.started | date: date_with_time }}</td>
			</ng-container>

			<ng-container matColumnDef="finished">
				<th mat-header-cell *matHeaderCellDef mat-sort-header>Finished On</th>
				<td mat-cell *matCellDef="let element">{{ element.finished | date: date_with_time }}</td>
			</ng-container>

			<ng-container matColumnDef="estimatedRuntime">
				<th mat-header-cell *matHeaderCellDef mat-sort-header>Estimated Runtime</th>
				<td mat-cell *matCellDef="let element">{{ element.estimatedRuntime }}</td>
			</ng-container>

			<ng-container matColumnDef="runtime">
				<th mat-header-cell *matHeaderCellDef mat-sort-header>Runtime</th>
				<td mat-cell *matCellDef="let element">{{ element.runtime }}</td>
			</ng-container>

			<ng-container matColumnDef="expandedDetail">
				<td mat-cell *matCellDef="let element" [attr.colspan]="displayedColumns.length">
					<div class="expanded-element-detail" [@detailExpand]="element.isExpanded ? 'expanded' : 'collapsed'">
						<lm-floating-panel *ngIf="element.isExpanded"></lm-floating-panel>
					</div>
				</td>
			</ng-container>

			<tr mat-header-row *matHeaderRowDef="displayedColumns; sticky: true" class="columnHeader"></tr>
			<tr
				mat-row
				*matRowDef="let row; columns: displayedColumns"
				[class.selected-row-background]="row.isSelected"
				[class.expanded-row]="expandedElement === row"
				(click)="selectRow(row); expandedElement = expandedElement === row ? null : row"
			></tr>
			<tr mat-row *matRowDef="let row; columns: ['expandedDetail']" class="expanded-detail-row"></tr>
		</table>
	</div>
	<mat-paginator [pageSizeOptions]="[10, 20, 50, 100]" (page)="clickNextButton()" showFirstLastButtons></mat-paginator>
</article>
<lm-version></lm-version>