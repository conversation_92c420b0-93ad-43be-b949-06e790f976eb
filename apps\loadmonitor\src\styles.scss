@use 'sass:map' as map;
@use 'node_modules/@angular/material' as mat;
// Core styles that can be used to apply material design treatments to any element.
@use 'node_modules/@angular/material/core/typography/typography' as mat-typography;
@use 'node_modules/@angular/cdk/overlay' as mat-overlay;
@use 'node_modules/@angular/cdk/a11y' as mat-a11y;
@use 'node_modules/@angular/cdk/text-field' as mat-text-field;

@use 'styles/theme/gfk.typography' as gfk;
@use 'styles/theme/material-gfk.typography' as mat-gfk;


@use "@gfk/style" as gfk-style;
//@import '~@angular/cdk/overlay-prebuilt.css';
@import '@gfk/style/normalize';
@import '@gfk/style/global';

//==================================================/DO NOT USE=========================================================
// Include the common styles for Angular Material. We include this here so that you only
// have to load a single css file for Angular Material in your app.
// Be sure that you only ever include this mixin once!
// Or Use the individual @includes per component
//@include mat.core(mat-gfk.$typography);

// Override typography for all Angular Material, including mat-base-typography and all components.
//@include mat.all-component-typographies(mat-gfk.$typography);
//===================================================DO NOT USE/========================================================

//=================================================/USE INSTEAD=========================================================
// Override typography for a specific Angular Material components.
@include mat-typography.typography-hierarchy(mat-gfk.$typography);
@include mat.ripple();
@include mat-a11y.a11y-visually-hidden();
@include mat-overlay.overlay();
@include mat-text-field.text-field-autosize();
@include mat-text-field.text-field-autofill();

// @include mat.badge-typography(gfk.$typography);
@include mat.autocomplete-typography(gfk.$typography);
@include mat.bottom-sheet-typography(gfk.$typography);
@include mat.button-typography(gfk.$typography);
@include mat.button-toggle-typography(gfk.$typography);
// @include mat.card-typography(gfk.$typography);
// @include mat.checkbox-typography(gfk.$typography);
@include mat.chips-typography(gfk.$typography);
@include mat.divider-typography(gfk.$typography);
@include mat.table-typography(gfk.$typography);
@include mat.datepicker-typography(gfk.$typography);
@include mat.dialog-typography(gfk.$typography);
@include mat.expansion-typography(gfk.$typography);
@include mat.form-field-typography(gfk.$typography);
@include mat.grid-list-typography(gfk.$typography);
@include mat.icon-typography(gfk.$typography);
@include mat.input-typography(gfk.$typography);
@include mat.menu-typography(gfk.$typography);
@include mat.paginator-typography(gfk.$typography);
// @include mat.progress-bar-typography(gfk.$typography);
// @include mat.progress-spinner-typography(gfk.$typography);
// @include mat.radio-typography(gfk.$typography);
@include mat.select-typography(gfk.$typography);
@include mat.sidenav-typography(gfk.$typography);
// @include mat.slide-toggle-typography(gfk.$typography);
// @include mat.slider-typography(gfk.$typography);
// @include mat.stepper-typography(gfk.$typography);
// @include mat.sort-typography(gfk.$typography);
@include mat.tabs-typography(gfk.$typography);
@include mat.toolbar-typography(gfk.$typography);
// @include mat.tooltip-typography(gfk.$typography);
@include mat.list-typography(gfk.$typography);
@include mat.option-typography(gfk.$typography);
// @include mat.optgroup-typography(gfk.$typography);
@include mat.snack-bar-typography(gfk.$typography);
// @include mat.tree-typography(gfk.$typography);
//=================================================USE INSTEAD/=========================================================

//=================================================THEME FILES/=========================================================
@import "styles/theme/gfk-light.theme";
//=================================================/THEME FILES=========================================================

//=================================================/CUSTOM GLOBAL CSS===================================================
/* You can add global styles to this file, and also import other style files */
@import 'assets/fonts/font';
@import 'assets/fonts/icons/icons-font';

//html, body { height: 100%; }

.gfk-typography {
  @include gfk.typography-base(gfk.$typography);
}

html,
body {
  @extend %font-body;
  height: auto;
  background-color: gfk-style.$grey-100;
}

// we use fake button in app.component instead
#atlwdg-trigger {
  display: none;
}

.progress-spinner {
  display: flex;
  width: 49px;
  justify-content: flex-end;
  align-items: center;
}

.table-grid {
  padding-top: 5px;
}

.tab-container {
  overflow: auto;
}

.table-height {
  height: calc(100vh - 360px);
}

.columnHeader {
  background-color: #E5E8E8;
  height: 30px !important;
  font-weight: 800;
}

.each-slice-top-headbar {
  padding-top: 50px;
  display: flex;
  justify-content: space-between;
  padding-left: 23px;
}

//=================================================CUSTOM GLOBAL CSS/===================================================
.sub-nav .page-name h1 {
  margin-bottom: 0 !important;
}

.refresh-button-right {
  float: right;
  padding: 1px 20px;
  margin-top: 5px !important;
  font-family: 'Lato';
}

.clear-all-filters {
  color: gfk-style.$brand;
  font-weight: gfk-style.$font-weight-heavy;
  cursor: pointer;
  display: flex;
  align-items: flex-end;
}

.filter-options {
  height: 45px;
  display: flex;
  justify-content: space-between;
}

.min-height-quickdialog {
  min-height: 120px !important;
}

.gfk-secondary-header {
  .gfk-nav {
    &.sub-nav {
      div {
        flex-direction: column;
        align-items: flex-start;
        padding: 0px 12px;
      }
      .nav-tab {
        line-height: 2.5rem;
        padding: 8px 0;
        border-bottom: 0 none;
        color: #9499a3;
      }
      .page-name h3 {
        font-size: 1.5rem;
        color: #5c626e;
        font-weight: 400;
      }
    }
  }
}

.gfk-nav {
  nav {
    .logo-container {
      padding: 0px 0px;
      display: flex;
    }
  }
}

.gfk-top-header {
  .gfk-nav {
    nav {
      ul {
        li {
          .logo-container {
            display: flex !important;
            padding: 0px !important;
          }
        }
      }
    }
  }
}


.filter-tray-open{
  width: 78.5%;
}

.filter-tray-close{
  width: 100%;
}

.alignleft {
  float: left;
}

.alignRight {
  float: right;
}

.width-100{
  width: 100%;
}

.toggle-button {
  display: flex;
  justify-content: space-between;
  flex-direction: row;
  width: 100%;
  align-items: center;
}

.filter-area {
  display: flex;
  flex-wrap: wrap;
  padding-left: 13px;
}

.flexdiv-buttons {
  display: flex;
  justify-content: flex-end;
  align-items: flex-end;
  padding-right: 12px;
}

.jira-button-margin-right {
  margin-right: 10px !important;
}

.refreshedTime{
	width: 40%;
	display: flex;
	align-items: center;
	justify-content: end;
	padding: 8px;
}

.d-inline-block{
  display: inline-block;
}

.sidebar-container {
  width: 100% !important;
  .sidebar-header {
    padding: 16px 16px !important;
  }
  .sidebar-content {
    overflow-y: scroll;
    height: calc(100vh - 170px);
  }
  .sidebar-title {
    line-height: normal;
    color: #9499a3!important;
    font-size: 24px;
    font-weight: 400!important;
  }
  .dot-flashing {
    position: absolute !important;
    top: 20px !important;
    left: 90px !important;
  }
}

.eds-input {
  .mat-form-field-wrapper {
    background-color: white !important;
    padding-bottom: 0px !important;
    margin-bottom: 12px !important;
    border: 1px solid #bbc0c9 !important;
    border-radius: 4px !important;
  }

  .mat-form-field-appearance-outline .mat-form-field-infix {
    padding: 3px 0px 8px 0px !important
  }

  .chip-list {
    margin-bottom: 5px !important;
  }

  .mat-chip-list {
    font-size: 14px !important;
  }

  .gfk-text-input.size-default {
    font-size: 14px !important;
  }

  .history-filter {
    font-size: 14px !important;
  }
}

.user-profile li {
  font-size: .7143rem !important;
}

.text-xs {
  font-size: 0.754rem !important;
}

.mat-paginator-sticky {
  bottom: 0px;
  position: sticky;
  z-index: 10;
}