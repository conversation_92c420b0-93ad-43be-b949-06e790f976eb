<header #TopNavHeader class="gfk-top-header nav-bg flex items-center text-xs">
	<gfk-nav landingUrl="https://home.de.vpc1445.in.gfk.com/" [externalLinks]="false" [appName]="TestApp">
		<a
			*gfkNavItem
			[ngClass]="
				currentTab === 'publish-republish' ||
				currentTab === 'migration-rule-runs' ||
				currentTab === 'data-order' ||
				currentTab === 'ld-general'
					? 'nav-tab tab-active'
					: 'nav-tab'
			"
			routerLinkActive="tab-active"
			(click)="tabActive('publish-republish', 'Monitor DWH Process', true)"
			>Monitor DWH Process</a
		>
		<a
			class="nav-tab"
			[ngClass]="currentTab === 'bcr-reworks' || currentTab === 'feature-move' ? 'nav-tab tab-active' : 'nav-tab'"
			*gfkNavItem
			routerLinkActive="tab-active"
			(click)="tabActive('bcr-reworks', 'DWH Global Process', true)"
			>DWH Global Process</a
		>
		<a
			class="nav-tab"
			[ngClass]="currentTab === 'dwh-qc' || currentTab === 'dwhrelease' || currentTab === 'logging-qc' ? 'nav-tab tab-active' : 'nav-tab'"
			*gfkNavItem
			routerLinkActive="tab-active"
			(click)="tabActive('dwh-qc', 'QC Monitoring', true)"
			>QC Monitoring</a
		>
		<a
			class="nav-tab"
			[ngClass]="currentTab === 'shop-master-data' || currentTab === 'item-master-data' ? 'nav-tab tab-active' : 'nav-tab'"
			*gfkNavItem
			routerLinkActive="tab-active"
			(click)="tabActive('shop-master-data', 'Monitor Master Data', true)"
			>Monitor Master Data</a
		>
		<a
			class="nav-tab"
			[ngClass]="
				currentTab === 'coverage-imports' || currentTab === 'csv-imports' || currentTab === 'ranged-weighting-import'
					? 'nav-tab tab-active'
					: 'nav-tab'
			"
			*gfkNavItem
			routerLinkActive="tab-active"
			(click)="tabActive('coverage-imports', 'Monitor Data Import', true)"
			>Monitor Data Import</a
		>
		<a
			class="nav-tab"
			[ngClass]="currentTab === 'export' ? 'nav-tab tab-active' : 'nav-tab'"
			*gfkNavItem
			routerLinkActive="tab-active"
			(click)="tabActive('export', 'Monitor Data Export', true)"
			>Monitor Data Export</a
		>
		<div *gfkSection class="flex items-center">
			<a (click)="openJiraFeedback($event)" class="text-interactive no-underline headline">Provide feedback</a>
			<ng-container *ngIf="userName$ | async as userName; else notLoggedIn">
				<gfk-user-profile-settings
					[userName]="userName"
					[isLoggedIn]="true"
					helpUrl="https://gfk.sharepoint.com/sites/MIOPS/SitePages/Load-Monitor-X.aspx"
					(onLogout)="signOut()"
				>
				</gfk-user-profile-settings>
			</ng-container>
			<ng-template #notLoggedIn>
				<gfk-user-profile-settings
					[userName]="'Not logged in (Refresh)'"
					[isLoggedIn]="true"
					(onLogout)="signIn()"
					helpUrl="https://gfk.sharepoint.com/sites/MIOPS/SitePages/Load-Monitor-X.aspx"
				>
				</gfk-user-profile-settings>
			</ng-template>
		</div>
	</gfk-nav>
</header>
<header class="gfk-secondary-header" id="gfk-secondary-header">
	<div class="flex items-center text-xs">
		<gfk-sub-nav
			[pageName]="pageName"
			*ngIf="
				currentTab === 'publish-republish' ||
				currentTab === 'migration-rule-runs' ||
				currentTab === 'data-order' ||
				currentTab === 'ld-general'
			"
		>
			<a class="nav-tab" *gfkNavItem routerLink="/publish-republish" routerLinkActive="tab-active">Publish/Republish</a>
			<a class="nav-tab" *gfkNavItem routerLink="/migration-rule-runs" routerLinkActive="tab-active">Migration Rule Runs</a>
			<a class="nav-tab" *gfkNavItem routerLink="/data-order" routerLinkActive="tab-active">Data Order</a>
			<a class="nav-tab" *gfkNavItem routerLink="/ld-general" routerLinkActive="tab-active">LD General</a>
		</gfk-sub-nav>
		<gfk-sub-nav [pageName]="pageName" *ngIf="currentTab === 'bcr-reworks' || currentTab === 'feature-move'">
			<a class="nav-tab" *gfkNavItem routerLink="/bcr-reworks" routerLinkActive="tab-active">BCR Reworks</a>
			<a class="nav-tab" *gfkNavItem routerLink="/feature-move" routerLinkActive="tab-active">Feature Move</a>
		</gfk-sub-nav>
		<gfk-sub-nav [pageName]="pageName" *ngIf="currentTab === 'dwh-qc' || currentTab === 'dwhrelease' || currentTab === 'logging-qc'">
			<a class="nav-tab" *gfkNavItem routerLink="/dwh-qc" routerLinkActive="tab-active">DWH/QC</a>
			<a class="nav-tab" *gfkNavItem routerLink="/dwhrelease" routerLinkActive="tab-active">DWH Release</a>
			<a class="nav-tab" *gfkNavItem routerLink="/logging-qc" routerLinkActive="tab-active">Logging QC</a>
		</gfk-sub-nav>
		<gfk-sub-nav [pageName]="pageName" *ngIf="currentTab === 'shop-master-data' || currentTab === 'item-master-data'">
			<a class="nav-tab" *gfkNavItem routerLink="/shop-master-data" routerLinkActive="tab-active">Shop Master Data</a>
			<a class="nav-tab" *gfkNavItem routerLink="/item-master-data" routerLinkActive="tab-active">Item Master Data</a>
		</gfk-sub-nav>
		<gfk-sub-nav
			[pageName]="pageName"
			*ngIf="currentTab === 'coverage-imports' || currentTab === 'csv-imports' || currentTab === 'ranged-weighting-import'"
		>
			<a class="nav-tab" *gfkNavItem routerLink="/coverage-imports" routerLinkActive="tab-active">Coverage Import</a>
			<a class="nav-tab" *gfkNavItem routerLink="/csv-imports" routerLinkActive="tab-active">CSV Import</a>
			<a class="nav-tab" *gfkNavItem routerLink="/ranged-weighting-import" routerLinkActive="tab-active">Ranged Weighting Import</a>
		</gfk-sub-nav>
		<gfk-sub-nav [pageName]="pageName" *ngIf="currentTab === 'export'">
			<a class="nav-tab" *gfkNavItem routerLink="/export" routerLinkActive="tab-active">Export</a>
		</gfk-sub-nav>
	</div>
</header>
<main [style.margin-top]="this.getMarginTop(TopNavHeader.offsetHeight)">
	<router-outlet *ngIf="!isIframe"></router-outlet>
</main>