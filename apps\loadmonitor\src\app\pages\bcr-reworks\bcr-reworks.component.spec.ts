import { ComponentFixture, TestBed } from '@angular/core/testing';
import { BcrReworksComponent } from './bcr-reworks.component';
import { BcrReworksService } from '@loadmonitor/shared/services/bcr-reworks.service';
import { MatSnackBar } from '@angular/material/snack-bar';
import { MatDialog, MatDialogModule } from '@angular/material/dialog';
import { MatSnackBarModule } from '@angular/material/snack-bar';
import { FormBuilder } from '@angular/forms';
import { ChangeDetectorRef } from '@angular/core';
import { FilterService } from '@dwh/lmx-lib/src';
import { DatePipe } from '@angular/common';
import { ConfigService } from '@loadmonitor/shared/services/config.service';
import { FilterTrayCacheService } from '@loadmonitor/shared/services/filter-tray-cache.service';
import { of } from 'rxjs';

describe('BcrReworksComponent', () => {
    let component: BcrReworksComponent;
    let fixture: ComponentFixture<BcrReworksComponent>;

    beforeEach(async () => {
        await TestBed.configureTestingModule({
            imports: [
                MatDialogModule,
                MatSnackBarModule,
            ],
            declarations: [BcrReworksComponent],
            providers: [
                { provide: BcrReworksService, useValue: {} },
                { provide: MatSnackBar, useValue: {} },
                FormBuilder,
                ChangeDetectorRef,
                MatDialog,
                FilterService,
                DatePipe,
                ConfigService,
                FilterTrayCacheService,
            ]
        }).compileComponents();
    });

    beforeEach(() => {
        fixture = TestBed.createComponent(BcrReworksComponent);
        component = fixture.componentInstance;
        fixture.detectChanges();
    });

    it('should create the component', () => {
        expect(component).toBeTruthy();
    });
});
