import { ComponentFixture, TestBed } from '@angular/core/testing';
import { LoggingQcComponent } from './logging-qc.component';
import { LoggingQCService } from '@loadmonitor/shared/services/logging-qc.service';
import { MatSnackBar } from '@angular/material/snack-bar';
import { FormBuilder } from '@angular/forms';
import { FilterService } from '@dwh/lmx-lib/src';
import { FilterTrayCacheService } from '@loadmonitor/shared/services/filter-tray-cache.service';
import { of } from 'rxjs';
import { MatTableDataSource } from '@angular/material/table';
import { DatePipe } from '@angular/common';
import { ToggleButtonComponent } from '@gfk/ng-lib';
import { BehaviorSubject } from 'rxjs';

describe('LoggingQcComponent', () => {
    let component: LoggingQcComponent;
    let fixture: ComponentFixture<LoggingQcComponent>;
    let loggingQCService: jest.Mocked<LoggingQCService>;
    let snackBar: jest.Mocked<MatSnackBar>;
    let filterService: jest.Mocked<FilterService>;
    let filterTrayCacheService: jest.Mocked<FilterTrayCacheService>;
    let datePipe: jest.Mocked<DatePipe>;

    beforeEach(async () => {
        loggingQCService = {
            getAsync: jest.fn(),
        } as any;
        snackBar = {
            openFromComponent: jest.fn(),
        } as any;
        filterService = {
            setFilters: jest.fn(),
            filtersSelected$: new BehaviorSubject({}),
        } as any;
        filterTrayCacheService = {
            getCacheValueData: jest.fn(),
        } as any;
        datePipe = {
            transform: jest.fn(),
        } as any;

        await TestBed.configureTestingModule({
            declarations: [LoggingQcComponent, ToggleButtonComponent],
            providers: [
                { provide: LoggingQCService, useValue: loggingQCService },
                { provide: MatSnackBar, useValue: snackBar },
                { provide: FormBuilder, useClass: FormBuilder },
                { provide: FilterService, useValue: filterService },
                { provide: FilterTrayCacheService, useValue: filterTrayCacheService },
                { provide: DatePipe, useValue: datePipe },
            ],
        }).compileComponents();
    });

    beforeEach(() => {
        fixture = TestBed.createComponent(LoggingQcComponent);
        component = fixture.componentInstance;
        fixture.detectChanges();
    });

    it('should create the component', () => {
        expect(component).toBeTruthy();
    });    

    // Additional tests for other methods can be added here
});
