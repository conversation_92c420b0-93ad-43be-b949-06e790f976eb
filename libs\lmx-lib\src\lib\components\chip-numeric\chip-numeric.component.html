<mat-form-field class="chip-list" appearance="outline">
	<!-- <mat-label>{{ label }}</mat-label> -->
	<mat-chip-list #cl_ids aria-label="Ids selection">
		<mat-chip *ngFor="let item of ids" [selectable]="true" [removable]="true" (removed)="removeChips(item)">
			{{ item }}
			<mat-icon matChipRemove *ngIf="true">cancel</mat-icon>
		</mat-chip>
		<input
			[placeholder]="(!ids.length) ? placeholder : ''"
			[matChipInputFor]="cl_ids"
			[matChipInputSeparatorKeyCodes]="separatorKeysCodes"
			[formControl]="numericControl"
			[matChipInputAddOnBlur]="true"
			(matChipInputTokenEnd)="addChips($event)"
			(paste)="paste($event)"
			[id]="id"
		/>
	</mat-chip-list>
</mat-form-field>
<mat-error class="mat-small" *ngIf="numericControl.errors">Only positive numeric values are allowed</mat-error>