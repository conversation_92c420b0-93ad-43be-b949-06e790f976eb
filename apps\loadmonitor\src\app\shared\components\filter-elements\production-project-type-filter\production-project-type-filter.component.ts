import { ProductionProjectTypeService } from '@loadmonitor/shared/services/production-project-type.service';
import { JobTypes } from '@loadmonitor/shared/jobTypes';
import { Component, ElementRef, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges, ViewChild } from '@angular/core';
import { FormControl } from '@angular/forms';
import { ProductionProjectType } from '@loadmonitor/shared/interfaces/ProductionProjectType';
import { Observable } from 'rxjs/internal/Observable';
import { debounceTime, map, startWith } from 'rxjs/operators';
import { MatAutocompleteSelectedEvent } from '@angular/material/autocomplete';
import { get, keyBy, merge, values } from 'lodash';
import { COMMA, ENTER } from '@angular/cdk/keycodes';

const EMPTY = null;

@Component({
  selector: 'lm-production-project-type-filter',
  templateUrl: './production-project-type-filter.component.html',
  styleUrls: ['./production-project-type-filter.component.scss'],
})
export class ProductionProjectTypeFilterComponent implements OnInit, OnChanges {
  @Input() jobType: JobTypes = JobTypes.none;
  @Input() productionProjectTypeIds: number[] = [];
  @Output() valueChanges = new EventEmitter<ProductionProjectType[]>();
  @ViewChild('productionProjectTypeIdsInput')
  productionProjectTypeIdsInput!: ElementRef<HTMLInputElement>;

  // Mat-Chips - ProductionProjectType
  productionProjectTypesCtrl = new FormControl(EMPTY);
  filteredPPTs$!: Observable<ProductionProjectType[]>;
  selectedPPTs$!: Observable<ProductionProjectType[]>;
  allPPTs: ProductionProjectType[] = [];


  //Tag-Chip-List
  readonly separatorKeysCodes = [ENTER, COMMA] as const;


  constructor(private ProductionProjectTypeService: ProductionProjectTypeService) { }

  ngOnInit(): void {
    this.getProductionProjectType();
    this.filteredPPTs$ = this.productionProjectTypesCtrl.valueChanges.pipe(
      startWith(EMPTY),
      debounceTime(300),
      map((ProductionProjectType) =>
        this.filterMatchingPPTs(get(ProductionProjectType, 'name', ProductionProjectType)),
      ),
    );

    //Todo this should be a BehaviorSubject or something, not pipe() on unrelated observable
    this.selectedPPTs$ = this.productionProjectTypesCtrl.valueChanges.pipe(
      map(() =>
        this.filterSelectedPPTs(),
      ),
    );

    this.selectedPPTs$.subscribe((productionProjectTypeIds) => {
      this.valueChanges.emit(productionProjectTypeIds);
    });
  }

  ngOnChanges(changes: SimpleChanges){
    this.allPPTs.forEach(function(i){ i.selected=false })
    this.setSelectedPPTIds(changes.productionProjectTypeIds.currentValue);
  }

 public removeAllProductionProjectType(){
    this.filterSelectedPPTs().map((prod) => prod.selected = false);
    this.refreshSuggestionsAndEmptyInput();
  }

  getProductionProjectType(): void {
    this.ProductionProjectTypeService.getAsync(this.jobType).subscribe((result) => {
      const merged = merge(keyBy(this.allPPTs, 'id'), keyBy(result, 'id'));
      this.allPPTs = values(merged);
      this.setSelectedPPTIds(this.productionProjectTypeIds);
      this.refreshSuggestionsAndEmptyInput();
    });
  }

  // ProductionProjectType Chips

  removeProductionProjectTypeChips(item: ProductionProjectType): void {
    this.toggleSelection(item);
  }

  optionClicked(event: Event, item: ProductionProjectType): void {
    event.stopPropagation();
    this.toggleSelection(item);
  }

  toggleSelection(item: ProductionProjectType): void {
    item.selected = !item.selected;
    this.refreshSuggestionsAndEmptyInput();
  }

  selectedProductionProjectTypeChips(event: MatAutocompleteSelectedEvent): void {
    this.toggleSelection(event.option.value);
  }

  refreshSuggestionsAndEmptyInput(): void {
    this.productionProjectTypesCtrl.reset();
    this.productionProjectTypeIdsInput.nativeElement.value = ''; // do not remove, because material is stupid
  }

  filterSelectedPPTs(): ProductionProjectType[] {
    return this.allPPTs.filter(
      (PPT) => PPT.selected,
    );
  }

  filterMatchingPPTs(value: string | null): ProductionProjectType[] {
    if (value === '' || value === null) {
      return this.allPPTs;
    } else {
      const filterValue = value.toLowerCase();
      return this.allPPTs.filter(
        (PPT) => PPT.name.toLowerCase().indexOf(filterValue) === 0,
      );
    }
  }

  public getSelectedPPTIds() {
    return this.filterSelectedPPTs().map((PPT) => PPT.id);
  }

  public getSelectedPPTNames() {
    return this.filterSelectedPPTs().map((PPT) => PPT.name);
  }

  public setSelectedPPTIds(PPTIds: number[]): void {
    PPTIds?.forEach((PPTid: number) => {
      const found = this.allPPTs.find((PPT: ProductionProjectType) => PPT.id === PPTid);
      if (found) {
        found.selected = true;
      } else {
        this.allPPTs.push({id: PPTid, name: 'Loading...', selected: true})
      }
    });
    this.refreshSuggestionsAndEmptyInput();
  }
}
