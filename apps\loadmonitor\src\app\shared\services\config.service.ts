import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root',
})
export class ConfigService {
  private config = window.CONFIG;

  getApiUrl(): string {
    const apiBaseUrl = this.config.get('apiBaseUrl');
    const apiVersion = this.config.get('apiVersion');
    let apiUrl = 'api/lmx'+`${apiBaseUrl}/${apiVersion}`
    apiUrl ='/api/lmx/api/v1'
    return apiUrl;
  }

  getJeeApiUrl(): string {
    const jeeApiBaseUrl =  this.config.get('jeeApiBaseUrl');
    const jeeApiVersion = this.config.get('jeeApiVersion');
    let jeeApiUrl = 'api/lmx'+`${jeeApiBaseUrl}/${jeeApiVersion}`
    jeeApiUrl ='/api/jee/api/v2'
    return jeeApiUrl;
  }

  getCountryApiUrl():string{
    const countryApiUrl =  this.config.get('countryApiBaseUrl');
    const apiVersion = this.config.get('apiVersion');
    const countApiUrl =  `${countryApiUrl}/${apiVersion}`;
    return countApiUrl;
  }

  getPeriodicityApiUrl(): string {
    const periodicityApiBaseUrl =  this.config.get('periodicityApiBaseUrl');
    const apiVersion = this.config.get('apiVersion');
    return `${periodicityApiBaseUrl}/${apiVersion}`;
  }

  getVersion() {
    return this.config.get('version');
  }
}
