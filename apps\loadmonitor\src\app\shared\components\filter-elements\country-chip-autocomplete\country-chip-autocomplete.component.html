<mat-form-field class="chip-list" appearance="outline">
    <mat-label>Country </mat-label>
    <mat-chip-list class="chip-list-wrapper" #chipListCountry aria-label="Countries selection">
      <mat-chip *ngFor="let country of selectedCountryIds|async" [selectable]="true" [removable]="true"
        (removed)="removeCountryChips(country)">
        {{ country.name }}
        <mat-icon matChipRemove>cancel</mat-icon>
      </mat-chip>
      <input placeholder="New ..." #countryIdsInput [formControl]="countryIdsCtrl" [matAutocomplete]="autoCountry"
        [matChipInputFor]="chipListCountry" [matChipInputSeparatorKeyCodes]="separatorKeysCodes"
        class="js-filter-inp-country" />
    </mat-chip-list>
    <mat-autocomplete #autoCountry="matAutocomplete" [autoActiveFirstOption]="true" (optionSelected)="selectedCountryChips($event)">
      <mat-option *ngFor="let country of filteredCountryIds | async" [value]="country">
        <div (click)="optionClicked($event, country)">
          <mat-checkbox class="option-checkbox" [checked]="country.selected" (change)="toggleSelection(country)"
            (click)="$event.stopPropagation()">
            {{ country.name }}
          </mat-checkbox>
        </div>
      </mat-option>
    </mat-autocomplete>
  </mat-form-field>
  