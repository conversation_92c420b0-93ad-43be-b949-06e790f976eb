import { Component, ElementRef, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges, ViewChild } from '@angular/core';
import { Status } from '@loadmonitor/shared/interfaces/Status';
import { COMMA, ENTER } from '@angular/cdk/keycodes';
import { FormControl } from '@angular/forms';
import { Observable } from 'rxjs/internal/Observable';
import { StatusService } from '@loadmonitor/shared/services/status.service';
import { debounceTime, map, startWith } from 'rxjs/operators';
import { get, keyBy, merge, values } from 'lodash';

const EMPTY = null;

@Component({
  selector: 'lm-status-chip-autocomplete',
  templateUrl: './status-chip-autocomplete.component.html',
  styleUrls: ['./status-chip-autocomplete.component.scss']
})
export class StatusChipAutocompleteComponent implements OnInit, OnChanges {
  @Input() sliceName = "";
  @Input() statusIds: number[] = [];
  @Output() valueChanges = new EventEmitter<Status[]>();
  @ViewChild('statusIdsInput')
  statusIdsInput!: ElementRef<HTMLInputElement>;

  //Tag-Chip-List
  readonly separatorKeysCodes = [ENTER, COMMA] as const;

  // Mat-Chips - Status
  statusIdsCtrl = new FormControl(EMPTY);
  filteredStatusIds!: Observable<Status[]>;
  selectedStatusIds!: Observable<Status[]>;
  allStatusIds: Status[] = [];

  constructor(
    private statusService: StatusService
  ) { }

  ngOnInit(): void {
    this.fillStatuses();
    this.filteredStatusIds = this.statusIdsCtrl.valueChanges.pipe(
      startWith(EMPTY),
      debounceTime(300),
      map((status) =>
        this.setStatusFilter(get(status, 'description', status)),
      ),
    );

    //Todo this should be a BehaviorSubject or something, not pipe() on unrelated observable
    this.selectedStatusIds = this.statusIdsCtrl.valueChanges.pipe(
      map(() =>
        this.filterSelectedStatusIds(),
      ),
    );

    this.selectedStatusIds.subscribe((statusIds) => {
      this.valueChanges.emit(statusIds);
    });
  }

  public removeAllStatus() {
    this.filterSelectedStatusIds().map((status) => status.selected = false);
    this.refreshSuggestionsAndEmptyInput();
  }

  fillStatuses(): void {
    this.statusService.getAsync(this.sliceName).subscribe((result) => {
      const merged = merge(keyBy(this.allStatusIds, 'id'), keyBy(result, 'id'));
      this.allStatusIds = values(merged);

      // add some special Status elements only for exports slice
      if (this.sliceName === "Exports") {
        const exportedState = this.allStatusIds.filter((status) => status.description.toLowerCase().indexOf('exported') === 0);
        const sendingState = this.allStatusIds.filter((status) => status.description.toLowerCase().indexOf('sending') === 0);

        if (exportedState.length === 1 && sendingState.length === 1) {
          const specialStatusArray = this.getSpecialStatusesForExport(exportedState[0].id, sendingState[0].id);
          if (specialStatusArray) {
            specialStatusArray.forEach((element) => {
              this.allStatusIds.push(element);
            })
          }
        }
      }
      this.allStatusIds.sort((a, b) => a.description.localeCompare(b.description));
      this.setSelectedStatusIds(this.statusIds);
      this.refreshSuggestionsAndEmptyInput();
    });
  }

  getSpecialStatusesForExport(exportId: number, sendingId: number): Status[] {
    const arrStatus: Status[] = new Array<Status>();
    const sentObj = { description: "Sent", processStageId: 1 };
    const sendingObj = { description: "Sending", processStageId: 2 };
    const errorObj = { description: "Error", processStageId: 4 };
    const exportedText = "Exported + ";

    // Sent
    arrStatus.push(<Status>({ description: `${exportedText}${sentObj.description}`, id: exportId+1000, selected: false, processStageId: sentObj.processStageId }));
    // Sending
    arrStatus.push(<Status>({ description: `${exportedText}${sendingObj.description}`, id: exportId + 2000, selected: false, processStageId: sendingObj.processStageId }));
    //Error
    arrStatus.push(<Status>({ description: `${exportedText}${errorObj.description}`, id: exportId + 3000, selected: false, processStageId: errorObj.processStageId }));

    // Sending
    arrStatus.push(<Status>({ description: `Sending + ${sendingObj.description}`, id: sendingId + 5000, selected: false, processStageId: sendingObj.processStageId }));

    return arrStatus;
  }

  // Status Chips
  removeStatusChips(item: Status): void {
    this.toggleSelection(item);
  }

  optionClicked(event: Event, item: Status) {
    event.stopPropagation();
    this.toggleSelection(item);
  }

  toggleSelection(item: Status) {
    item.selected = !item.selected;
    this.refreshSuggestionsAndEmptyInput();
  }

  refreshSuggestionsAndEmptyInput() {
    this.statusIdsCtrl.reset();
    this.statusIdsInput.nativeElement.value = '';
  }

  private setStatusFilter(value: string): Status[] {
    if (value === '' || value === null) {
      return this.allStatusIds;
    }
    const filterValue = value.toLowerCase();

    return this.allStatusIds.filter(
      (status) => status.description.toLowerCase().indexOf(filterValue) === 0
    );
  }

  public getSelectedStatusIds() {
    return this.filterSelectedStatusIds().map((status) => status.id);
  }

  public getSelectedStatusDescription() {
    return this.filterSelectedStatusIds().map((status) => status.description);
  }

  public getSelectedProcessStageIds() {
    return this.filterStatusWithProcessStageId().map((status) => status.processStageId);
  }

  filterSelectedStatusIds(): Status[] {
    return this.allStatusIds.filter(
      (status) => status.selected,
    );
  }

 public filterSelectedStatusDescription(): string[] {
    return this.allStatusIds.filter(
      (status) => status.selected,
    ).map((s)=> s.description);
  }

  filterStatusWithProcessStageId(): Status[] {
    return this.allStatusIds.filter(
      (status) => status.selected && status.processStageId !== undefined
    )
  }

  filterMatchingStatusIds(value: string | null): Status[] {
    if (value === '' || value === null) {
      return this.allStatusIds;
    } else {
      const filterValue = value.toLowerCase();
      return this.allStatusIds.filter(
        (status) => status.description.toLowerCase().indexOf(filterValue) === 0,
      );
    }
  }

  public setSelectedStatusIds(statusIds: number[]): void {
    statusIds?.forEach((statusId: number) => {
      const found = this.allStatusIds.find((status: Status) => status.id === statusId);
      if (found) {
        found.selected = true;
      } else {
        this.allStatusIds.push({
          id: statusId, description: 'Loading...', selected: true,
          processStageId: 0
        })
      }
    });
    this.refreshSuggestionsAndEmptyInput();
  }

  ngOnChanges(changes: SimpleChanges){
    this.allStatusIds.forEach(function(i){ i.selected=false })
    this.setSelectedStatusIds(changes.statusIds.currentValue);
  }
}
