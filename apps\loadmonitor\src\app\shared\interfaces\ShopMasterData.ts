import { DetailMenuConfiguration } from "@loadmonitor/shared/interfaces/DetailMenuConfiguration";

export interface ShopMasterData {
    unitId: number;
    statusDesc: string;
    outletMasterDataName: string;
    createdBy: string;
    created: Date | string;
    finished: Date | string;
    started: Date | string;
    errorMessage: string;
    detailMenuConfig?: DetailMenuConfiguration[];
    isSelected?: boolean;
    isExpanded?: boolean;
  }
