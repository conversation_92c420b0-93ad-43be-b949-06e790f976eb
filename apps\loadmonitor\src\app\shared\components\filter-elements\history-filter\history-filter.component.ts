import { Component, ElementRef, Input, ViewChild, OnInit, Output, EventEmitter } from '@angular/core';
import { FormControl, FormGroup } from '@angular/forms';
import { DateAdapter, MAT_DATE_FORMATS, MAT_DATE_LOCALE } from '@angular/material/core';
import { MomentDateAdapter } from '@angular/material-moment-adapter';
import * as moment from 'moment';
import { Observable } from 'rxjs';


export const MY_FORMATS = {
  parse: {
    dateInput: 'YYYY-MM-DD',
  },
  display: {
    dateInput: 'YYYY-MM-DD',
    monthYearLabel: 'YYYY',
    dateA11yLabel: 'LL',
    monthYearA11yLabel: 'YYYY',
  },
};

@Component({
  selector: 'lm-history-filter',
  templateUrl: './history-filter.component.html',
  styleUrls: ['./history-filter.component.scss'],
  providers: [
    { provide: DateAdapter, useClass: MomentDateAdapter, deps: [MAT_DATE_LOCALE] },

    { provide: MAT_DATE_FORMATS, useValue: MY_FORMATS },
  ],
})
export class HistoryFilterComponent implements OnInit {
  @Input() selected = '1';
  @Input() startDate?: Date;
  @Input() endDate?: any;
  @Input() isInfoIconShow?: boolean =false;

  @Input() icon = 'help-outline';
  @ViewChild('dateRangeStart')
  dateRangeStart!: ElementRef<HTMLInputElement>;

  @ViewChild('dateRangeEnd')
  dateRangeEnd!: ElementRef<HTMLInputElement>;

  dateRange!: FormGroup;
  selecteddates$!: Observable<Date[]>;

  @Output() valueChanges = new EventEmitter<any[]>();

  isToClearDateRange = false;
  IsDateRangeDisabled = false;

  ngOnInit() {
    //Removing offset, setting to zulu
    if (this.endDate != null) {
      this.endDate = new Date(this.endDate);
      this.endDate?.setMinutes(this.endDate.getMinutes() + this.endDate.getTimezoneOffset());
    }

    this.dateRange = new FormGroup({
      start: this.startDate ? new FormControl(moment(this.startDate)) : new FormControl(moment(null)),
      end: this.endDate ? new FormControl(moment(this.endDate)) : new FormControl(moment(null)),
    });
    this.changeHistory(this.selected);

    this.dateRange.valueChanges.subscribe((historydates) => {
      this.valueChanges.emit(historydates);
    });
  }

  public ResetHistoryFilter() {
    this.startDate = undefined;
    this.endDate =undefined;
    if (this.endDate != null) {
      this.endDate = new Date(this.endDate);
      this.endDate?.setMinutes(this.endDate.getMinutes() + this.endDate.getTimezoneOffset());
    }

    this.dateRange = new FormGroup({
      start: this.startDate ? new FormControl(moment(this.startDate)) : new FormControl(moment(null)),
      end: this.endDate ? new FormControl(moment(this.endDate)) : new FormControl(moment(null)),
    });
    this.selected = '1';
    this.changeHistory(this.selected);
  }

  changeHistory(historyId: any) {
    this.IsDateRangeDisabled = false;

    // Just for information:
    // we use Dat.UTC here, but the result is not in UTC format if you debug it
    // but in the request we have the UTC time and that's what we want

    if (historyId == '1') {
      if (this.isToClearDateRange) {
        this.startDate = undefined;
        this.endDate = undefined;
        this.setRange(this.startDate, this.endDate);
        this.dateRange.setValue({ start: new FormControl(moment(null)), end: new FormControl(moment(null)) });
        this.isToClearDateRange = false;
        this.IsDateRangeDisabled = false;
      }
      else {
        this.IsDateRangeDisabled = false;
        this.setRange(this.startDate, this.endDate);
      }
    }
    if (historyId == '2') {
      const date = new Date();
      this.startDate = new Date(Date.UTC(date.getFullYear(), date.getMonth(), date.getDate(), 0, 0, 0, 0));
      this.endDate = new Date(Date.UTC(date.getFullYear(), date.getMonth(), date.getDate(), 23, 59, 59, 999));
      this.IsDateRangeDisabled = true;
      this.convertEndDatetoZeroTZForPicker();
    }
    if (historyId == '3') {
      const date = new Date();
      this.startDate = new Date(Date.UTC(date.getFullYear(), date.getMonth(), date.getDate() - 2, 0, 0, 0, 0));
      this.endDate = new Date(Date.UTC(date.getFullYear(), date.getMonth(), date.getDate(), 23, 59, 59, 999));
      this.IsDateRangeDisabled = true;
      this.convertEndDatetoZeroTZForPicker();
    }
  }

  private convertEndDatetoZeroTZForPicker() {
    const tempEndDate = new Date(this.endDate);
    tempEndDate.setMinutes(tempEndDate.getMinutes() + tempEndDate.getTimezoneOffset());

    this.dateRange.setValue({ start: moment(this.startDate), end: moment(tempEndDate) });
    this.isToClearDateRange = true;
  }

  dateChange() {
    this.setRange(
      this.dateRange.value.start ? new Date(this.dateRange.value.start) : undefined,
      this.dateRange.value.end ? new Date(this.dateRange.value.end) : undefined,
    );
    this.IsDateRangeDisabled = false;
    
  }

  setRange(start: any, end: any) {

    if (typeof start == 'string') {
      start = new Date(start);
    }

    //Removing offset, setting to zulu
    if (typeof end == 'string') { //exec below only when input coming from cache; cache stores string not date
      end = new Date(end);
      end.setMinutes(end.getMinutes() + end.getTimezoneOffset());
    }
    //

    this.startDate = start != null && start != undefined ? new Date(Date.UTC(start.getFullYear(), start.getMonth(), start.getDate(), 0, 0, 0, 0)) : undefined;
    this.endDate = end != null && end != undefined ? new Date(Date.UTC(end.getFullYear(), end.getMonth(), end.getDate(), 23, 59, 59, 999)) : undefined;
    if(this.startDate && this.endDate){
      this.convertEndDatetoZeroTZForPicker();
    }
  }
}
