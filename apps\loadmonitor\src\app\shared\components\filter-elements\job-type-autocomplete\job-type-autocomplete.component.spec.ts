import { ComponentFixture, TestBed } from '@angular/core/testing';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { SharedModule } from '@loadmonitor/shared/shared.module';

import { JobTypeAutocompleteComponent } from './job-type-autocomplete.component';

describe('JobTypeAutocompleteComponent', () => {
  let component: JobTypeAutocompleteComponent;
  let fixture: ComponentFixture<JobTypeAutocompleteComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [JobTypeAutocompleteComponent],
      imports: [SharedModule, NoopAnimationsModule],
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(JobTypeAutocompleteComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
