import { Injectable } from '@angular/core';
import { Clipboard } from '@angular/cdk/clipboard';
import { HttpParams } from '@angular/common/http';
import { Subject } from 'rxjs';
import { ParentPropertyPage } from '@loadmonitor/shared/interfaces/ParentPropertyPage';
import { DatePipe } from '@angular/common';
import { SettingsConstants } from '@loadmonitor/shared/settings.constants';
import { JobTypes } from '@loadmonitor/shared/jobTypes';
import { Observable } from 'rxjs';

export interface FloatingModel
{
  data:ParentPropertyPage[],
  jobType:JobTypes
}

@Injectable({
  providedIn: 'root'
})

export class HelperService {

  public floatingPanelData$ = new Subject<FloatingModel>();
  private sectorDataForCategory$ = new Subject<any>();
  private categoryDataForProductGroup$ = new Subject<any>();

  constructor(private clipboard: Clipboard,
              private datePipe: DatePipe) {}

  sendSelectedSectors(sectorIds: number[]) {
      this.sectorDataForCategory$.next({ sectorIds: sectorIds });
  }

  getSelectedSectors(): Observable<any> {
      return this.sectorDataForCategory$.asObservable();
  }

  sendSelectedCategories(categoryIds: number[]) {
      this.categoryDataForProductGroup$.next({ categoryIds: categoryIds });
  }

  getSelectedCategories(): Observable<any> {
      return this.categoryDataForProductGroup$.asObservable();
  }


  copyTextToClipboard(text: string) {
    this.clipboard.copy(text);
  }

  public createLink(destinationFormName: string, parameters: any): void {
    const params = new HttpParams({fromObject: parameters}).toString();
    let url = 'http://dwhSuite';

    if (destinationFormName !== null) {
      url = `${url}/${destinationFormName}`;
    }

    if (params !== null) {
      url = `${url}?${params}`;
    }

    this.copyTextToClipboard(url);
  }

  calculateDuration(startingTime: Date | string, finishingTime: Date | string, onlyTime: boolean): string | null {
    const dayInMilliseconds = 86400000;
    if (!startingTime) {
      return 'Did not start';
    } else if (!finishingTime) {
      return 'Did not finish';
    } else {
      if (!(startingTime instanceof Date)) {
        startingTime = new Date(startingTime);
      }

      if (!(finishingTime instanceof Date)) {
        finishingTime = new Date(finishingTime);
      }

      const runtime = Math.abs(finishingTime.getTime() - startingTime.getTime());
      if (onlyTime && runtime >= dayInMilliseconds) {
        return 'More than 24h';
      } else if (!onlyTime && runtime >= dayInMilliseconds) {
        return Math.floor(runtime / dayInMilliseconds) + 'd ' + this.datePipe.transform(runtime, SettingsConstants.FORMAT_ONLY_TIME, '+0000');
      } else {
        //ranges should not be adjusted to a local timezone
        return this.datePipe.transform(runtime, SettingsConstants.FORMAT_ONLY_TIME, '+0000');
      }
    }

  }
}
