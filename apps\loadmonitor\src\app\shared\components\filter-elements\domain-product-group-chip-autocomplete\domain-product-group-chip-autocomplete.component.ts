import {
  Component,
  ElementRef,
  ViewChild,
  EventEmitter,
  Output,
  OnInit,
  Input,
  SimpleChanges,
  OnChanges
} from '@angular/core';
import { FormControl } from '@angular/forms';
import { Observable } from 'rxjs/internal/Observable';
import { debounceTime, map, startWith } from 'rxjs/operators';
import { get } from 'lodash';
import { DomainProductGroups } from '@loadmonitor/shared/interfaces/DomainProductGroups';
import { DomainProductGroupService } from '@loadmonitor/shared/services/domainProductgroup.service';
import { COMMA, ENTER } from '@angular/cdk/keycodes';
import { HelperService } from '@loadmonitor/shared/services/helper.service';
import { Subscription } from 'rxjs';
import { MatAutocompleteSelectedEvent } from '@angular/material/autocomplete';

const EMPTY = null;

@Component({
  selector: 'lm-domain-product-group-chip-autocomplete',
  templateUrl: './domain-product-group-chip-autocomplete.component.html',
  styleUrls: ['./domain-product-group-chip-autocomplete.component.scss']
})
export class DomainProductGroupChipAutocompleteComponent implements OnInit,OnChanges {
  @Output() addProductgroupEvent = new EventEmitter<DomainProductGroups>();
  @Output() removeProductgroupEvent = new EventEmitter<DomainProductGroups>();

  @ViewChild('domainProductGroupIdsInput')
  domainProductGroupIdsInput!: ElementRef<HTMLInputElement>;

  @Input() cachedDomainProductGroups: number[] = [];

  domainProductGroupIds!: Observable<DomainProductGroups[]>;
  categoryIds!: number[];
  sectorIds!: number[];

  dpgIdsPerSectorCategory: DomainProductGroups[] = [];

  //Tag-Chip-List
  readonly separatorKeysCodes = [ENTER, COMMA] as const;

  // Mat-Chips - Domain Product Groups
  domainProductGroupIdsCtrl = new FormControl();
  filteredDomainProductGroupIds!: Observable<DomainProductGroups[]>;
  allDomainProductGroupIds: DomainProductGroups[] = [];

  private subscription!: Subscription | undefined;

  constructor(
    private domainProductGroupService: DomainProductGroupService,
    private util: HelperService
  ) {
    this.categoryIds = [];
    this.sectorIds = [];
    this.fillDomainProductGroups();
    this.filteredDomainProductGroupIds =
      this.domainProductGroupIdsCtrl.valueChanges.pipe(
        startWith(EMPTY),
        debounceTime(300),
        map((domainPG) =>
          this.setDomainProductGroupFilter(get(domainPG, 'name', domainPG))
        )
      );

    this.domainProductGroupIds = this.domainProductGroupIdsCtrl.valueChanges.pipe(
      map(() =>
        this.filterSelectedDpg(),
      ),
    );
  }


  ngOnInit() {

    this.subscription = this.util.getSelectedSectors().subscribe(result => {
      this.sectorIds = result.sectorIds;
      this.fillDpgsPerSectorCategories(this.sectorIds, this.categoryIds);

    });

    this.subscription = this.util.getSelectedCategories().subscribe(result => {
      this.categoryIds = result.categoryIds;
      this.fillDpgsPerSectorCategories(this.sectorIds, this.categoryIds);
    });

  }

  OnDestroy() {
    this.subscription?.unsubscribe();
  }

  public getSelectedDpgIds() {
    return this.filterSelectedDpg().map((Dpg) => Dpg.id);
  }
  filterSelectedDpg(): DomainProductGroups[] {
    return this.allDomainProductGroupIds.filter(
      (Dpg) => Dpg.selected,
    );
  }

  fillDomainProductGroups(): void {
    this.domainProductGroupService.getAsync().subscribe((result) => {
      const domainProductGroupIds = result.map((r) => {
        const tag: DomainProductGroups = {
          id: r.id,
          name: r.name,
          categoryId: r.categoryId,
          selected: false
        };
        return tag;
      });
      this.allDomainProductGroupIds = domainProductGroupIds.sort((firstId: DomainProductGroups, otherId: DomainProductGroups) => firstId.name.localeCompare(otherId.name));

      this.fillDpgsPerSectorCategories(this.sectorIds, this.categoryIds);
      this.setSelectedDpgIds(this.cachedDomainProductGroups);
    });
  }

  public setSelectedDpgIds(Dpg: number[]): void {
    Dpg?.forEach((DpgId: number) => {
      const found = this.allDomainProductGroupIds.find((Dpg: DomainProductGroups) => Dpg.id === DpgId);
      if (found) {
        found.selected = true;
      }
    });
    this.refreshSuggestionsAndEmptyInput();
  }

 removeAllDPGrp(): void {
    this.filterSelectedDpg().map((x) => x.selected = false);
    this.refreshSuggestionsAndEmptyInput();
  }


  fillDpgsPerSectorCategories(sectorIds: number[], categoryIds: number[]): void {
    this.domainProductGroupService.getAsync(sectorIds, categoryIds).subscribe((result) => {
      this.dpgIdsPerSectorCategory = this.allDomainProductGroupIds.filter(i => result.some(j => j.id === i.id));
      this.refreshSuggestionsAndEmptyInput();
    });
  }

  removeDomainProductGroupChips(item: DomainProductGroups): void {
    this.toggleSelection(item);
  }

  optionClicked(event: Event, item: DomainProductGroups) {
    event.stopPropagation();
    this.toggleSelection(item);
  }

  toggleSelection(item: DomainProductGroups) {
    item.selected = !item.selected;
    this.refreshSuggestionsAndEmptyInput();
  }

  refreshSuggestionsAndEmptyInput() {
    this.domainProductGroupIdsCtrl.reset();
    if(this.domainProductGroupIdsInput)
      this.domainProductGroupIdsInput.nativeElement.value = '';
  }

  private setDomainProductGroupFilter(value: string): DomainProductGroups[] {
    if (value === '' || value === null) {
      return this.dpgIdsPerSectorCategory;
    }
    else {
      const filterValue = value.toLowerCase();
      return this.dpgIdsPerSectorCategory.filter(
        (Dpg) => Dpg.name.toLowerCase().indexOf(filterValue) === 0,
      );
    }
  }

  selectedDomainProductGroupChips(event: MatAutocompleteSelectedEvent): void {
    this.toggleSelection(event.option.value);
    this.domainProductGroupIdsCtrl.setValue(null);
  }

  ngOnChanges(changes: SimpleChanges){
    this.allDomainProductGroupIds.forEach(function(i){ i.selected=false })
    this.setSelectedDpgIds(changes.cachedDomainProductGroups.currentValue);
  }

}
