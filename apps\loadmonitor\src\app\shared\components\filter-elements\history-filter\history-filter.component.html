<div class="history-filter">
	<div>
		<gfk-icon
			*ngIf="isInfoIconShow"
			icon="{{ icon }}"
			class="color-gfk-organge"
			gfk-tooltip="Select 'today' or 'last 3 days' from the list. Or if you want to set a period of your choice then select ‘Date Interval’ here and mention a start and an end date in the Date Range filter"
			tooltipHeading="Days"
		></gfk-icon>
		<mat-form-field appearance="outline" class="date">
			<mat-label>Days</mat-label>
			<mat-select [(value)]="selected" placeholder="Mode" (selectionChange)="changeHistory($event.value)" class="js-filter-sel-days">
				<mat-option value="1">Date Interval</mat-option>
				<mat-option value="2">Today</mat-option>
				<mat-option value="3">Last 3 days</mat-option>
			</mat-select>
		</mat-form-field>
	</div>
  <div>
		<gfk-icon
			*ngIf="isInfoIconShow"
			class="color-gfk-organge"
			icon="{{ icon }}"
			gfk-tooltip="Define a specific start and end date"
			tooltipHeading="Date Range"
		>
		</gfk-icon>
		<mat-form-field appearance="outline" class="date">
			<mat-label>Date range</mat-label>
			<mat-date-range-input [formGroup]="dateRange" [rangePicker]="dateRangePicker" [disabled]="IsDateRangeDisabled">
				<input
					matStartDate
					placeholder="Start date"
					formControlName="start"
					#dateRangeStart
					(dateChange)="dateChange()"
					class="js-filter-inp-start-date"
				/>
				<input
					matEndDate
					placeholder="End date"
					formControlName="end"
					#dateRangeEnd
					(dateChange)="dateChange()"
					class="js-filter-inp-end-date"
				/>
			</mat-date-range-input>
			<mat-datepicker-toggle matSuffix [for]="dateRangePicker"></mat-datepicker-toggle>
			<mat-date-range-picker #dateRangePicker></mat-date-range-picker>
		</mat-form-field>
	</div>
</div>
