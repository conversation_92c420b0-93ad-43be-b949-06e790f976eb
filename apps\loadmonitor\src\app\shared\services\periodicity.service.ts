import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { shareReplay } from 'rxjs/operators';
import { Periodicity } from '../interfaces/Periodicity';
import { ConfigService } from './config.service';

@Injectable({
  providedIn: 'root',
})
export class PeriodicityService {
  private url!:string

  private cachedPeriodicity! : Observable<Periodicity[]>;

  constructor(private http: HttpClient, private config: ConfigService) {
    this.url = `${this.config.getPeriodicityApiUrl()}/periodicities`;
  }

  getAsync(): Observable<Periodicity[]> {
    if(!this.cachedPeriodicity)
    {
      this.cachedPeriodicity = this.http
        .get<Periodicity[]>(this.url)
        .pipe(shareReplay(1));
    }
    return this.cachedPeriodicity;
  }
}
