import { ComponentFixture, TestBed } from '@angular/core/testing';
import { DwhReleaseComponent } from './dwhrelease.component'; // Adjust path as needed
import { DWHReleaseService } from '@loadmonitor/shared/services/dwhrelease.service';
import { MatSnackBar } from '@angular/material/snack-bar';
import { FormBuilder } from '@angular/forms';
import { MatDialog } from '@angular/material/dialog';
import { FilterTrayCacheService } from '@loadmonitor/shared/services/filter-tray-cache.service';
import { of } from 'rxjs';
import { DatePipe } from '@angular/common'; // Import DatePipe


describe('DwhReleaseComponent', () => {
  let component: DwhReleaseComponent;
  let fixture: ComponentFixture<DwhReleaseComponent>;
  let dwhReleaseService: jest.Mocked<DWHReleaseService>;
  let snackBar: jest.Mocked<MatSnackBar>;
  let formBuilder: FormBuilder;
  let dialog: jest.Mocked<MatDialog>;
  let filterTrayCacheService: jest.Mocked<FilterTrayCacheService>;

  beforeEach(() => {
    dwhReleaseService = {
      getDetailAsync: jest.fn().mockReturnValue(of({ records: [] })),
      getAsync: jest.fn().mockReturnValue(of({ count: 0, records: [], moreRecordsAvailable: false })),
    } as unknown as jest.Mocked<DWHReleaseService>;

    snackBar = {
      openFromComponent: jest.fn(),
    } as unknown as jest.Mocked<MatSnackBar>;

    formBuilder = new FormBuilder();
    dialog = {
      open: jest.fn().mockReturnValue({
        afterClosed: jest.fn().mockReturnValue(of(true)),
      }),
    } as unknown as jest.Mocked<MatDialog>;

    filterTrayCacheService = {
      getCacheValueData: jest.fn(),
    } as unknown as jest.Mocked<FilterTrayCacheService>;

    TestBed.configureTestingModule({
      declarations: [DwhReleaseComponent],
      providers: [
        { provide: DWHReleaseService, useValue: dwhReleaseService },
        { provide: MatSnackBar, useValue: snackBar },
        { provide: FormBuilder, useValue: formBuilder },
        { provide: MatDialog, useValue: dialog },
        { provide: FilterTrayCacheService, useValue: filterTrayCacheService },
        DatePipe
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(DwhReleaseComponent);
    component = fixture.componentInstance;
  });

  it('should create the component', () => {
    expect(component).toBeTruthy();
  });

});
