import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ExportComponent } from './export.component';
import { MatSnackBar } from '@angular/material/snack-bar';
import { FormBuilder } from '@angular/forms';
import { of } from 'rxjs';
import { ExportService } from '@loadmonitor/shared/services/export.service';
import { FilterService } from '@dwh/lmx-lib';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { DatePipe } from '@angular/common';
import { MatDialog } from '@angular/material/dialog';

describe('ExportComponent', () => {
  let component: ExportComponent;
  let fixture: ComponentFixture<ExportComponent>;
  let mockExportService: jest.Mocked<ExportService>;
  let mockSnackBar: jest.Mocked<MatSnackBar>;
  let mockFilterService: jest.Mocked<FilterService>;
  let dialog: jest.Mocked<MatDialog>;

  beforeEach(async () => {
    dialog = {
      open: jest.fn(),
    } as unknown as jest.Mocked<MatDialog>;

    await TestBed.configureTestingModule({
      declarations: [ExportComponent],
      imports: [HttpClientTestingModule],
      providers: [
        { provide: ExportService, useValue: mockExportService },
        { provide: MatSnackBar, useValue: mockSnackBar },
        { provide: FormBuilder, useClass: FormBuilder },
        { provide: FilterService, useValue: mockFilterService },
        { provide: DatePipe, useValue: new DatePipe('en-US') },
        { provide: MatDialog, useValue: dialog },
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(ExportComponent);
    component = fixture.componentInstance;
  });

  it('should create the component', () => {
    expect(component).toBeTruthy();
  });

  // Additional tests for other methods and functionalities can be added here
});
