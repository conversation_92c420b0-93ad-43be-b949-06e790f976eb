import { DetailMenuConfiguration } from "./DetailMenuConfiguration";

export interface MigrationRuleRuns {
  runId: number;
  status: string;
  ruleSetName: string;
  ruleSetId: number;
  sourceComponentId: number;
  targetComponentId: number;
  scopeTypeId: number;
  jobId: number;
  message?: string;
  createdBy: string;
  created: Date;
  started: Date;
  finished: Date;
  detailMenuConfig?: DetailMenuConfiguration[];
  isSelected?: boolean;
  isExpanded?: boolean;
}













