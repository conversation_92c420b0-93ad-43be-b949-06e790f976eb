@use 'sass:map' as map;
@use '../../../../styles/theme/gfk-light.palette' as gfk;


.dot-flashing {
  position: relative;
  width: 10px;
  height: 10px;
  border-radius: 5px;
  background-color: #1e3a98;
  color: #1e3a98;
  animation: dotFlashing 1s infinite linear alternate;
  animation-delay: .5s;
  margin-left: auto;
  margin-right: 20px;
  margin-top: 10px;
  display: inline-block;
}

.dot-flashing::before, .dot-flashing::after {
  content: '';
  display: inline-block;
  position: absolute;
  top: 0;
}

.dot-flashing::before {
  left: -15px;
  width: 10px;
  height: 10px;
  border-radius: 5px;
  background-color: #1e3a98;
  color: #1e3a98;
  animation: dotFlashing 1s infinite alternate;
  animation-delay: 0s;
}

.dot-flashing::after {
  left: 15px;
  width: 10px;
  height: 10px;
  border-radius: 5px;
  background-color: #1e3a98;
  color: #1e3a98;
  animation: dotFlashing 1s infinite alternate;
  animation-delay: 1s;
}

@keyframes dotFlashing {
  0% {
    background-color: #1e3a98;
  }
  50%,
  100% {
    background-color: #fff1e6;
  }
}
