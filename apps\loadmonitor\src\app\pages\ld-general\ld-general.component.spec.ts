import { ComponentFixture, TestBed } from '@angular/core/testing';
import { LdGeneralComponent } from './ld-general.component';
import { MatSnackBar } from '@angular/material/snack-bar';
import { FormBuilder } from '@angular/forms';
import { ProductionProjectTypeService } from '@loadmonitor/shared/services/production-project-type.service';
import { LdGeneralService } from './../../shared/services/ld-general.service';
import { FilterService } from '@dwh/lmx-lib/src/lib/services/filter.service';
import { HelperService } from '@loadmonitor/shared/services/helper.service';
import { FilterTrayCacheService } from '@loadmonitor/shared/services/filter-tray-cache.service';
import { BehaviorSubject, of } from 'rxjs';
import { NO_ERRORS_SCHEMA } from '@angular/core';
import { MatDialog, MatDialogModule } from '@angular/material/dialog';
import { DatePipe } from '@angular/common';
import { JobTypes } from '@loadmonitor/shared/jobTypes';
import { TagItem } from '@loadmonitor/shared/interfaces/TagItem';

describe('LdGeneralComponent', () => {
  let component: LdGeneralComponent;
  let fixture: ComponentFixture<LdGeneralComponent>;
  let datePipe: jest.Mocked<DatePipe>;

  const mockSnackBar = {
    open: jest.fn(),
  };

  const mockProductionProjectTypeService = {
    getAsync: jest.fn().mockReturnValue(of([{ id: 1, name: 'Project A' }, { id: 2, name: 'Project B' }] as TagItem[])),
  };
  
  const mockLdGeneralService = {};
  
  const mockFilterService = {
    filterCount$: new BehaviorSubject<number>(0),
    setFilters: jest.fn(),
  };
  
  const mockHelperService = {};
  
  const mockFilterTrayCacheService = {
    getCacheValueData: jest.fn(),
    getCacheNameData: jest.fn(),
  };

  beforeEach(async () => {
    datePipe = {
      transform: jest.fn(),
    } as any;

    await TestBed.configureTestingModule({
      declarations: [LdGeneralComponent],
      imports: [
        MatDialogModule
      ],
      providers: [
        FormBuilder,
        { provide: MatSnackBar, useValue: mockSnackBar },
        { provide: ProductionProjectTypeService, useValue: mockProductionProjectTypeService },
        { provide: LdGeneralService, useValue: mockLdGeneralService },
        { provide: FilterService, useValue: mockFilterService },
        { provide: HelperService, useValue: mockHelperService },
        { provide: FilterTrayCacheService, useValue: mockFilterTrayCacheService },
        MatDialog,
        { provide: DatePipe, useValue: datePipe },
      ],
      schemas: [NO_ERRORS_SCHEMA],
    }).compileComponents();

    fixture = TestBed.createComponent(LdGeneralComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should fill production project types on initialization', () => {
    component.fillProductionProjectType();
    expect(mockProductionProjectTypeService.getAsync).toHaveBeenCalledWith(JobTypes.ldGeneral);
    expect(component.productionProjectTypes.size).toBe(2);
    expect(component.productionProjectTypes.get('1')).toBe('Project A');
    expect(component.productionProjectTypes.get('2')).toBe('Project B');
  });
});
