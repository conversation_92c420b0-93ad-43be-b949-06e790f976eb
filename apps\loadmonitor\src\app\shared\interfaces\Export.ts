import { DetailMenuConfiguration } from './DetailMenuConfiguration';

export interface Exports {
	exportId: number;
	exportName: string;
	reportProjectName: string;
	reportProjectId: number;
	toolFormat: string;
	status: string;
	message: string;
	expectedExportCount: number;
	allocatedExportCount: number;
	period: string;
	jobId: number;
	favoriteId: number;
	favoriteName: string;
	scope: string;
	profileId: number;
	profileName: string;
	createdBy: string;
	created: Date | string;
	finished: Date | string;
	started: Date | string;
	estimatedRuntime: string;
	runtime: string | null;
	detailMenuConfig?: DetailMenuConfiguration[];
	warningLevel?: any;
	isSelected?: boolean;
	isExpanded?: boolean;
	isChecked?: boolean;
}
