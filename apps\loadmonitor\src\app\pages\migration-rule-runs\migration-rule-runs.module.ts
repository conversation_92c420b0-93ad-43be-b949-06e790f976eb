import { NgModule } from '@angular/core';

import { MigrationRuleRunsRoutingModule } from './migration-rule-runs-routing.module';
import { MigrationRuleRunsComponent } from './migration-rule-runs.component';
import { SharedModule } from '@loadmonitor/shared/shared.module';
import { MatTableModule } from '@angular/material/table';
import { MatSortModule } from '@angular/material/sort';
import { MatPaginatorModule } from '@angular/material/paginator';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatInputModule } from '@angular/material/input';
import { MatChipsModule } from '@angular/material/chips';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { LmxLibModule } from '@dwh/lmx-lib';

@NgModule({
  declarations: [MigrationRuleRunsComponent],
  imports: [
    MigrationRuleRunsRoutingModule,
    SharedModule,
    MatTableModule,
    MatSortModule,
    MatPaginatorModule,
    MatFormFieldModule,
    MatExpansionModule,
    MatInputModule,
    MatChipsModule,
    MatAutocompleteModule,
    LmxLibModule
  ]
})
export class MigrationRuleRunsModule {
}
