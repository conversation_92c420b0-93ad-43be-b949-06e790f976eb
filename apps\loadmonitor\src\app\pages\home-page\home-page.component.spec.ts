import { ComponentFixture, TestBed } from '@angular/core/testing';
import { HomePageComponent } from './home-page.component';
import { AuthFacade } from '@dwh/lmx-lib/src/lib/services/auth';
import { ConfigService } from '@loadmonitor/shared/services/config.service';
import { of } from 'rxjs';
import { ChangeDetectorRef } from '@angular/core';

class MockAuthFacade {
    user$ = of({ email: '<EMAIL>' });
    signOut = jest.fn().mockReturnValue(of(null));
    signIn = jest.fn();
}

class MockConfigService {
    getVersion = jest.fn().mockReturnValue('1.0.0_test');
}

describe('HomePageComponent', () => {
    let component: HomePageComponent;
    let fixture: ComponentFixture<HomePageComponent>;
    let authFacade: AuthFacade;

    beforeEach(async () => {
        await TestBed.configureTestingModule({
            declarations: [HomePageComponent],
            providers: [
                { provide: AuthFacade, useClass: MockAuthFacade },
                { provide: ConfigService, useClass: MockConfigService },
                { provide: ChangeDetectorRef, useValue: { detectChanges: jest.fn() } },
            ],
        }).compileComponents();

        fixture = TestBed.createComponent(HomePageComponent);
        component = fixture.componentInstance;
        authFacade = TestBed.inject(AuthFacade);
        fixture.detectChanges();
    });

    it('should create the component', () => {
        expect(component).toBeTruthy();
    });

    it('should initialize component with correct values', () => {
        component.ngOnInit();
        expect(component.version).toBe('test');
        expect(component.pageName).toBe('Monitor Data Export'); // Default page name
        expect(component.isIframe).toBe(window !== window.parent && !window.opener);
    });

    it('should handle window resize', () => {
        component.onResize();
        // Add your margin top logic test if needed
    });

    it('should set pageName based on currentTab', () => {
        component.currentTab = 'bcr-reworks';
        component.ngOnInit();
        expect(component.pageName).toBe('DWH Global Process');
    });

    it('should clean up on destroy', () => {
        component.ngOnDestroy();
        expect(component['_destroying$'].isStopped).toBe(true);
    });

    it('should map user email to username correctly', (done) => {
        component.userName$.subscribe(username => {
            expect(username).toBe('doe, john');
            done();
        });
    });

    it('should call signOut on AuthFacade when signOut is called', () => {
        component.signOut();
        expect(authFacade.signOut).toHaveBeenCalled();
    });

    it('should call signIn on AuthFacade when signIn is called', () => {
        component.signIn();
        expect(authFacade.signIn).toHaveBeenCalled();
    });
});
