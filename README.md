[![Quality Gate Status](https://sonarqube.gfk.com/api/project_badges/measure?project=DWH.LoadMonitor.UI&metric=alert_status&token=sqb_5f068694d8ba673fe09cfc64f0c7c2e274452f25)](https://sonarqube.gfk.com/dashboard?id=DWH.LoadMonitor.UI)

# Load Monitor

This project was generated using [Nx](https://nx.dev).

## Before you begin

Install all dependencies, including:
1. nvm for switching between nodejs versions in [Windows](https://github.com/coreybutler/nvm-windows) and [macOs](https://formulae.brew.sh/formula/nvm) (before installation, remove existing nodejs to have no conflicts)
2. nodejs v20.19.0 or later by running `nvm install v20.19.0` (Angular 20 requires Node.js ^20.19.0 || ^22.12.0 || ^24.0.0)
3. yarn by running `npm install -g yarn@latest`
4. package.json dependencies by running `yarn`
5. Prefix all nx commands with `yarn nx <command>` or install nx globally by running `yarn global add @nrwl/cli`

## Generate an application

Run `nx g @nrwl/angular:app my-app` to generate an application.

When using Nx, you can [generate](https://nx.dev/latest/angular/cli/generate) multiple applications and libraries in the same workspace.

## Generate a library

Run `nx g @nrwl/angular:lib my-lib` to generate a library.

> You can also use any of the plugins above to generate libraries as well.

Libraries are shareable across libraries and applications. They can be imported from `@dwh/mylib`.

## Publish a library
In order to share the library to other repository/project, it needs to be published first.
The default library is `@dwh/lmx-lib`.
1. Create components within that library by running `yarn extend`, pick a name for the component and create it.
2. Implement needed functionality
3. Build as a library by running `yarn build:lib`
4. Publish it into nexus by running `yarn release`
5. Once the receiver project has registry set (.yarnrc or .npmrc) it can import the result via `@dwh/lmx-lib`

## Development server

Run `nx serve loadmonitor` for a dev server or `nx serve` for default app. Navigate to http://localhost:4200/. The app will automatically reload if you change any of the source files.
By default the app should run via webpack proxy (proxy.conf.json) targeting a remote API.

## Code scaffolding

Run `nx generate component my-component --project=loadmonitor` to generate a new component.
Or simply `nx g c my-component` to generate it. Other Angular CLI commands also work.

## Build

Run `nx build loadmonitor` to build the project or `nx build` for default app. The build artifacts will be stored in the `dist/` directory. Use the `--prod` flag for a production build.

## Running unit tests

Run `nx test loadmonitor` to execute the unit tests via [Jest](https://jestjs.io).

Run `nx affected:test` to execute the unit tests affected by a change.

## Running end-to-end tests

Run `nx e2e loadmonitor-e2e` to execute the end-to-end tests via [Cypress](https://www.cypress.io).

Run `nx affected:e2e` to execute the end-to-end tests affected by a change.

## Understand your workspace

Run `nx dep-graph` to see a diagram of the dependencies of your projects.

## Further help

Visit the [Nx Documentation](https://nx.dev) to learn more.

## Extra:

Run `yarn lint:fix` to check the code style and possible to fix it

Run `yarn test-and-lint` to run tests and code style checks on CI

Run `yarn code-coverage` to generate coverage

Run `yarn bundle-analyze` to see the prod package files and what's inside them.

Run `yarn bundle-source-maps` for a similar view based on source-maps
