import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable, Subject } from 'rxjs';
import { Jobs } from '../interfaces/Jobs';
import { LdGeneral } from '../interfaces/LdGeneral';
import { ConfigService } from './config.service';
import { LdGeneralPropertyPage } from '../interfaces/LdGeneralPropertyPage';
import { DWHMessagePropertyPage } from '../interfaces/DWHMessagePropertyPage';

@Injectable({
  providedIn: 'root',
})
export class LdGeneralService {
  private url!:string;
  public floatingPanelData$ = new Subject<LdGeneralPropertyPage[]>();
  public floatingPanelDestroy$ = new Subject<boolean>();

  constructor(private http: HttpClient, private config: ConfigService) {
    this.url = `${this.config.getApiUrl()}/LDGenerals`;
  }

  getAsync(
    countryIds?: number[],
    periodIds?: number[],
    periodicityIds?: number[],
    userNames?: string[],
    statusIds?: number[],
    loadDefinitionIds?: number[],
    productionProjectIds?: number[],
    productionProjectName?: string,
    productionProjectTypeIds?: number[],
    startDate?: Date,
    endDate?: any
  ): Observable<Jobs<LdGeneral>> {
    const body = {
      countryIds,
      periodIds,
      periodicityIds,
      userNames,
      statusIds,
      loadDefinitionIds,
      productionProjectIds,
      productionProjectName,
      productionProjectTypeIds,
      startDate,
      endDate,
    };
    return this.http.post<Jobs<LdGeneral>>(this.url, body);
  }

  getDetailAsync(loadDefinitionId: number): Observable<LdGeneralPropertyPage[]>{
    return this.http.get<LdGeneralPropertyPage[]>(this.url + '/' + loadDefinitionId);
  }
  getMessageAsync(loadDefinitionId: number): Observable<Jobs<DWHMessagePropertyPage>>{
    return this.http.get<Jobs<DWHMessagePropertyPage>>(this.url + '/DwhMessages/' + loadDefinitionId);
  }
}
