<mat-form-field class="chip-list" appearance="outline">
  <mat-label>Production Project Type </mat-label>
  <mat-chip-list class="chip-list-wrapper" #chipListProductionProjectType aria-label="ProductionProjectTypes selection">
    <mat-chip *ngFor="let productionProjectType of selectedPPTs$ | async"
              [selectable]="true"
              [removable]="true"
              (removed)="removeProductionProjectTypeChips(productionProjectType)"
    >
      {{ productionProjectType.name }}
      <mat-icon matChipRemove *ngIf="true">cancel</mat-icon>
    </mat-chip>
    <input
      placeholder="New ..."
      #productionProjectTypeIdsInput
      [formControl]="productionProjectTypesCtrl"
      [matAutocomplete]="autoProductionProjectType"
      [matChipInputFor]="chipListProductionProjectType"
      [matChipInputSeparatorKeyCodes]="separatorKeysCodes"
      class="js-filter-inp-productionProjectType" />
  </mat-chip-list>
  <mat-autocomplete #autoProductionProjectType="matAutocomplete"
    (optionSelected)="selectedProductionProjectTypeChips($event)" [autoActiveFirstOption]="true">
    <mat-option *ngFor="let productionProjectType of filteredPPTs$ | async"
      [value]="productionProjectType">
      <div (click)="optionClicked($event, productionProjectType)">
        <mat-checkbox class="option-checkbox" [checked]="productionProjectType.selected" (change)="toggleSelection(productionProjectType)"
          (click)="$event.stopPropagation()">
          {{ productionProjectType.name }}
        </mat-checkbox>
      </div>
    </mat-option>
  </mat-autocomplete>
</mat-form-field>
