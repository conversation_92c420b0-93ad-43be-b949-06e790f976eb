import { UserCache } from '@loadmonitor/shared/interfaces/caching/UserCache';
import { Category } from '../Category';
import { Sector } from '../Sector';
import { Country } from '../Country';
import { DomainProductGroups } from '../DomainProductGroups';

export interface BcrReworksCache
{
    sectorIds: number[];
    categoryIds: number[];
    domainProductGroupIds: number[];
    channelIds: number[];
    baseChannelIds: number[];
    status: number[];
    selectedDays: string;
    startDate?: Date;
    endDate?: Date;
    users?: UserCache[];
    jobIds: number[],
    loadIds: number[],
    bcRearrangedIds: number[],
    feature: string,
    isAutoRefresh?: boolean;
    countries: Country[];
    domainProductGroup: DomainProductGroups[];
    category:Category[];
    sector:Sector[];
}
