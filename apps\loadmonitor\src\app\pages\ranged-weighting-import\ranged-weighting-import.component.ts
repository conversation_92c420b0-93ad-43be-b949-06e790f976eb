import { MatSort } from '@angular/material/sort';
import { AfterViewInit, Component, OnInit, ViewChild, AfterContentInit, Input, HostListener, OnDestroy } from '@angular/core';
import { MatTable, MatTableDataSource } from '@angular/material/table';
import { RangedWeightingImport } from '@loadmonitor/shared/interfaces/RangedWeightingImport';
import { RangedWeightingImportService } from '@loadmonitor/shared/services/ranged-weighting-import.service';
import { MatPaginator } from '@angular/material/paginator';
import { FormBuilder, FormGroup } from '@angular/forms';
import { RangedWeightingImportFilter } from '@loadmonitor/shared/interfaces/RangedWeightingImportFilter';
import { COMMA, ENTER } from '@angular/cdk/keycodes';
import { MatSnackBar } from '@angular/material/snack-bar';
import { DefaultSnackbarComponent } from '@loadmonitor/shared/components/default-snackbar/default-snackbar.component';
import { JobTypes } from '@loadmonitor/shared/jobTypes';
import { SettingsConstants } from '@loadmonitor/shared/settings.constants';
import { RangedWeightingCache } from '@loadmonitor/shared/interfaces/caching/RangedWeightingCache';
import { Filter, FilterRecords, FilterService, FilterTrayComponent } from '@dwh/lmx-lib/src';
import { ToggleButtonComponent } from '@gfk/ng-lib';
import { environment } from 'apps/loadmonitor/src/environments/environment';
import { BehaviorSubject, Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { DatePipe } from '@angular/common';
import { UserService } from '@loadmonitor/shared/services/user.service';
import { StatusService } from '@loadmonitor/shared/services/status.service';
import { StatusSelectService } from '@loadmonitor/shared/services/status-select.service';
import * as moment from 'moment';
import { FilterTrayCacheService } from '@loadmonitor/shared/services/filter-tray-cache.service';

@Component({
	selector: 'lm-ranged-weighting-import',
	templateUrl: './ranged-weighting-import.component.html',
	styleUrls: ['./ranged-weighting-import.component.scss'],
})
export class RangedWeightingImportComponent implements AfterViewInit, OnInit, AfterContentInit, OnDestroy {

	/* *********
	 * ViewChild
	 ********* */

	@Input() icon = 'delete';

	@ViewChild(FilterTrayComponent) filterTrayComponent: FilterTrayComponent | undefined;
	@ViewChild(ToggleButtonComponent) togglebuttonComponent: ToggleButtonComponent | undefined;

	@ViewChild(MatSort)
	sort: MatSort = new MatSort();

	@ViewChild(MatTable)
	table!: MatTable<RangedWeightingImport>;

	@ViewChild(MatPaginator)
	paginator!: MatPaginator;

	/* *********************
	 * Components variables
	 ********************* */
	jobType: JobTypes = JobTypes.rangedWeightingImport;

	// Progress Spinner visibility
	isLoading = false;
	FilterTrayOpenFlag = true;
	public RangedWeightingImportForm!: FormGroup;
	public cacheObj: RangedWeightingCache = {} as RangedWeightingCache;

  selectedFilters: any;
	cacheValue: BehaviorSubject<any> = new BehaviorSubject<any>({});
	resetForm: Subject<boolean> = new Subject();

	cacheName = 'cache::RangedWeighting';
	/* *********************
	 * Components parameters
	 ********************* */

	//Mat-Table
	dataSource = new MatTableDataSource<RangedWeightingImport>();
	filter: RangedWeightingImportFilter | undefined;
	displayedColumns: string[] = [
		'weightingId',
		'status',
		'message',
		'qcProjectId',
		'baseProjectName',
		'username',
		'notifyEmail',
		'jeeJobId',
		'jeeJobInfo',
		'periodDescription',
		'created',
		'started',
		'finished',
	];

	//Mat-Expansion-Panel
	panelOpenState = false;

	//Tag-Chip-List
	readonly separatorKeysCodes = [ENTER, COMMA] as const;

	readonly date_with_time = SettingsConstants.DATE_FORMAT_WITH_TIME;

	/* *********************
	 * Components variables
	 ********************* */
	eventsSubject: Subject<void> = new Subject<void>();
	IsDisabled = false;
	environments = environment.environments;
	IsEnabled = false;
	modalshow = false;
	filterCount = '';
	toggleInterval!: any;
	timer = 60;
	EnableRefresh = true;
	cacheInterval!: any;
	defaultoption = localStorage.getItem(this.cacheName + '-LastFilter') || 'Default';
	defaultfilter = JSON.parse(localStorage.getItem(this.cacheName + '-DefaultFilter') || '[]');
	currentfilter = JSON.parse(localStorage.getItem('QuickFilter') || '[]').filter(
		(x) => x.user == localStorage.getItem('username') && x.slice == window.location.href.split('/')[3]
	);
	refreshedDateTime!: boolean;
	rangedWeightingImportData!: any[];

  @HostListener('document:visibilitychange', ['$event'])
	appVisibility() {
		if (document.hidden) {
			if (this.cacheInterval) {
				clearInterval(this.cacheInterval);
			}
			if (this.toggleInterval) {
				clearInterval(this.toggleInterval);
			}
		} else {
			if (this.cacheObj.isAutoRefresh === true) {
				this.cacheInterval = setInterval(() => {
					if (this.defaultoption != 'Default') {
						this.refresh();
					}
				}, this.timer * 1000);
			} else {
				if (this.cacheInterval !== undefined) clearInterval(this.cacheInterval);
				if (this.toggleInterval !== undefined) clearInterval(this.toggleInterval);
			}
		}
	}
	private destroy$ = new Subject<void>();

	/* **************
	 * Public Methods
	 ************** */
	constructor(
		private rangedWeightingImportService: RangedWeightingImportService,
		private snackBar: MatSnackBar,
		private formBuilder: FormBuilder,
		public filterService: FilterService,
		public datepipe: DatePipe,
		private filterTrayCacheService: FilterTrayCacheService
	) {}

	private updateFC(): void {
		if(this.cacheObj){
			this.filterTrayCacheService.getCacheValueData(this.cacheObj);
		}
  	}


	ngOnInit(): void {
		if (this.environments.filter((x) => x.location == window.location.origin)[0]) {
			this.IsEnabled = this.environments.filter((x) => x.location == window.location.origin)[0].enabled;
		} else {
			this.IsEnabled = false;
		}
		this.RangedWeightingImportForm = this.formBuilder.group({
			isAutoRefresh: this.togglebuttonComponent?.checked,
		});
		this.SetFiltersCount();
	}
	ngAfterContentInit() {
		this.afterChildComponentsLoaded();
		this.CreateFilterSummaryChips();
		this.SetFiltersCount();

		if (this.cacheObj.isAutoRefresh === true) {
			if (this.cacheInterval === undefined) {
				this.cacheInterval = window.setInterval(() => {
					if (this.defaultoption != 'Default') {
						this.refresh();
					}
				}, this.timer * 1000);
			}
		} else {
			if (this.cacheInterval !== undefined) clearInterval(this.cacheInterval);
			if (this.toggleInterval !== undefined) clearInterval(this.toggleInterval);
		}
	}

	afterChildComponentsLoaded() {
		this.cacheObj = JSON.parse(localStorage.getItem(this.cacheName) || '{}');
		this.updateFC();
	}

	ngAfterViewInit(): void {
		this.dataSource.sort = this.sort;
		this.table.dataSource = this.dataSource;
	}
	

	refresh(): void {
		this.refreshedDateTime = false;
		this.filterTrayComponent?.showSpinner(true);
		this.isLoading = true;
		this.destroy$.next();
		this.rangedWeightingImportService
		.getAsync(
			this.selectedFilters.status?.map((status) => status),
			this.selectedFilters.importIds,
			this.selectedFilters.jobIds,
        	this.selectedFilters.qcProjectIds,
			this.selectedFilters.users?.map((users) => users.userName),
			this.selectedFilters.qcProjectName,
        	this.selectedFilters.startDate ? this.selectedFilters.startDate : null,
			this.selectedFilters.endDate ? moment(this.selectedFilters.endDate).format().split('T')[0] + 'T23:59:59.999Z' : null,
		 )
		.pipe(
			takeUntil(this.destroy$)
		)
		.subscribe((result) => {
			this.refreshedDateTime = true;
			if (result.moreRecordsAvailable) {
				this.notify('More than 1000 results exist!');
			}

			if (result.count === 0) {
				this.notify('No result found!');
				this.rangedWeightingImportData = [];
			}
			this.isLoading = false;
			this.rangedWeightingImportData = result.records;
			this.dataSource = new MatTableDataSource<RangedWeightingImport>(result.records);
			this.dataSource.sort = this.sort;
			this.paginator.pageIndex = 0;
			this.dataSource.paginator = this.paginator;
			this.table.dataSource = this.dataSource;
			this.filterTrayComponent?.showSpinner(false);
		},
		(error) => {
			if (error.status === 504) {
				this.notify('Gateway Timeout. The server took too long to respond. Please try again later.');
			} 
			else {
				this.notify(`An error occurred: ${error.message}`);
			}
			this.isLoading = false;
			this.filterTrayComponent?.showSpinner(false);
		});
		this.SetFiltersCount();
	}

	private notify(message: string) {
		this.snackBar.openFromComponent(DefaultSnackbarComponent, {
			duration: 10 * 1000,
			data: message,
		});
	}

	//Quick Filter Methods//
	openmodal() {
		this.eventsSubject.next();
	}

	disablesave(status) {
		this.IsDisabled = status;
	}


	setFormValue(autoRefreshStatus) {
		this.RangedWeightingImportForm.setValue({
			isAutoRefresh: autoRefreshStatus === undefined || autoRefreshStatus === false ? false : true,
		});
	}

	saveInQuickFilter(autoRefreshStatus, filterName) {
		this.setFormValue(autoRefreshStatus);
		this.currentfilter = JSON.parse(localStorage.getItem('QuickFilter') || '[]');
		this.currentfilter.push({
			data: this.selectedFilters,
			filter: filterName,
			slice: window.location.href.split('/')[3],
			user: localStorage.getItem('username'),
		});
		this.defaultoption = filterName;
		const oldcache = this.cacheObj;
		const olddefault = localStorage.getItem(this.cacheName + '-DefaultFilter');
		this.ConfirmApply(autoRefreshStatus);
		this.cacheObj = oldcache;
		localStorage.setItem(this.cacheName + '-DefaultFilter', olddefault || '[]');
		localStorage.setItem('QuickFilter', JSON.stringify(this.currentfilter));
		localStorage.setItem(this.cacheName + '-LastFilter', filterName);
	}

	applyfilter(selectedvalue) {
		this.resetForm.next(true);
		const oldautotoggle = this.cacheObj.isAutoRefresh;
		selectedvalue = selectedvalue.value ? selectedvalue.value : selectedvalue;
		this.defaultoption = selectedvalue;
		localStorage.setItem(this.cacheName + '-LastFilter', this.defaultoption || 'Default');
		if (selectedvalue == 'Default') {
			if (
				JSON.parse(localStorage.getItem(this.cacheName + '-DefaultFilter') || '[]').filter(
					(x) => x.user == localStorage.getItem('username') && x.slice == window.location.href.split('/')[3]
				)[0]
			) {
				this.cacheObj = JSON.parse(localStorage.getItem(this.cacheName + '-DefaultFilter') || '[]').filter(
					(x) => x.user == localStorage.getItem('username') && x.slice == window.location.href.split('/')[3]
				)[0]['data'];
			}

			localStorage.setItem(this.cacheName, JSON.stringify(this.cacheObj));
		} else {
			this.cacheObj = JSON.parse(localStorage.getItem('QuickFilter') || '[]')
				.filter((x) => x.user == localStorage.getItem('username') && x.slice == window.location.href.split('/')[3])
				.filter((x) => x.filter == selectedvalue)[0]['data'];
			localStorage.setItem(this.cacheName, JSON.stringify(this.cacheObj));
		}
		this.cacheObj.isAutoRefresh = oldautotoggle;
		this.CreateFilterSummaryChips();

		
    if (this.cacheObj.selectedDays == '3') {
			const date = new Date();
			this.cacheObj.startDate = new Date(Date.UTC(date.getFullYear(), date.getMonth(), date.getDate() - 2, 0, 0, 0, 0));
			this.cacheObj.endDate = new Date(Date.UTC(date.getFullYear(), date.getMonth(), date.getDate(), 0, 0, 0, 0));
		} else if (this.cacheObj.selectedDays == '2') {
			const date = new Date();
			this.cacheObj.startDate = new Date(Date.UTC(date.getFullYear(), date.getMonth(), date.getDate(), 0, 0, 0, 0));
			this.cacheObj.endDate = new Date(Date.UTC(date.getFullYear(), date.getMonth(), date.getDate(), 0, 0, 0, 0));
		}
		setTimeout(() => {
			this.updateFC();
			this.refresh();
		}, 200);
		this.selectedFilters = this.cacheObj;
		localStorage.setItem(this.cacheName, JSON.stringify(this.cacheObj));
	}

	saveInCache(autoRefreshStatus, AutoToggle) {
		this.setFormValue(autoRefreshStatus);
		localStorage.setItem(this.cacheName, JSON.stringify(this.selectedFilters));
		this.cacheObj = JSON.parse(localStorage.getItem(this.cacheName) || '{}');
		if(this.cacheObj.qcProjectName == null){
			this.cacheObj.qcProjectName = '';
		}
		if (!AutoToggle) {
			this.defaultfilter = [];
			this.defaultfilter.push({
				data: this.selectedFilters,
				slice: window.location.href.split('/')[3],
				user: localStorage.getItem('username'),
			});
			localStorage.setItem(this.cacheName + '-DefaultFilter', JSON.stringify(this.defaultfilter));
			let nofilter = 0;
			let selectedfilter = '';
			this.currentfilter.forEach((i) => {
				// if (i.filter == this.defaultoption) {
				if(JSON.stringify(i.data) == JSON.stringify(this.cacheObj)){
					nofilter++;
					selectedfilter = i.filter;
				}
			});
			if (nofilter == 0) {
				this.defaultoption = 'Default';
			} else {
				this.defaultoption = selectedfilter;
			}
			localStorage.setItem(this.cacheName + '-LastFilter', this.defaultoption);
		}
		this.updateFC();
	}

	toggleChecked(check: any) {
		if (check === true) {
			this.saveInCache(check, true);
			this.toggleInterval = setInterval(() => {
				if (this.defaultoption != 'Default') {
					this.refresh();
				}
			}, this.timer * 1000);
		} else {
			if (this.toggleInterval !== undefined) clearInterval(this.toggleInterval);
			if (this.cacheInterval !== undefined) clearInterval(this.cacheInterval);
			this.saveInCache(check, true);
		}
	}

	CreateFilterSummaryChips() {
		if (this.cacheObj && Object.keys(this.cacheObj).length) {
			this.usersFilterSummary();
			this.importIdsFilterSummary();
			this.jeeJobIdsFilterSummary();
			this.qcProjectIdsFilterSummary();
			this.statusFilterSummary();
			this.qcProjectNameFilterSummary();
			this.datesFilterSummary(this.cacheObj.startDate, this.cacheObj.endDate);
		}
	}

	ConfirmApply(autoRefreshStatus) {
		this.saveInCache(autoRefreshStatus, false);
		this.CreateFilterSummaryChips();

		let count;
		this.filterService.filtersSelected$.subscribe((selectedFilters: FilterRecords) => {
			count = Number(Object.keys(selectedFilters).length);
		});

		if (count > 0) {
			this.refresh();
		} else {
			if (confirm('Loading results without any filters selected will take some time to load. Are you sure you want to proceed?')) {
				this.refresh();
			}
		}
	}

	public isShownFilterTrayEventHandler($event: boolean) {
		this.filterTrayComponent?.toggleShowTray($event);
		this.FilterTrayOpenFlag = $event;
	}

	getWidth() {
		this.FilterTrayOpenFlag = !this.FilterTrayOpenFlag;
		return '100%';
	}

	filterSummaryChipClear(filteredItems) {
		if (filteredItems == 'ALL') {
			if (confirm('This will remove all of your selected filters and could not be reversed. Are you sure you want to do that?')) {
        		this.resetForm.next(true);
				this.filterTrayCacheService.getCacheNameData(this.cacheName);
		        this.filterCount = '';
			}
		}
		this.saveInCache(this.cacheObj.isAutoRefresh, false);
		this.CreateFilterSummaryChips();
		this.SetFiltersCount();
	}


	SetFiltersCount() {
		let count;
		this.filterService.filtersSelected$.subscribe((selectedFilters: FilterRecords) => {
			count = Number(Object.keys(selectedFilters).length);
		});
		if (count > 0) {
			this.filterCount = '(' + count.toString() + ')';
		} else {
			this.filterCount = '';
		}
	}

	ngOnDestroy() {
		if (this.cacheInterval) {
			clearInterval(this.cacheInterval);
		}

		if (this.toggleInterval) {
			clearInterval(this.toggleInterval);
		}
		this.destroy$.next();
    	this.destroy$.complete();
	}

	importIdsFilterSummary() {
		this.filterService.setFilters({
			importIds: {
				name: 'importIds',
				values: this.cacheObj.importIds,
				slice: window.location.href.split('/')[3],
			} as Filter,
		});
	}

	jeeJobIdsFilterSummary() {
		this.filterService.setFilters({
			jeeJobIds: {
				name: 'jeeJobIds',
				values: this.cacheObj.jobIds,
				slice: window.location.href.split('/')[3],
			} as Filter,
		});
	}

	qcProjectIdsFilterSummary() {
		this.filterService.setFilters({
			qcProjectIds: {
				name: 'qcProjectIds',
				values: this.cacheObj.qcProjectIds,
				slice: window.location.href.split('/')[3],
			} as Filter,
		});
	}

	usersFilterSummary() {
		this.filterService.setFilters({
			users: {
				name: 'User',
				values: this.cacheObj.users?.map((i) => i.name),
				slice: window.location.href.split('/')[3],
			} as Filter,
		});
	}

	statusFilterSummary() {
		this.filterService.setFilters({
			statusIds: {
				name: 'Status',
				values: this.cacheObj.status,
				slice: window.location.href.split('/')[3],
			} as Filter,
		});
	}

	qcProjectNameFilterSummary() {
		this.filterService.setFilters({
			qcProjectName: {
				name: 'qcProjectName',
				values: this.cacheObj.qcProjectName,
				slice: window.location.href.split('/')[3],
			} as unknown as Filter,
		});
	}

	datesFilterSummary(dateStart: any, dateEnd: any) {
		if (dateStart != null || dateEnd != null) {
			const start = dateStart == 'Invalid Date' || dateStart == null ? '' : this.datepipe.transform(dateStart, 'yyyy-MM-dd');
			const end = dateEnd == 'Invalid Date' || dateEnd == null ? '' : this.datepipe.transform(dateEnd, 'yyyy-MM-dd');
			this.filterService.setFilters({
				dates: {
					name: 'Date',
					values: (start !== undefined && end !== undefined) ? [start, end] : null,
					slice: window.location.href.split('/')[3],
				} as Filter,
			});
		} else {
			this.filterService.setFilters({
				dates: {
					name: 'Date',
					values: null,
					slice: window.location.href.split('/')[3],
				} as unknown as Filter,
			});
		}
	}

	//Quick Filter Methods End//
  getSelectedFilters(selectedFilters: any){
		this.cacheObj = selectedFilters;
		this.selectedFilters = selectedFilters;
	}
}
