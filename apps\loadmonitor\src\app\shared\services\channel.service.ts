import { Injectable } from '@angular/core';
import { Observable} from 'rxjs';
import { HttpClient } from '@angular/common/http';
import { ConfigService } from './config.service';
import { TagItem } from '@loadmonitor/shared/interfaces/TagItem';
import { shareReplay } from 'rxjs/operators';

@Injectable({
  providedIn: 'root',
})
export class ChannelService {
  private url!:string;

  private cachedChannel! : Observable<TagItem[]>;
  constructor(private http: HttpClient, private config: ConfigService) {
    this.url = `${this.config.getApiUrl()}/Channels`;
  }

  getAsync(): Observable<TagItem[]> {
    if(!this.cachedChannel)
    {
      this.cachedChannel = this.http
        .get<TagItem[]>(this.url)
        .pipe(shareReplay(1));
    }
    return this.cachedChannel;
  
  }
}
