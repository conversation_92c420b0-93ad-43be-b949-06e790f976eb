import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { PageNotFoundComponent } from '@loadmonitor/shared/components/page-not-found/page-not-found.component';
import { HomePageComponent } from '@loadmonitor/pages/home-page/home-page.component';
import { AuthFailedComponent } from '@loadmonitor/pages/auth-failed/auth-failed.component';
import { LoggedOutComponent } from '@loadmonitor/pages/logged-out/logged-out.component';
import { AuthRoute } from '@dwh/lmx-lib/src/lib/constants';
import { RedirectWebComponent } from '@dwh/lmx-lib/src/lib/components/redirect-web/redirect-web.component';
import { AppComponent } from './app.component';
import { AuthGuard } from './shared/guards/auth.guard';

const routes: Routes = [
  { path: AuthRoute.REDIRECT, component: RedirectWebComponent },
  {
    path: 'home',
    canActivate: [AuthGuard],
    component: HomePageComponent,
  },
  {
    path: '',
    canActivate: [],
    component: AppComponent,
  },
  {
    path: '',
    redirectTo: '/',
    pathMatch: 'full',
  },
  {
    path: 'auth-failed',
    component: AuthFailedComponent,
  },
  {
    path: 'logged-out',
    component: LoggedOutComponent,
  },
  {
    path: 'ranged-weighting-import',
    canActivate: [ AuthGuard ],
    loadChildren: () =>
      import(
        '@loadmonitor/pages/ranged-weighting-import/ranged-weighting-import.module'
        ).then((m) => m.RangedWeightingImportModule),
  },
  {
    path: 'logging-qc',
    canActivate: [ AuthGuard ],
    loadChildren: () =>
      import('@loadmonitor/pages/logging-qc/logging-qc.module').then(
        (m) => m.LoggingQcModule,
      ),
  },
  {
    path: 'coverage-imports',
    canActivate: [ AuthGuard ],
    loadChildren: () =>
      import(
        '@loadmonitor/pages/coverage-imports/coverage-imports.module'
        ).then((m) => m.CoverageImportsModule),
  },
  {
    path: 'bcr-reworks',
    canActivate: [ AuthGuard ],
    loadChildren: () =>
      import('@loadmonitor/pages/bcr-reworks/bcr-reworks.module').then(
        (m) => m.BcrReworksModule,
      ),
  },

  {
    path: 'csv-imports',
    canActivate: [AuthGuard ],
    loadChildren: () =>
      import('@loadmonitor/pages/csv-imports/csv-imports.module').then(
        (m) => m.CsvImportsModule,
      ),
  },
  {
    path: 'data-order',
    canActivate: [ AuthGuard ],
    loadChildren: () =>
      import('@loadmonitor/pages/data-order/data-order.module').then(
        (m) => m.DataOrderModule,
      ),
  },
  {
    path: 'dwhrelease',
    canActivate: [ AuthGuard ],
    loadChildren: () =>
      import('@loadmonitor/pages/dwhrelease/dwhrelease.module').then(
        (m) => m.DwhReleaseModule,
      ),
  },
  {
    path: 'dwh-qc',
    canActivate: [AuthGuard  ],
    loadChildren: () =>
      import('@loadmonitor/pages/dwh-qc/dwh-qc.module').then(
        (m) => m.DwhQcModule,
      ),
  },
  {
    path: 'export',
    canActivate: [AuthGuard  ],
    loadChildren: () =>
      import('@loadmonitor/pages/export/export.module').then(
        (m) => m.ExportModule,
      ),
  },
  {
    path: 'feature-move',
    canActivate: [ AuthGuard ],
    loadChildren: () =>
      import(
        '@loadmonitor/pages/feature-move/feature-move.module'
        ).then((m) => m.FeatureMoveModule),
  },
  {
    path: 'item-master-data',
    canActivate: [ AuthGuard ],
    loadChildren: () =>
      import(
        '@loadmonitor/pages/item-master-data/item-master-data.module'
        ).then((m) => m.ItemMasterDataModule),
  },
  {
    path: 'ld-general',
    canActivate: [AuthGuard  ],
    loadChildren: () =>
      import('@loadmonitor/pages/ld-general/ld-general.module').then(
        (m) => m.LdGeneralModule,
      ),
  },
  {
    path: 'migration-rule-runs',

    canActivate: [ AuthGuard ],
    loadChildren: () =>
      import(
        '@loadmonitor/pages/migration-rule-runs/migration-rule-runs.module'
        ).then((m) => m.MigrationRuleRunsModule),
  },
  {
    path: 'publish-republish',
    canActivate: [ AuthGuard ],
    loadChildren: () =>
      import('./pages/publish-republish/publish-republish.module').then(
        (m) => m.PublishRepublishModule,
      ),
  },
  {
    path: 'shop-master-data',
    canActivate: [ AuthGuard ],
    loadChildren: () =>
      import(
        '@loadmonitor/pages/shop-master-data/shop-master-data.module'
        ).then((m) => m.ShopMasterDataModule),
  },
  {
    path: '**',
    component: PageNotFoundComponent,
  },
];

@NgModule({
  imports: [RouterModule.forRoot(routes, {})],
  exports: [RouterModule],
})
export class AppRoutingModule {
}
