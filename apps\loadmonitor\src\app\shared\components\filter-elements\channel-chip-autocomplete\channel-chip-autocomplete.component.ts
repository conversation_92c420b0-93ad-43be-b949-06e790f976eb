import { Component, ElementRef, ViewChild, OnInit, Input, SimpleChanges, OnChanges } from '@angular/core';
import { FormControl } from '@angular/forms';
import { TagItem } from '@loadmonitor/shared/interfaces/TagItem';
import { ChannelService } from '@loadmonitor/shared/services/channel.service';
import { EMPTY, Observable } from 'rxjs';
import { map, startWith } from 'rxjs/operators';
import { get } from 'lodash';
import { COMMA, ENTER } from '@angular/cdk/keycodes';
import { MatAutocompleteSelectedEvent } from '@angular/material/autocomplete';

@Component({
	selector: 'lm-channel-chip-autocomplete',
	templateUrl: './channel-chip-autocomplete.component.html',
	styleUrls: ['./channel-chip-autocomplete.component.scss'],
})
export class ChannelChipAutocompleteComponent implements OnInit, OnChanges {
	@ViewChild('channelIdsInput')
	channelIdsInput!: ElementRef<HTMLInputElement>;
	@Input() cachedChannelIds: number[] = [];

	public channelIds: TagItem[] = [];

	readonly separatorKeysCodes = [ENTER, COMMA] as const;

	// Mat-Chips - Channel
	channelIdsCtrl = new FormControl();
	filteredChannelIds: Observable<TagItem[]> | undefined;
	allChannelIds: TagItem[] = [];
	selectedChannelsIds!: Observable<TagItem[]>;

	constructor(private channelService: ChannelService) {}
	ngOnInit() {
		this.fillChannels();
		this.selectedChannelsIds = this.channelIdsCtrl.valueChanges.pipe(
			startWith(EMPTY),
			map(() => this.filterSelectedChannelIds())
		);
		this.filteredChannelIds = this.channelIdsCtrl.valueChanges.pipe(
			startWith(null),
			map((unitType) => this.setChannelFilter(get(unitType, 'name', unitType)))
		);
		this.refreshSuggestionsAndEmptyInput();
	}

	// Channel Chips
	removeChannelChips(item: TagItem): void {
		this.toggleSelection(item);
	}

	optionClicked(event: Event, item: TagItem) {
		event.stopPropagation();
		this.toggleSelection(item);
	}
	toggleSelection(item: TagItem) {
		item.selected = !item.selected;
		this.refreshSuggestionsAndEmptyInput();
	}
	filterSelectedChannelIds(): TagItem[] {
		return this.allChannelIds.filter((channel) => channel.selected);
	}

	removeAllChannels() {
		this.filterSelectedChannelIds().map((x) => (x.selected = false));
		this.refreshSuggestionsAndEmptyInput();
	}
	public setSelectedChannelIds(channelIds: number[]): void {
		channelIds?.forEach((channelId: number) => {
			const found = this.allChannelIds.find((tagItm: TagItem) => tagItm.id === channelId);
			if (found) {
				found.selected = true;
			} else {
				this.allChannelIds.push({ id: channelId, name: 'Loading...', selected: true });
			}
		});
	}
	refreshSuggestionsAndEmptyInput() {
		if (this.channelIdsInput != undefined) this.channelIdsInput.nativeElement.value = '';
		this.channelIdsCtrl.reset();
	}
	public getSelectedChannelIds() {
		return this.filterSelectedChannelIds().map((channel) => channel.id);
	}
	fillChannels(): void {
		this.channelService.getAsync().subscribe((result) => {
			const allChannelIds = result.map((r) => {
				const tag: TagItem = { id: r.id, name: r.name, selected: false };
				return tag;
			});
			this.allChannelIds = allChannelIds.sort((firstId: TagItem, otherId: TagItem) => firstId.name.localeCompare(otherId.name));
			this.setSelectedChannelIds(this.cachedChannelIds);
			this.refreshSuggestionsAndEmptyInput();
		});
	}
	selectedChannelChips(event: MatAutocompleteSelectedEvent): void {
		this.toggleSelection(event.option.value);
		this.channelIdsCtrl.setValue(null);
	}
	private setChannelFilter(value: string): TagItem[] {
		if (value === '' || value === null) {
			return this.allChannelIds;
		} else {
			const filterValue = value.toLowerCase();
			return this.allChannelIds.filter((channel) => channel.name.toLowerCase().indexOf(filterValue) === 0);
		}
	}

	ngOnChanges(changes: SimpleChanges){
		this.allChannelIds.forEach(function(i){ i.selected=false })
		this.setSelectedChannelIds(changes.cachedChannelIds.currentValue);
		this.refreshSuggestionsAndEmptyInput();
	  }
}
