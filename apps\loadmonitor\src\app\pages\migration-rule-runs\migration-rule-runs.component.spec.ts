import { ComponentFixture, TestBed } from '@angular/core/testing';
import { MigrationRuleRunsComponent } from './migration-rule-runs.component';
import { MigrationRuleRunsService } from '@loadmonitor/shared/services/migration-rule-runs.service';
import { MatSnackBar } from '@angular/material/snack-bar';
import { FormBuilder } from '@angular/forms';
import { HelperService } from '@loadmonitor/shared/services/helper.service';
import { FilterService } from '@dwh/lmx-lib';
import { DatePipe } from '@angular/common';
import { FilterTrayCacheService } from '@loadmonitor/shared/services/filter-tray-cache.service';
import { of } from 'rxjs';

describe('MigrationRuleRunsComponent', () => {
  let component: MigrationRuleRunsComponent;
  let fixture: ComponentFixture<MigrationRuleRunsComponent>;
  let migrationRuleRunsService: jest.Mocked<MigrationRuleRunsService>;
  let snackBar: jest.Mocked<MatSnackBar>;
  let formBuilder: FormBuilder;
  let helperService: jest.Mocked<HelperService>;
  let filterService: jest.Mocked<FilterService>;
  let datePipe: jest.Mocked<DatePipe>;
  let filterTrayCacheService: jest.Mocked<FilterTrayCacheService>;

  beforeEach(() => {
    migrationRuleRunsService = {
      getAsync: jest.fn().mockReturnValue(of({ count: 0, records: [], moreRecordsAvailable: false })),
      getDetailAsync: jest.fn().mockReturnValue(of({})),
    } as unknown as jest.Mocked<MigrationRuleRunsService>;

    snackBar = {
      openFromComponent: jest.fn(),
    } as unknown as jest.Mocked<MatSnackBar>;

    formBuilder = new FormBuilder();
    
    helperService = {
      floatingPanelData$: { next: jest.fn() },
    } as unknown as jest.Mocked<HelperService>;

    filterService = {
      setFilters: jest.fn(),
      filtersSelected$: of({}),
    } as unknown as jest.Mocked<FilterService>;

    datePipe = {
      transform: jest.fn().mockReturnValue('2024-01-01'),
    } as unknown as jest.Mocked<DatePipe>;

    filterTrayCacheService = {
      getCacheValueData: jest.fn(),
    } as unknown as jest.Mocked<FilterTrayCacheService>;

    TestBed.configureTestingModule({
      declarations: [MigrationRuleRunsComponent],
      providers: [
        { provide: MigrationRuleRunsService, useValue: migrationRuleRunsService },
        { provide: MatSnackBar, useValue: snackBar },
        { provide: FormBuilder, useValue: formBuilder },
        { provide: HelperService, useValue: helperService },
        { provide: FilterService, useValue: filterService },
        { provide: DatePipe, useValue: datePipe },
        { provide: FilterTrayCacheService, useValue: filterTrayCacheService },
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(MigrationRuleRunsComponent);
    component = fixture.componentInstance;
  });

  it('should create the component', () => {
    expect(component).toBeTruthy();
  });

  // Add more tests for other methods and functionalities as needed
});
