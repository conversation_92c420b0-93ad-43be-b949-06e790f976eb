.alignleft {
    float: left;
  }
  
  .alignright {
    float: right
  }
  .asterisk {
    padding-left: 1px;
    padding-right: 9px;
    color: red;
  }
  .txt{
    font-family: Arial, Helvetica, sans-serif;
    background: #FFFFFF;
    color: #333;
    border: 1px solid #A4A4A4;
    padding: 4px 8px 4px 4px !important;
    line-height: 1;
    width: 575px;
    height:25px;
    margin-top: 10px;
  }
   .txt:hover {
    box-shadow: inset 0px 1px 2px rgba(0,0,0,0.3);
    -moz-box-shadow: inset 0px 1px 2px rgba(0,0,0,0.3);
    -webkit-box-shadow: inset 0px 1px 2px rgba(0,0,0,0.3);
  }
   .txt:focus {
    border: 1px solid #4d90fe;
    outline: none;
    box-shadow: inset 0px 1px 2px rgba(0,0,0,0.3); 
    -moz-box-shadow: inset 0px 1px 2px rgba(0,0,0,0.3);
    -webkit-box-shadow: inset 0px 1px 2px rgba(0,0,0,0.3);
    background: rgb(255, 255, 255);
  }
  textarea{
    width: 590px;
    min-height: 174px;
    max-height: 200px;
    max-width: 590px;
    min-width: 590px;
    background: #fff;
    border: 1px #dfe1e5 solid;
    outline: none;
    vertical-align: top;
    box-sizing:border-box;
    margin-left: 3px;
  }
  //new
  .centered-container {
    width: auto;
    
    color: white;
    background:#ffffff;
  }
  .background {
    margin: -24px;
    padding: 0;
    background:#F4F6F4;
 }
  
  .header {
      height:50px;
      background:#F4F6F4;
      color: #696461;
      font-weight: 700;
      padding-left: 20px;
      padding-top: 20px;
  }
  .note
  {
    background:#DEEBF4;
    color: #696461;
    height: 30px;
    border-radius: 3px;
margin: 20px 20px;
padding: 5px 5px;
  }
  .main
  {
    background:#ffffff;
    color: #827d7a;
    height: auto;
    width: auto;
    margin-top: 30px;
    margin-left: 150px;
    margin-right: 150px;
    margin-bottom: 30px;
  }
  .footer {
    height:auto;
    background:#F4F6F4;
      color: #696461;
      font-weight: 700;
      padding-right: 20px;

}
  
h5 {
  display: inline;
}
  .content {
      height:auto;
      background:#ffffff;
  }

  .error
  {
      margin-left: 75px;
      font-size: xx-small;
      color: red;
  }