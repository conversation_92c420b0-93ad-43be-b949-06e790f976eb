import { UserCache } from '@loadmonitor/shared/interfaces/caching/UserCache';
import { Country } from '../Country';
import { JobType } from '../JobType';
import { DomainProductGroups } from '../DomainProductGroups';
import { Category } from '../Category';
import { BaseProjectType } from '../BaseProjectType';
import { Sector } from '../Sector';

export interface DwhReleaseCache
{
    bpTypeIds: number[];
    baseProjectType: BaseProjectType[];
    jobTypeIds: number[];
    sectorIds: number[];
    categoryIds: number[];
    domainProductGroupIds: number[];
    domainProductGroup: DomainProductGroups[];
    countryIds: number[];
    status: number[];
    selectedDays: string;
    startDate?: Date;
    endDate?: Date;
    users?: UserCache[];
    loadIds: number[],
    jobIds: number[],
    qcProjectIds: number[],
    baseProjectIds: number[],
    qcProjectName: string,
    periodicityIds: number[];
    periodIds: any;
    isAutoRefresh?: boolean;
    countries:Country;
    jobType: JobType[];
    category:Category[];
    sector:Sector[];
}
