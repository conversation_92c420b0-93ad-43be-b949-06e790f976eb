<mat-form-field class="chip-list" appearance="outline">
  <mat-label>Export Ids</mat-label>
  <mat-chip-list #cl_exportIds aria-label="Export Ids selection">
    <mat-chip *ngFor="let item of exportIds" [selectable]="true" [removable]="true"
      (removed)="removeChips(item, exportIds)">
      {{ item }}
      <mat-icon matChipRemove *ngIf="true">cancel</mat-icon>
    </mat-chip>
    <input placeholder="New..." [matChipInputFor]="cl_exportIds"
      [matChipInputSeparatorKeyCodes]="separatorKeysCodes" [matChipInputAddOnBlur]="true"
      (matChipInputTokenEnd)="addChips($event, exportIds)"
      class="js-filter-inp-export-id" />
  </mat-chip-list>
</mat-form-field>
