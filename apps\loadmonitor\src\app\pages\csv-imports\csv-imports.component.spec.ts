import { ComponentFixture, TestBed } from '@angular/core/testing';
import { CsvImportsComponent } from './csv-imports.component';
import { MatTableDataSource } from '@angular/material/table';
import { CsvImportService } from '@loadmonitor/shared/services/csvImport.service';
import { of } from 'rxjs';
import { FormBuilder } from '@angular/forms';
import { MatSnackBar } from '@angular/material/snack-bar';
import { MatDialog } from '@angular/material/dialog';
import { FilterTrayCacheService } from '@loadmonitor/shared/services/filter-tray-cache.service';
import { DatePipe } from '@angular/common';

describe('CsvImportsComponent', () => {
    let component: CsvImportsComponent;
    let fixture: ComponentFixture<CsvImportsComponent>;
    let csvImportService: jest.Mocked<CsvImportService>;
    let snackBar: jest.Mocked<MatSnackBar>;
    let dialog: jest.Mocked<MatDialog>;
    let filterTrayCacheService: jest.Mocked<FilterTrayCacheService>;

    beforeEach(async () => {
        csvImportService = {
            getAsync: jest.fn(),
            getDetailAsync: jest.fn(),
        } as unknown as jest.Mocked<CsvImportService>;
        
        snackBar = {
            openFromComponent: jest.fn(),
        } as unknown as jest.Mocked<MatSnackBar>;

        dialog = {
            open: jest.fn(),
        } as unknown as jest.Mocked<MatDialog>;

        filterTrayCacheService = {
            getCacheValueData: jest.fn(),
        } as unknown as jest.Mocked<FilterTrayCacheService>;

        await TestBed.configureTestingModule({
            declarations: [CsvImportsComponent],
            providers: [
                FormBuilder,
                { provide: CsvImportService, useValue: csvImportService },
                { provide: MatSnackBar, useValue: snackBar },
                { provide: MatDialog, useValue: dialog },
                { provide: FilterTrayCacheService, useValue: filterTrayCacheService },
                { provide: DatePipe, useValue: new DatePipe('en-US') },
            ],
        }).compileComponents();

        fixture = TestBed.createComponent(CsvImportsComponent);
        component = fixture.componentInstance;
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });

    // Additional tests can be added here for other methods and functionalities.
});
