import { ItemMasterData } from '../interfaces/ItemMasterData';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { HttpClient } from '@angular/common/http';
import { ConfigService } from './config.service';
import { Jobs } from '@loadmonitor/shared/interfaces/Jobs';

@Injectable({
  providedIn: 'root',
})
export class ItemMasterDataService {
  private url!:any;

  constructor(private http: HttpClient, private config: ConfigService) {
    this.url = `${this.config.getApiUrl()}/ItemMasterData`;
  }

  getAsync(
    countryIds?: number[],
    sectorIds?: number[],
    categoryIds?: number[],
    domainProductGroupIds?: number[],
    userNames?: string[],
    statusIds?: number[],
    prepUnitIds?: number[],
    loadUnitIds?: number[],
    jobIds?: number[],
    domainProductGroupType?: number | null,
    imlJobTypes?: string[],
    startDate?: Date,
    endDate?: any
  ): Observable<Jobs<ItemMasterData>> {
    const body = {
      countryIds,
      sectorIds,
      categoryIds,
      domainProductGroupIds,
      userNames,
      statusIds,
      prepUnitIds,
      loadUnitIds,
      jobIds,
      domainProductGroupType,
      imlJobTypes,
      startDate,
      endDate
    };
    return this.http.post<Jobs<ItemMasterData>>(this.url, body);
  }
}
