<div class="history-filter">
	<div>
		<mat-form-field  appearance="outline" class="date width-100">
			<mat-select  [(value)]="selected" placeholder="Days" (selectionChange)="changeHistory($event.value)" id="dateInterval-input" class="js-filter-sel-days">
				<mat-option value="1">Date Interval</mat-option>
				<mat-option value="2">Today</mat-option>
				<mat-option value="3">Last 3 days</mat-option>
			</mat-select>
		</mat-form-field>
	</div>
	<div>
		<mat-form-field appearance="outline" class="date width-100">
			<mat-date-range-input [formGroup]="dateRange" [rangePicker]="dateRangePicker" [disabled]="IsDateRangeDisabled">
				<input
					matStartDate
					placeholder="Start date"
					formControlName="start"
					#dateRangeStart
					(dateChange)="dateChange()"
					class="js-filter-inp-start-date"/>
				<input
					matEndDate
					placeholder="End date"
					formControlName="end"
					#dateRangeEnd
					id="endDate-input"
					(dateChange)="dateChange()"
					class="js-filter-inp-end-date"/>
			</mat-date-range-input>
			<mat-datepicker-toggle matSuffix [for]="dateRangePicker"></mat-datepicker-toggle>
			<mat-date-range-picker #dateRangePicker></mat-date-range-picker>
		</mat-form-field>
	</div>
</div>
