---
apiVersion: backstage.io/v1alpha1
kind: Component
metadata:
  name: loadmonitoruI
  title: LoadMonitorUI
  annotations:
    # grafana/dashboard-selector: "(tags @> 'kubernetes-mixin')"
    gitlab.com/project-id: '519'
    gitlab.com/project-slug: dp/de/products/load-monitor/loadmonitor-ui
    sonarqube.org/project-key: 'DWH.LoadMonitor.UI'
    #backstage.io/kubernetes-id: ''
    #backstage.io/kubernetes-label-selector: ''
  description: 'Load Monitor UI'
  tags:
    - k8s
    - nodejs
    - angular
spec:
  lifecycle: production
  owner: titans
  type: service
  system: startrack
  dependsOn:
    - Component:urm-api
