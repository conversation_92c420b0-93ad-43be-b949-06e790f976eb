import { JobTypes } from '@loadmonitor/shared/jobTypes';
import {
	Component,
	OnInit,
	ViewChild,
	AfterContentInit,
	AfterViewInit,
	ChangeDetectorRef,
	Input,
	OnDestroy,
	HostListener,
} from '@angular/core';
import { COMMA, ENTER } from '@angular/cdk/keycodes';
import { FormBuilder, FormGroup } from '@angular/forms';
import { Subject, Subscription } from 'rxjs';
import { catchError, takeUntil } from 'rxjs/operators';
import { MatSnackBar } from '@angular/material/snack-bar';
import { DefaultSnackbarComponent } from '@loadmonitor/shared/components/default-snackbar/default-snackbar.component';
import { BcrReworks } from '@loadmonitor/shared/interfaces/BcrReworks';
import { MatTable, MatTableDataSource } from '@angular/material/table';
import { MatSort } from '@angular/material/sort';
import { MatPaginator } from '@angular/material/paginator';
import { BcrReworksService } from '@loadmonitor/shared/services/bcr-reworks.service';
import { SettingsConstants } from '@loadmonitor/shared/settings.constants';
import { BcrReworksCache } from '@loadmonitor/shared/interfaces/caching/BcrReworksCache';
import { CreateTicketDialogComponent } from '@loadmonitor/shared/components/create-ticket-dialog/create-ticket-dialog.component';
import { BcrReworksJiraProperty } from '@loadmonitor/shared/interfaces/BcrReworksJiraProperty';
import { MatDialog } from '@angular/material/dialog';
import { SelectionModel } from '@angular/cdk/collections';
import { Filter, FilterRecords, FilterService, FilterTrayComponent } from '@dwh/lmx-lib/src';
import { environment } from 'apps/loadmonitor/src/environments/environment';
import { ToggleButtonComponent } from '@gfk/ng-lib';
import { DatePipe } from '@angular/common';
import * as moment from 'moment';
import { ConfigService } from '@loadmonitor/shared/services/config.service';
import { FilterTrayCacheService } from '@loadmonitor/shared/services/filter-tray-cache.service';

@Component({
	selector: 'lm-bcr-reworks',
	templateUrl: './bcr-reworks.component.html',
	styleUrls: ['./bcr-reworks.component.scss'],
})
export class BcrReworksComponent implements OnInit, AfterContentInit, AfterViewInit, OnDestroy {

	@ViewChild(MatSort)
	sort: MatSort = new MatSort();

	@ViewChild(MatTable)
	table!: MatTable<BcrReworks>;

	@ViewChild(MatPaginator)
	paginator!: MatPaginator;

	@ViewChild(FilterTrayComponent) filterTrayComponent: FilterTrayComponent | undefined;
	@ViewChild(ToggleButtonComponent) togglebuttonComponent: ToggleButtonComponent | undefined;

	//Mat-Expansion-Panel
	panelOpenState = false;

	jobType: JobTypes = JobTypes.bcrRework;
	IsSelectAll = false;
	//Tag-Chip-List
	readonly separatorKeysCodes = [ENTER, COMMA] as const;

	// Progress Spinner visibility
	isLoading = false;

	public cacheObj: BcrReworksCache = {} as BcrReworksCache;
	cacheName = 'cache::BcrReworks';
	eventsSubject: Subject<void> = new Subject<void>();
	filterCount = '';
	IsDisabled = false;
	environments = environment.environments;
	IsEnabled = false;
	FilterTrayOpenFlag = true;
	modalshow = false;
	EnableRefresh = true;
	toggleInterval!: any;
	timer = 60;
	cacheInterval!: any;
	defaultoption = localStorage.getItem(this.cacheName + '-LastFilter') || 'Default';
	defaultfilter = JSON.parse(localStorage.getItem(this.cacheName + '-DefaultFilter') || '[]');
	currentfilter = JSON.parse(localStorage.getItem('QuickFilter') || '[]').filter(
		(x) => x.user == localStorage.getItem('username') && x.slice == window.location.href.split('/')[3]
	);
	/* *********************
	 * Components parameters
	 ********************* */

	//Mat-Table
	dataSource = new MatTableDataSource<BcrReworks>();
	selection = new SelectionModel<BcrReworks>(true, []);
	displayedColumns: string[] = [
		'menu',
		'loadId',
		'bcRearrangeId',
		'status',
		'country',
		'domainProductGroup',
		'channel',
		'feature',
		'featureValue',
		'fromPeriod',
		'toPeriod',
		'baseChannel',
		'addTargetRbbp',
		'outletId',
		'createdBy',
		'created',
		'started',
		'finished',
		'message',
		'jobId',
		'jeeJobInfo',
	];

	BCRReworksForm!: FormGroup;
	readonly date_with_time = SettingsConstants.DATE_FORMAT_WITH_TIME;
	@Input() icon = 'delete';
	private subscription!: Subscription | undefined;
	refreshedDateTime!: boolean;
	date!: Date;
	selectedFilters: any;
	resetForm: Subject<boolean> = new Subject();
	version: string;
	bcrReworksData!: any[];


	@HostListener('document:visibilitychange', ['$event'])
	appVisibility() {
		if (document.hidden) {
			if (this.cacheInterval) {
				clearInterval(this.cacheInterval);
			}
			if (this.toggleInterval) {
				clearInterval(this.toggleInterval);
			}
		} else {
			if (this.cacheObj.isAutoRefresh === true) {
				this.cacheInterval = setInterval(() => {
					if (this.defaultoption != 'Default') {
						this.refresh();
					}
				}, this.timer * 1000);
			} else {
				if (this.cacheInterval !== undefined) clearInterval(this.cacheInterval);
				if (this.toggleInterval !== undefined) clearInterval(this.toggleInterval);
			}
		}
	}

	// eslint-disable-next-line @typescript-eslint/member-ordering
	sliceFields: any = {
		countries: 'countries',
		users: 'users',
		status: 'status',
		domainProductGroup: 'domainProductGroup',
		category: 'category',
		sector: 'sector',
		selectedDays: 'selectedDays',
		startDate: 'startDate',
		endDate: 'endDate',
		feature: 'feature',
		loadIds: 'loadIds',
		jobIds: 'jobIds',
		bcRearrangedIds: 'bcRearrangedIds',
		channelIds: 'channelIds',
		baseChannelIds: 'baseChannelIds'
  	}
	private destroy$ = new Subject<void>();

	constructor(
		private bcrReworksService: BcrReworksService,
		private snackBar: MatSnackBar,
		private formBuilder: FormBuilder,
		private cdRef: ChangeDetectorRef,
		public dialog: MatDialog,
		public filterService: FilterService,
		public datepipe: DatePipe,
		private configService: ConfigService,
		private filterTrayCacheService: FilterTrayCacheService
	) {
		const packageVersion = this.configService.getVersion();
		this.version = packageVersion.substring(packageVersion.indexOf('_') + 1);
	}

  	ngOnDestroy() {
		if (this.cacheInterval) {
			clearInterval(this.cacheInterval);
		}

		if (this.toggleInterval) {
			clearInterval(this.toggleInterval);
		}
		this.destroy$.next();
	    this.destroy$.complete();
	}

	private updateFC(): void {
		if(this.cacheObj){
			this.filterTrayCacheService.getCacheValueData(this.cacheObj);
		}
  	}

	ngOnInit(): void {
		if (this.environments.filter((x) => x.location == window.location.origin)[0]) {
			this.IsEnabled = this.environments.filter((x) => x.location == window.location.origin)[0].enabled;
		} else {
			this.IsEnabled = false;
		}
		this.BCRReworksForm = this.formBuilder.group({
			isAutoRefresh: this.togglebuttonComponent?.checked,
		});
		this.dataSource.paginator = this.paginator;
		// this.updateFC();
		this.SetFiltersCount();
	}

	ngAfterContentInit() {
		this.afterChildComponentsLoaded();
		this.CreateFilterSummaryChips();
		this.SetFiltersCount();
		if (this.cacheObj.isAutoRefresh === true) {
			if (this.cacheInterval === undefined) {
				this.cacheInterval = setInterval(() => {
					if (this.defaultoption != 'Default') {
						this.refresh();
					}
				}, this.timer * 1000);
			}
		} else {
			if (this.cacheInterval !== undefined) clearInterval(this.cacheInterval);
			if (this.toggleInterval !== undefined) clearInterval(this.toggleInterval);
		}
	}

	ngAfterViewInit() {
		this.cdRef.detectChanges();
	}

	afterChildComponentsLoaded() {
		this.cacheObj = JSON.parse(localStorage.getItem(this.cacheName) || '{}');
		this.updateFC();
	}

	toggleChecked(check: any) {
		if (check === true) {
			this.saveInCache(check, true);
			this.toggleInterval = setInterval(() => {
				if (this.defaultoption != 'Default') {
					this.refresh();
				}
			}, this.timer * 1000);
		} else {
			if (this.toggleInterval !== undefined) clearInterval(this.toggleInterval);
			if (this.cacheInterval !== undefined) clearInterval(this.cacheInterval);
			this.saveInCache(check, true);
		}
	}

	setFormValue(autoRefreshStatus) {
		this.BCRReworksForm.setValue({
			isAutoRefresh: autoRefreshStatus === undefined || autoRefreshStatus === false ? false : true,
		});
	}

	saveInQuickFilter(autoRefreshStatus, filterName) {
		this.setFormValue(autoRefreshStatus);
		this.currentfilter = JSON.parse(localStorage.getItem('QuickFilter') || '[]');
		this.currentfilter.push({
			data: this.selectedFilters,
			filter: filterName,
			slice: window.location.href.split('/')[3],
			user: localStorage.getItem('username'),
		});
		this.defaultoption = filterName;
		const oldcache = this.cacheObj;
		const olddefault = localStorage.getItem(this.cacheName + '-DefaultFilter');
		this.ConfirmApply(autoRefreshStatus);
		this.cacheObj = oldcache;
		localStorage.setItem(this.cacheName + '-DefaultFilter', olddefault || '[]');
		localStorage.setItem('QuickFilter', JSON.stringify(this.currentfilter));
		localStorage.setItem(this.cacheName + '-LastFilter', filterName);
		this.SetFiltersCount();
	}

	applyfilter(selectedvalue) {
		this.resetForm.next(true);
		const oldautotoggle = this.cacheObj.isAutoRefresh;
		selectedvalue = selectedvalue.value ? selectedvalue.value : selectedvalue;
		this.defaultoption = selectedvalue;
		localStorage.setItem(this.cacheName + '-LastFilter', this.defaultoption || 'Default');
		if (selectedvalue == 'Default') {
			if (
				JSON.parse(localStorage.getItem(this.cacheName + '-DefaultFilter') || '[]').filter(
					(x) => x.user == localStorage.getItem('username') && x.slice == window.location.href.split('/')[3]
				)[0]
			) {
				this.cacheObj = JSON.parse(localStorage.getItem(this.cacheName + '-DefaultFilter') || '[]').filter(
					(x) => x.user == localStorage.getItem('username') && x.slice == window.location.href.split('/')[3]
				)[0]['data'];
			}

			localStorage.setItem(this.cacheName, JSON.stringify(this.cacheObj));
		} else {
			this.cacheObj = JSON.parse(localStorage.getItem('QuickFilter') || '[]')
				.filter((x) => x.user == localStorage.getItem('username') && x.slice == window.location.href.split('/')[3])
				.filter((x) => x.filter == selectedvalue)[0]['data'];
			localStorage.setItem(this.cacheName, JSON.stringify(this.cacheObj));
		}
		this.cacheObj.isAutoRefresh = oldautotoggle;
		this.CreateFilterSummaryChips();

		this.selectedFilters = this.cacheObj;
		if (this.cacheObj.selectedDays == '3') {
			const date = new Date();
			this.cacheObj.startDate = new Date(Date.UTC(date.getFullYear(), date.getMonth(), date.getDate() - 2, 0, 0, 0, 0));
			this.cacheObj.endDate = new Date(Date.UTC(date.getFullYear(), date.getMonth(), date.getDate(), 0, 0, 0, 0));
		} else if (this.cacheObj.selectedDays == '2') {
			const date = new Date();
			this.cacheObj.startDate = new Date(Date.UTC(date.getFullYear(), date.getMonth(), date.getDate(), 0, 0, 0, 0));
			this.cacheObj.endDate = new Date(Date.UTC(date.getFullYear(), date.getMonth(), date.getDate(), 0, 0, 0, 0));
		}
		setTimeout(() => {
			this.updateFC();
			this.refresh();
		}, 200);

		localStorage.setItem(this.cacheName, JSON.stringify(this.cacheObj));
	}
	public isShownFilterTrayEventHandler($event: boolean) {
		this.filterTrayComponent?.toggleShowTray($event);
		this.FilterTrayOpenFlag = $event;
	}

	getWidth() {
		this.FilterTrayOpenFlag = !this.FilterTrayOpenFlag;
		return '100%';
	}

	openmodal() {
		this.eventsSubject.next();
	}

	disablesave(status) {
		this.IsDisabled = status;
	}

	ConfirmApply(autoRefreshStatus) {
		this.saveInCache(autoRefreshStatus, false);
		this.CreateFilterSummaryChips();
    	this.getSelectedFilters(this.cacheObj);
		let count;
		this.filterService.filtersSelected$.subscribe((selectedFilters: FilterRecords) => {
			count = Number(Object.keys(selectedFilters).length);
		});

		if (count > 0) {
			this.refresh();
		} else {
			if (confirm('Loading results without any filters selected will take some time to load. Are you sure you want to proceed?')) {
				this.refresh();
			}
		}
	}

	CreateFilterSummaryChips() {
		if (this.cacheObj && Object.keys(this.cacheObj).length) {
			this.countryFilterSummary();
			this.sectorIdsFilterSummary();
			this.categoryIdsFilterSummary();
			this.domainProductGroupIdsFilterSummary();
			this.SourceBaseFilterSummary();
			this.TargetVirtualBaseFilterSummary();
			this.usersFilterSummary();
			this.statusFilterSummary();
			this.jeeJobIdsFilterSummary();
			this.loadIdsFilterSummary();
			this.bcrRearrangeIdsFilterSummary();
			this.featureFilterSummary();
			this.datesFilterSummary(this.cacheObj.startDate, this.cacheObj.endDate);
		}
	}

	countryFilterSummary() {
		this.filterService.setFilters({
			countries: {
				name: 'Country',
				values: this.cacheObj.countries,
				slice: window.location.href.split('/')[3],
			} as Filter,
		});
	}

	usersFilterSummary() {
		this.filterService.setFilters({
			userIds: {
				name: 'User',
				values: this.cacheObj.users?.map((i) => i.name),
				slice: window.location.href.split('/')[3],
			} as Filter,
		});
	}

	statusFilterSummary() {
		this.filterService.setFilters({
			statusIds: {
				name: 'Status',
				values: this.cacheObj.status,
				slice: window.location.href.split('/')[3],
			} as Filter,
		});
	}

	SourceBaseFilterSummary() {
		this.filterService.setFilters({
			SourceBase: {
				name: 'SourceBase',
				values: this.cacheObj.channelIds,
				slice: window.location.href.split('/')[3],
			} as Filter,
		});
	}

	TargetVirtualBaseFilterSummary() {
		this.filterService.setFilters({
			TargetVirtualBase: {
				name: 'TargetVirtualBase',
				values: this.cacheObj.baseChannelIds,
				slice: window.location.href.split('/')[3],
			} as Filter,
		});
	}

	domainProductGroupIdsFilterSummary() {
		this.filterService.setFilters({
			domainProductGroupIds: {
				name: 'Domain',
				values: this.cacheObj.domainProductGroup,
				slice: window.location.href.split('/')[3],
			} as Filter,
		});
	}

	sectorIdsFilterSummary() {
		this.filterService.setFilters({
			sectorIds: {
				name: 'sectorIds',
				values: this.cacheObj.sector,
				slice: window.location.href.split('/')[3],
			} as unknown as Filter,
		});
	}

	categoryIdsFilterSummary() {
		this.filterService.setFilters({
			categoryIds: {
				name: 'categoryIds',
				values: this.cacheObj.category,
				slice: window.location.href.split('/')[3],
			} as Filter,
		});
	}

	loadIdsFilterSummary() {
		this.filterService.setFilters({
			loadUnitIds: {
				name: 'loadIds',
				values: this.cacheObj.loadIds,
				slice: window.location.href.split('/')[3],
			} as Filter,
		});
	}

	jeeJobIdsFilterSummary() {
		this.filterService.setFilters({
			jeeJobIds: {
				name: 'jeeJobIds',
				values: this.cacheObj.jobIds,
				slice: window.location.href.split('/')[3],
			} as Filter,
		});
	}

	bcrRearrangeIdsFilterSummary() {
		this.filterService.setFilters({
			BcrRearrangeIds: {
				name: 'BcrRearrangeIds',
				values: this.cacheObj.bcRearrangedIds,
				slice: window.location.href.split('/')[3],
			} as Filter,
		});
	}

	featureFilterSummary() {
		const _feature = [] as any[];
		if (this.cacheObj.feature) {
			_feature.push(this.cacheObj.feature);
		}

		this.filterService.setFilters({
			Feature: {
				name: 'Feature',
				values: _feature,
				slice: window.location.href.split('/')[3],
			} as Filter,
		});
	}

	datesFilterSummary(dateStart: any, dateEnd: any) {
		if (dateStart != null || dateEnd != null) {
			const start = dateStart == 'Invalid Date' || dateStart == null ? '' : this.datepipe.transform(dateStart, 'yyyy-MM-dd');
			const end = dateEnd == 'Invalid Date' || dateEnd == null ? '' : this.datepipe.transform(dateEnd, 'yyyy-MM-dd');
			this.filterService.setFilters({
				dates: {
					name: 'Date',
					values: (start !== undefined && end !== undefined) ? [start, end] : null,
					slice: window.location.href.split('/')[3],
				} as Filter,
			});
		} else {
			this.filterService.setFilters({
				dates: {
					name: 'Date',
					values: null,
					slice: window.location.href.split('/')[3],
				} as unknown as Filter,
			});
		}
	}

	saveInCache(autoRefreshStatus, AutoToggle) {
		this.setFormValue(autoRefreshStatus);
		localStorage.setItem(this.cacheName, JSON.stringify(this.selectedFilters));
		this.cacheObj = JSON.parse(localStorage.getItem(this.cacheName) || '{}');
		if (!AutoToggle) {
			this.defaultfilter = [];
			this.defaultfilter.push({
				data: this.selectedFilters,
				slice: window.location.href.split('/')[3],
				user: localStorage.getItem('username'),
			});
			localStorage.setItem(this.cacheName + '-DefaultFilter', JSON.stringify(this.defaultfilter));
			let nofilter = 0;
			let selectedfilter = '';
			this.currentfilter.forEach((i) => {
				if(JSON.stringify(i.data) == JSON.stringify(this.cacheObj)){
					nofilter++;
					selectedfilter = i.filter;
				}
			});
			if (nofilter == 0) {
				this.defaultoption = 'Default';
			} else {
				this.defaultoption = selectedfilter;
			}
		}
		localStorage.setItem(this.cacheName + '-LastFilter', this.defaultoption);
		this.updateFC();
	}

	refresh(): void {
		this.refreshedDateTime = false;
		this.filterTrayComponent?.showSpinner(true);
		this.isLoading = true;
		this.destroy$.next();		
		this.bcrReworksService
			.getAsync(
				this.selectedFilters.countries?.map((country) => country),
				this.selectedFilters.sector?.map((sector) => sector),
				this.selectedFilters.category?.map((category) => category.id),
				this.selectedFilters.domainProductGroup?.map((domainProductGroup) => domainProductGroup.id),
				this.selectedFilters.channelIds?.map((channel) => channel),
				this.selectedFilters.users?.map((users) => users.userName),
				this.selectedFilters.status?.map((status) => status),
				this.selectedFilters.baseChannelIds?.map((baseChannel) => baseChannel),
				this.selectedFilters.jobIds,
				this.selectedFilters.loadIds,
				this.selectedFilters.bcRearrangedIds,
				this.selectedFilters.feature,
				this.selectedFilters.startDate ? this.selectedFilters.startDate : null,
				this.selectedFilters.endDate ? moment(this.selectedFilters.endDate).format().split('T')[0] + 'T23:59:59.999Z' : null,
			)
			.pipe(
				takeUntil(this.destroy$),
				catchError((error) => {
					this.isLoading = false;
					return error;
				})
			)
			.subscribe((result: any) => {
				this.refreshedDateTime = true;
				if (result.moreRecordsAvailable) {
					this.notify('More than 1000 results exist!');
				}

				if (result.count === 0) {
					this.notify('No result found!');
					this.bcrReworksData = [];
				}
				this.isLoading = false;
				this.bcrReworksData = result.records;
				this.dataSource = new MatTableDataSource<BcrReworks>(result.records);
				this.dataSource.sort = this.sort;
				this.paginator.pageIndex = 0;
				this.dataSource.paginator = this.paginator;
				this.table.dataSource = this.dataSource;
				this.filterTrayComponent?.showSpinner(false);
			},
			(error) => {
				if (error.status === 504) {
					this.notify('Gateway Timeout. The server took too long to respond. Please try again later.');
				} 
				else {
					this.notify(`An error occurred: ${error.message}`);
				}
				this.isLoading = false;
				this.filterTrayComponent?.showSpinner(false);
			});
		this.SetFiltersCount();
	}

	SetFiltersCount() {
		let count;
		this.filterService.filtersSelected$.subscribe((selectedFilters: FilterRecords) => {
			count = Number(Object.keys(selectedFilters).length);
		});
		if (count > 0) {
			this.filterCount = '(' + count.toString() + ')';
		} else {
			this.filterCount = '';
		}
	}
	/**
	 * JUST FOR DEBUG! send a request with a preset data and fill the explorer data source
	 */
	// debugSubmit(): void {
	// 	if (this.history === undefined) {
	// 		this.history = {} as HistoryFilterComponent;
	// 	}
	// 	this.history.startDate = new Date('05/09/2018');
	// 	this.history.endDate = new Date('06/09/2018');

	// 	this.refresh();
	// }

	private notify(message: string) {
		this.snackBar.openFromComponent(DefaultSnackbarComponent, {
			duration: 10 * 1000,
			data: message,
		});
	}

	rowToggleSelection(ev, item) {
		item.isChecked = ev.checked;
	}

	checkAtleastOneRowChecked() {
		return this.dataSource.data.filter((i) => i.isChecked).length > 0;
	}

	openJiraTicket(): void {
		const checkedList = this.dataSource.data.filter((i) => i.isChecked);
		const bcrReworksJiraProperties: Array<BcrReworksJiraProperty> = new Array<BcrReworksJiraProperty>();

		checkedList.map(function (value) {
			bcrReworksJiraProperties.push({
				country: value.country,
				productGroup: value.domainProductGroup,
				bcrRearrangeId: value.bcrRearrangeId,
			});
		});

		const dialogRef = this.dialog.open(CreateTicketDialogComponent, {
			width: '1000px',
			height: '487px',
			data: {
				bcrReworksJiraProperties: bcrReworksJiraProperties,
				jobType: this.jobType,
				title: 'BcrReworks',
			},
		});

		dialogRef.afterClosed().subscribe((resSubmit) => {
			if (resSubmit === true) {
				this.dataSource.data.forEach((i) => (i.isChecked = false));
				this.selection.clear();
				this.IsSelectAll = false;
			}
		});
	}

	selectRows(selectrow) {
		this.IsSelectAll = !this.IsSelectAll;
		let endIndex: number;
		const page = this.dataSource.paginator !== null ? this.dataSource.paginator.pageSize : 0;
		const pageindex = this.dataSource.paginator !== null ? this.dataSource.paginator.pageIndex : 0;

		if (!selectrow.checked) {
			this.selection.clear();
			if (this.dataSource.data.length > (pageindex + 1) * page) {
				endIndex = (pageindex + 1) * page;
			} else {
				endIndex = this.dataSource.data.length;
			}

			for (let index = pageindex * page; index < endIndex; index++) {
				const row = this.dataSource.data[index];
				this.selection.deselect(row);
				this.dataSource.data[index].isChecked = false;
			}
		} else {
			if (this.dataSource.data.length > (pageindex + 1) * page) {
				endIndex = (pageindex + 1) * page;
			} else {
				endIndex = this.dataSource.data.length;
			}
			for (let index = pageindex * page; index < endIndex; index++) {
				const row = this.dataSource.sortData(this.dataSource.data, this.sort)[index];
				this.selection.select(row);
				this.dataSource.sortData(this.dataSource.data, this.sort)[index].isChecked = true;
			}
		}
	}

	isSelectedPage() {
		const numSelected = this.selection.selected.length;
		const page = this.dataSource.paginator !== null ? this.dataSource.paginator.pageSize : 0;
		const pageindex = this.dataSource.paginator !== null ? this.dataSource.paginator.pageIndex : 0;
		let endIndex: number;
		if (this.dataSource.data.length > (pageindex + 1) * page) {
			endIndex = (pageindex + 1) * page;
		} else {
			endIndex = this.dataSource.data.length - pageindex * page;
		}
		return numSelected === endIndex;
	}

	filterSummaryChipClear() {
		if (confirm('This will remove all of your selected filters and could not be reversed. Are you sure you want to do that?')) {
			this.resetForm.next(true);
			this.filterTrayCacheService.getCacheNameData(this.cacheName);
			this.saveInCache(this.cacheObj.isAutoRefresh, false);
			this.CreateFilterSummaryChips();
			this.SetFiltersCount();
		}
	}


	getSelectedFilters(selectedFilters: any){
		this.cacheObj = selectedFilters;
		this.selectedFilters = selectedFilters;
	}
}
