import { Injectable } from '@angular/core';
import { BaseProjectType } from '../interfaces/BaseProjectType';

@Injectable({
  providedIn: 'root'
})
export class BaseProjectTypeSelectService {
	private bpTypeIds: number[] = [];
	private bpTypeNames: string[] = [];
  private allBaseProjectType!: BaseProjectType[];

	constructor() {
    this.fillBaseProjectTypes();
  }

	public fillBaseProjectTypes() {
	  this.allBaseProjectType = [
      { id: 1, name: 'Industry', selected: false },
      { id: 2, name: 'Retailer', selected: false },
      { id: 3, name: 'Industry/Retailer', selected: false },
      { id: 4, name: 'IDAS DST', selected: false },
    ];
    const allBaseProjectType = this.allBaseProjectType.sort((firstService: BaseProjectType, otherService: BaseProjectType) => firstService.name.localeCompare(otherService.name));
    return allBaseProjectType;
	}

	getSelectedBPTypeIds(bpType: BaseProjectType[]): number[] {
		this.bpTypeIds = [];
		if (bpType) {
			bpType.forEach((i) => {
				this.bpTypeIds.push(i.id);
			});
			return this.bpTypeIds;
		} else return [];
	}

	getSelectedBPTypeNames(bptypename: BaseProjectType[]): string[] {
		this.bpTypeNames = [];
		if (bptypename) {
			bptypename.forEach((i) => {
				this.bpTypeNames.push(i.name);
			});
			return this.bpTypeNames;
		} else return [];
	}
}
