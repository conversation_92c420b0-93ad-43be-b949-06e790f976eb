<div *ngIf="configuration && configuration.length > 0; else noMenuItems">
  <button testId="lmx-button" mat-icon-button [matMenuTriggerFor]="menu" class="js-btn-detail-menu-dots">
    <mat-icon>more_vert</mat-icon>
  </button>

  <mat-menu #menu="matMenu">
    <ng-container *ngIf="configuration.length > 0; else noMenuItems">
      <button testId="lmx-button" mat-menu-item *ngFor="let element of configuration" (click)="openContext(element.service, element.params, element.isShowDetails, element.isMessageLink, element.isFTXLink, element.link)" class="js-btn-menu-element-{{element.service}}">
        <mat-icon>{{ element.menuIcon }}</mat-icon>
        <span>{{ element.menuText }}</span>
      </button>
    </ng-container>
  </mat-menu>


</div>
<ng-template #noMenuItems>
  <button testId="lmx-button" mat-icon-button disabled="disabled" class="js-btn-detail-menu-dots" title="No available actions">
    <mat-icon>more_vert</mat-icon>
  </button>
</ng-template>


<!-- The Modal -->
<gfk-modal [triggerModal]="modalShow" modalTitle="DWH Messages" width="65%" height="500px"
  [cancelDisabled]="false"
  (onAction)="closeModal(); modalShow=false">
  <table>
    <div *ngIf="records.length > 0; then thenBlock else elseBlock"></div>
     <ng-template #thenBlock>
     <tr>
    
       <th>Message Date</th>
       <th>Message</th>

     </tr>
     
     <tr *ngFor="let element of records">
      
       <td>{{element.messageDate | date: dateTime }}</td>
       <td>{{element.message}}</td>
     </tr>
   </ng-template>
   <ng-template #elseBlock>
     <tr>
       <td>No messages to show.</td>
     </tr>
   </ng-template>
  </table>
</gfk-modal>