import { UserCache } from '@loadmonitor/shared/interfaces/caching/UserCache';
import { Country } from '../Country';
import { ProductionProjectType } from '../ProductionProjectType';

export interface DataOrderCache {
    dataOrderIds: number[];
    productionProjectName: string;
    productionProjectIds: number[];
    periodicityNames: string[];
    productionProjectTypeIds: number[];
    productionProjectTypeNames: string[];
    productionProjectType: ProductionProjectType[];
    countryIds: number[];
    countryNames: string[];
    periodicityIds: number[];
    periodIds: any;
    status: number[];
    statusdescription: string[];
    selectedDays: string;
    startDate?: Date;
    endDate?: Date;
    users?: UserCache[];
    isAutoRefresh?: boolean;
    countries: Country[];
  }
