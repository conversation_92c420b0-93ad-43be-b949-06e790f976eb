import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { HttpClient } from '@angular/common/http';
import { ConfigService } from './config.service';
import { Jobs } from '@loadmonitor/shared/interfaces/Jobs';
import { PublishRepublish } from '../interfaces/PublishRepublish';

@Injectable({
  providedIn: 'root',
})
export class PublishRepublishService {
  private url!:any;

  constructor(private http: HttpClient, private config: ConfigService) {
    this.url = `${this.config.getApiUrl()}/publishrepublish` ;
  }

  // TODO: test and maybe rework
  getAsync(filterParams: PublishRepublishFilterParams): Observable<Jobs<PublishRepublish>> {
    return this.http.post<Jobs<PublishRepublish>>(this.url, filterParams);
  }
}

export interface PublishRepublishFilterParams {
  rbProjectIds?: number[],
  loadIds?: number[],
  jeeJobIds?: number[],
  countryIds?: number[],
  operations?: string[],
  categoryIds?: number[],
  sectorIds?: number[],
  domainProductGroupIds?: number[],
  periodicityIds?: number[],
  periodIds?: number[],
  rbProjectName?: string,
  statusIds?: number[],
  projectTypeIds?: number[],
  userNames?: string[],
  startDate?: Date,
  endDate?: any
}
