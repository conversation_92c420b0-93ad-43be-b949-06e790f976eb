import { DWHQC } from '../interfaces/dwh-qc';
import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { HttpClient } from '@angular/common/http';
import { ConfigService } from './config.service';
import { Jobs } from '@loadmonitor/shared/interfaces/Jobs';

@Injectable({
  providedIn: 'root',
})
export class DWHQCService {
  private url!:string;
  
  constructor(private http: HttpClient, private config: ConfigService) {
    this.url = `${this.config.getApiUrl()}/BaseprojectPeriods`;
  }

  getAsync(
    userNames?: string[],
    qcProjectIds?: number[],
    baseProjectIds?: number[],
    countryIds?: number[],
    domainProductGroupIds?: number[],
    categoryIds?: number[],
    sectorIds?: number[],
    periodIds?: number[],
    periodicityIds?: number[],
    baseProjectName?: string,
    baseProjectTypeIds?: number[],
    qcProjectflag?: boolean | null,
    startDate?: Date,
    endDate?: any
  ): Observable<Jobs<DWHQC>> {
    const body = {
      userNames,
      qcProjectIds,
      baseProjectIds,
      countryIds,
      domainProductGroupIds,
      categoryIds,
      sectorIds,
      periodIds,
      periodicityIds,
      baseProjectTypeIds,
      qcProjectflag,
      baseProjectName,
      startDate,
      endDate
    };
    return this.http.post<Jobs<DWHQC>>(this.url, body);
  }

}
