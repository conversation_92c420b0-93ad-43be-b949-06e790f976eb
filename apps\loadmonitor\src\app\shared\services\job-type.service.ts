import { Injectable } from '@angular/core';
import { JobType } from '../interfaces/JobType';
import { JobTypes } from '../jobTypes';

@Injectable({
  providedIn: 'root',
})
export class JobTypeService {
    
    private allJobTypeIds: { id: number; name: string; selected: boolean; }[] = [];

    getAsync(jobType: JobTypes) {
        switch (jobType) {
            case JobTypes.DWHRelease:
            this.allJobTypeIds = [
                { id: 1, name: 'CreateShop Execute', selected: false },
                { id: 2, name: 'CreateShop Template', selected: false },
                { id: 3, name: 'Dwh2Dwh', selected: false },
                { id: 4, name: 'Release', selected: false },
                { id: 5, name: 'Unrelease', selected: false }
            ];
            break;
    
            case JobTypes.itemMasterData:
                this.allJobTypeIds = [
                { id: 1, name: 'BasePG Encoding', selected: false },
                { id: 2, name: 'Prepare Item Masterdata', selected: false },
                { id: 3, name: 'Load Item Masterdata', selected: false },
                { id: 4, name: 'Rework Exclusivity', selected: false }
            ];
            break;
    
        }
        this.allJobTypeIds = this.allJobTypeIds.sort((firstService: JobType, otherService: JobType) => firstService.name.localeCompare(otherService.name));
        return this.allJobTypeIds;
    }

}
