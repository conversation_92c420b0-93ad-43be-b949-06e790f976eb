import { UserCache } from '@loadmonitor/shared/interfaces/caching/UserCache';
import { Category } from '../Category';
import { Country } from '../Country';
import { DomainProductGroups } from '../DomainProductGroups';
import { JobType } from '../JobType';
import { Sector } from '../Sector';

export interface ItemMasterDataCache {
    sectorIds: number[];
    categoryIds: number[];
    domainProductGroupType: any;
    countryIds: number[];
    status: number[];
    selectedDays: string;
    startDate?: Date;
    endDate?: Date;
    users?: UserCache[];
    loadUnitIds:number[];
    prepUnitIds:number[];
    jobIds:number[];
    domainPgTypeId:number;
    jobTypeIds: number[];
    jobType: JobType[];
    isAutoRefresh?: boolean;
    countries: Country[];
    domainProductGroup: DomainProductGroups[];
    category:Category[];
    sector:Sector[];
  }
