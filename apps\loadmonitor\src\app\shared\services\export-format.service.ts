import { Injectable } from "@angular/core";
import { Observable } from 'rxjs';
import { HttpClient } from '@angular/common/http';
import { ConfigService } from './config.service';
import { ExportFormat } from '../interfaces/ExportFormat';
import { shareReplay } from "rxjs/operators";

@Injectable({
  providedIn: 'root',
})

export class ExportFormatService {
  private url!:string;

  private cachedExportFormat! : Observable<ExportFormat[]>;
  constructor(private http: HttpClient, private config: ConfigService){
    this.url = `${this.config.getApiUrl()}/ExportFormats`;
  }

  getAsync(): Observable<ExportFormat[]>{
    if(!this.cachedExportFormat)
    {
      this.cachedExportFormat = this.http
        .get<ExportFormat[]>(this.url)
        .pipe(shareReplay(1));
    }
    return this.cachedExportFormat;
  }
}
