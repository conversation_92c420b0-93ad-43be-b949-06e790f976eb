import { Component, OnInit, inject } from '@angular/core';
import { AuthFacade, UserService } from '@dwh/lmx-lib/src/lib/services/auth';
import { map } from 'rxjs';

@Component({
	selector: 'lm-app-root',
	templateUrl: './app.component.html',
	styleUrls: ['./app.component.scss'],
})
export class AppComponent implements OnInit {
	private readonly authFacade = inject(AuthFacade);
	private readonly userService = inject(UserService);
	userName$ = this.authFacade.user$.pipe(
		map((user: any) => {
			if (user) {
				const username = `${user.email}`;
				localStorage.setItem('Email', user.email);
				return username;
			}
			return '';
		})
	);
	
	ngOnInit(): void {
		this.userService.signInUser();
		this.authFacade.registerAuthentication();
		this.userName$.subscribe();
	}
}
