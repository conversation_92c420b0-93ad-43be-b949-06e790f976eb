import { COMM<PERSON>, ENTER } from '@angular/cdk/keycodes';
import { Component, ElementRef, Input, ViewChild, OnInit, Output, EventEmitter, SimpleChanges, OnChanges } from '@angular/core';
import { FormControl } from '@angular/forms';
import { User } from '@loadmonitor/shared/interfaces/User';
import { UserCache } from '@loadmonitor/shared/interfaces/caching/UserCache';
import { UserService } from '@loadmonitor/shared/services/user.service';
import { EMPTY, Observable, of } from 'rxjs';
import { debounceTime, startWith, switchMap, map } from 'rxjs/operators';

@Component({
  selector: 'lm-user-chip-autocomplete',
  templateUrl: './user-chip-autocomplete.component.html',
  styleUrls: ['./user-chip-autocomplete.component.scss'],
})
export class UserChipAutocompleteComponent implements OnInit, OnChanges {
  @ViewChild('userIdsInput')
  private userIdsInput!: ElementRef<HTMLInputElement>;

  @Input() cachedUsers: UserCache[] = [];
  @Output() valueChanges = new EventEmitter<User[]>();

  readonly separatorKeysCodes = [ENTER, COMMA] as const;

  userFormCtrl = new FormControl(EMPTY);
  filteredUsers!: Observable<User[] | null>;
  selectedUsers!: Observable<User[]>;
  selected: User[] = [];

  constructor(private userService: UserService) {

  }

  ngOnInit() {

    this.filteredUsers = this.userFormCtrl.valueChanges.pipe(
      startWith(''),
      debounceTime(500),
      switchMap((value) => {
        const paramValue = (value === null) ? '' : value.toString();
        return value !== '' ? this.getUsersAfterAdjustment(paramValue) : of(null);
      })
    );

    this.selectedUsers = this.userFormCtrl.valueChanges.pipe(
      map(() =>
        this.filterSelectedUsers(),
      ),
    );

    this.selectedUsers.subscribe((userIds) => {
      this.valueChanges.emit(userIds);
    });

  }
  ngOnChanges(changes: SimpleChanges){
    this.selected=[];
    changes.cachedUsers.currentValue.forEach((i) => {
      this.selected.push({ userId: i.id, userName: i.name, selected: true, firstName: "", lastName: "" });
    });
  }

  public removeAllUsers() {
    this.filterSelectedUsers().map((user) => user.selected = false);
    this.selected.splice(0,this.selected.length);
    this.refreshSuggestionsAndEmptyInput();
  }

  filterSelectedUsers(): User[] {
    return this.selected.filter(
      (users) => users.selected,
    );
  }
  getSelectedUsers() {
    return this.selected.map((i) => { return <UserCache>{ id: i.userId, name: i.userName } });
  }

  getUsersAfterAdjustment(value: string) {
    return this.userService.getAsync(value).pipe(
      map((response) => {
        this.selected.filter(i => i.selected).forEach((s) => {
          response.forEach((r) => {
            if (r.userId == s.userId) { r.selected = s.selected }
          })
        })
        return response
      }
      )
    );
  }

  removeUserChips(item: User): void {
    this.toggleSelection(item);
  }

  optionClicked(event: Event, item: User) {
    event.stopPropagation();
    this.toggleSelection(item);
  }

  toggleSelection(item: User) {
    item.selected = !item.selected;
    if (item.selected) {
      this.selected.push(item);
    } else {
      const index = this.selected.findIndex(value => value.userId === item.userId);
      this.selected.splice(index, 1);
    }
    this.refreshSuggestionsAndEmptyInput();
  }

  refreshSuggestionsAndEmptyInput() {
    this.userFormCtrl.reset();
    this.userIdsInput.nativeElement.value = '';
    this.userFormCtrl.setValue(null);

  }
}
