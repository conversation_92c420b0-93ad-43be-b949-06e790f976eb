import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { HttpClient } from '@angular/common/http';
import { ConfigService } from './config.service';
import { Operation } from '@loadmonitor/shared/interfaces/Operation';
import { shareReplay } from 'rxjs/operators';

@Injectable({
  providedIn: 'root',
})
export class OperationService {
  private url!:string;

  private cachedOperation! : Observable<Operation[]>;
  constructor(private http: HttpClient, private config: ConfigService) {
    this.url = `${this.config.getApiUrl()}/operations`;
  }

  getAsync(): Observable<Operation[]> {
    if(!this.cachedOperation)
    {
      this.cachedOperation = this.http
        .get<Operation[]>(this.url)
        .pipe(shareReplay(1));
    }
    return this.cachedOperation;
  }
}
