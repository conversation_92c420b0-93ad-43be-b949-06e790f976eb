import { TestBed } from '@angular/core/testing';
import { ConfigService } from './config.service';
import { PeriodService } from './period.service';
import { HttpClientTestingModule } from '@angular/common/http/testing';

describe('PeriodService', () => {
  let service: PeriodService;
  let configService: ConfigService;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [ConfigService]
    });

    service = TestBed.inject(PeriodService);
    configService = TestBed.inject(ConfigService);
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });
});
