<div class="background">
  <form [formGroup]="formdata" (ngSubmit)="onClickSubmit(formdata.value)">
    <div class="centered-container">
      <div class="header">
        Create JIRA ticket
      </div>
      <div class="content" [ngSwitch]="data.jobType">
        <div *ngSwitchCase="JobTypes.ldGeneral">
          <ng-container *ngTemplateOutlet="tmplDefault"></ng-container>
        </div>
        <div *ngSwitchCase="JobTypes.export">
          <ng-container *ngTemplateOutlet="tmplDefault"></ng-container>
        </div>
        <div *ngSwitchCase="JobTypes.rbPublishUnpublish">
          <ng-container *ngTemplateOutlet="tmplDefault"></ng-container>
        </div>
        <div *ngSwitchCase="JobTypes.DataOrder">
          <ng-container *ngTemplateOutlet="tmplDefault"></ng-container>
        </div>
        <div *ngSwitchCase="JobTypes.itemMasterData">
          <ng-container *ngTemplateOutlet="tmplDefault"></ng-container>
        </div>
        <div *ngSwitchCase="JobTypes.DWHRelease">
          <ng-container *ngTemplateOutlet="tmplDefault"></ng-container>
        </div>
        <div *ngSwitchCase="JobTypes.bcrRework">

          <ng-container *ngTemplateOutlet="tmplDefault"></ng-container>
        </div>
        <div *ngSwitchCase="JobTypes.coverageImport">
          <ng-container *ngTemplateOutlet="tmplDefault"></ng-container>
        </div>
        <div *ngSwitchCase="JobTypes.CsvImport">
          <ng-container *ngTemplateOutlet="tmplDefault"></ng-container>
        </div>

       <ng-template #tmplDefault>
          <div class="note">
            <h5>Please provide details about the issue. Summary is a required field.</h5>
          </div>
          <div class="main">
            <h5>Summary </h5><span class="asterisk">*</span>
            <input required class="txt" type="text" name="summary" formControlName="summary">
            <div class="error" *ngIf="summary.errors?.['required'] && (summary.dirty || summary.touched)">
              Summary is required.
            </div>
            <br />
            <h5>Description </h5>
            <textarea class="txt" type="text" name="description" formControlName="description"></textarea>
            <br />
          </div>
        </ng-template>
      </div>
      <div class="footer">

        <mat-dialog-actions align="end">

          <button testId="lmx-button" type="submit" mat-raised-button color="primary" [matDialogClose]="true"
            [disabled]="!formdata.valid">Submit</button>
          <button testId="lmx-button" mat-button mat-dialog-close>Close</button>
        </mat-dialog-actions>
      </div>
    </div>
  </form>
</div>
