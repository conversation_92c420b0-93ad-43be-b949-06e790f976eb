import { ComponentFixture, TestBed } from '@angular/core/testing';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { SharedModule } from '@loadmonitor/shared/shared.module';

import { ExportIdChipInputComponent } from './export-id-chip-input.component';

describe('ExportIdChipInputComponent', () => {
  let component: ExportIdChipInputComponent;
  let fixture: ComponentFixture<ExportIdChipInputComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ExportIdChipInputComponent],
      imports: [SharedModule, NoopAnimationsModule],
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(ExportIdChipInputComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
