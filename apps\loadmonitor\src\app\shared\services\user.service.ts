import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { HttpClient } from '@angular/common/http';
import { ConfigService } from './config.service';
import { User } from '@loadmonitor/shared/interfaces/User';

@Injectable({
  providedIn: 'root',
})
export class UserService {
  private url!:string;

  constructor(private http: HttpClient, private config: ConfigService) {
    this.url = `${this.config.getApiUrl()}/Users/<USER>/`;
  }

  getAsync(query: string): Observable<User[]> {
    if(query==null)query='';
    return this.http.get<User[]>(this.url + query);
  }
}
