import { Injectable } from '@angular/core';
import { ProductionProjectType } from '../interfaces/ProductionProjectType';

@Injectable({
  providedIn: 'root',
})
export class ProductionProjectTypeSelectService {

  private productionProjectTypeNumId: number[] = [];
  private productionProjectTypeNames: string[] = [];

  getSelectedProductionProjectTypeIds(productionProjectTypes: ProductionProjectType[]): number[] {
		this.productionProjectTypeNumId = [];
		if (productionProjectTypes) {
			productionProjectTypes.forEach((i) => {
				this.productionProjectTypeNumId.push(i.id);
			});
			return this.productionProjectTypeNumId;
		}
		else
		return [];
	}

  getSelectedProductionProjectTypeNames(productionProjectTypes: ProductionProjectType[]): string[] {
		this.productionProjectTypeNames = [];
		if (productionProjectTypes) {
			productionProjectTypes.forEach((i) => {
				this.productionProjectTypeNames.push(i.name);
			});
			return this.productionProjectTypeNames;
		}
		else
		return [];
	}
}
