import { COMM<PERSON>, ENTER } from '@angular/cdk/keycodes';
import { Component, EventEmitter, forwardRef, Input, Output } from '@angular/core';
import { ControlValueAccessor, FormControl, NG_VALUE_ACCESSOR, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatChipInputEvent, MatChipsModule } from '@angular/material/chips';
import { Observable } from 'rxjs';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatOptionModule } from '@angular/material/core';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { MatIconModule } from '@angular/material/icon';
import { CommonModule } from '@angular/common';

const materialComponents = [
	MatCheckboxModule,
	MatOptionModule,
	MatAutocompleteModule,
	MatChipsModule,
	MatFormFieldModule,
	MatIconModule,
	CommonModule,
];

@Component({
	selector: 'lmx-chip-numeric',
	templateUrl: './chip-numeric.component.html',
	styleUrls: ['./chip-numeric.component.scss'],

	providers: [
		{
			provide: NG_VALUE_ACCESSOR,
			useExisting: forwardRef(() => ChipNumericComponent),
			multi: true,
		},
	],
	standalone: true,
	imports: [ReactiveFormsModule, ...materialComponents],
})
export class ChipNumericComponent implements  ControlValueAccessor {
	numericControl: FormControl;
	isValid = false;
	numberRegEx = /^[0-9]*$/;

	constructor() {
		this.numericControl = new FormControl(null, {
			validators: Validators.pattern(this.numberRegEx),
		});
	}

	@Input() label = 'numeric chip';
	@Input() ids: number[] = [];
	@Input() placeholder!: string;
	@Input() id!: string;
	@Output() valueChanges = new EventEmitter<any[]>();
	//@ViewChild('input') inputEl!: ElementRef<HTMLInputElement>;

	selectedIds$!: Observable<number[]>;
	readonly separatorKeysCodes = [ENTER, COMMA] as const;



	removeAllIds() {
		this.ids.splice(0, this.ids.length);
		this.numericControl.reset();
	}

	addChips(event: MatChipInputEvent): void {
		const value = (event.value || '').trim();
		if (Number(value) !== 0) {
			if (!Number(value) || Number(value) < 0) {
				return;
			}
		}

		if (value && this.ids.indexOf(Number(value)) < 0) {
			this.ids.push(Number.parseInt(value, 10)); //Push if valid
		}
		this.onChange(this.ids);
		event.chipInput?.clear();
	}

	removeChips(item: number): void {
		const index = this.ids.indexOf(item);

		if (index >= 0) {
			this.ids.splice(index, 1);
		}
		this.onChange(this.ids);
	}

	paste(event: ClipboardEvent): void {
		event.preventDefault(); //Prevents the default action
		event.clipboardData
			?.getData('Text')
			.split(',')
			.forEach((value) => {
				if (value.trim()) {
					if (Number(value) && this.ids.indexOf(Number(value)) < 0) {
						this.ids.push(Number.parseInt(value, 10)); //Push if valid
					}
				}
			});
	}

	// ngOnChanges(changes: SimpleChanges) {
	// 	this.ids = [];
	// 	changes.ids.currentValue.forEach((i) => {
	// 		this.ids.push(Number.parseInt(i, 10));
	// 	});
	// }

	onChange = (_: any) => {
		// This is intentionally left empty
	};
	onTouched = () => {
		// This is intentionally left empty
	};

	// Implement the ControlValueAccessor methods
	writeValue(value: any) {
    this.ids= value;
		this.selectedIds$ = value;
		this.onChange(value);
		if (!this.selectedIds$) {
			this.clearInput();
		}
	}

	registerOnChange(fn: any) {
		this.onChange = fn;
	}

	registerOnTouched(fn: any) {
		this.onTouched = fn;
	}

	onChipRemove(chip: any) {
		this.removeChips(chip);
	}

	private clearInput(): void {
    this.ids= [];
    this.numericControl.reset();
	}
}
