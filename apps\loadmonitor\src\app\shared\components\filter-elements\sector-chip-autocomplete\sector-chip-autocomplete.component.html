<mat-form-field class="chip-list" appearance="outline">
  <mat-label>Sector </mat-label>
  <mat-chip-list class="chip-list-wrapper" #chipListSector aria-label="Sector selection">
    <mat-chip *ngFor="let sector of sectorIds  | async" [selectable]="true" [removable]="true"
      (removed)="removeChipsTagItem(sector)">
      {{ sector.name }} ({{ sector.id }})
      <mat-icon matChipRemove>cancel</mat-icon>
    </mat-chip>
    <input placeholder="New ..." #sectorIdsInput [formControl]="sectorIdsCtrl" [matAutocomplete]="autoSector"
      [matChipInputFor]="chipListSector" [matChipInputSeparatorKeyCodes]="separatorKeysCodes"
      class="js-filter-inp-sector" />
  </mat-chip-list>
  <mat-autocomplete #autoSector="matAutocomplete" [autoActiveFirstOption]="true" (optionSelected)="selectedSectorChips($event)">
    <mat-option *ngFor="let sector of filteredSectorIds | async" [value]="sector">
      <div (click)="optionClicked($event, sector)">
        <mat-checkbox class="option-checkbox" [checked]="sector.selected?sector.selected:false" (change)="toggleSelection(sector)"
          (click)="$event.stopPropagation()">
          {{ sector.name }} ({{ sector.id }})
        </mat-checkbox>
      </div>
    </mat-option>
  </mat-autocomplete>
</mat-form-field>
