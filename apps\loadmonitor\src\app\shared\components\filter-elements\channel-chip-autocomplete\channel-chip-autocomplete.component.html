<mat-form-field class="chip-list" appearance="outline">
  <mat-label>Channel </mat-label>
  <mat-chip-list class="chip-list-wrapper" #chipListChannel aria-label="Channel selection">
    <mat-chip *ngFor="let item of selectedChannelsIds | async" [selectable]="true" [removable]="true"
      (removed)="removeChannelChips(item)">
      {{ item.name }}
      <mat-icon matChipRemove *ngIf="true">cancel</mat-icon>
    </mat-chip>
    <input placeholder="New ..." #channelIdsInput [formControl]="channelIdsCtrl" [matAutocomplete]="autoChannel"
      [matChipInputFor]="chipListChannel" [matChipInputSeparatorKeyCodes]="separatorKeysCodes"
      class="js-filter-inp-channel" />
  </mat-chip-list>
  <mat-autocomplete #autoChannel="matAutocomplete" [autoActiveFirstOption]="true" (optionSelected)="selectedChannelChips($event)">
    <mat-option *ngFor="let item of filteredChannelIds | async" [value]="item">
      <div (click)="optionClicked($event, item)">
        <mat-checkbox class="option-checkbox" [checked]="item.selected?item.selected: false" (change)="toggleSelection(item)"
          (click)="$event.stopPropagation()">
          {{ item.name }} ({{ item.id }})
        </mat-checkbox>
      </div>
    </mat-option>
  </mat-autocomplete>
</mat-form-field>
