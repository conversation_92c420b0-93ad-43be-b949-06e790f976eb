<lm-navigation></lm-navigation>
<div class="each-slice-top-headbar" [ngClass]="FilterTrayOpenFlag ? 'filter-tray-open' : 'filter-tray-close'">
	<h3 class="alignleft">Ranged Weighting Import</h3>
	<div class="flexdiv-buttons">
		<lmx-filter-button showFilterCount="{{ filterCount }}" (isShownFilterTray)="isShownFilterTrayEventHandler($event)"></lmx-filter-button>
	</div>
</div>

<lmx-filter-tray
	(ButtonShowHideEvent)="this.getWidth()"
	(clickApplyButton)="ConfirmApply(true)"
	(clickSaveButton)="openmodal()"
	[IsEnabled]="IsEnabled"
	[IsDisabled]="IsDisabled"
>

<lm-filter-tray-form
    [sliceName]="'RangedWeightingImport'"
    [resetForm]="resetForm"
    (selectedFilters)="getSelectedFilters($event)"
>
</lm-filter-tray-form>
</lmx-filter-tray>

<div class="filter-area" [ngClass]="FilterTrayOpenFlag ? 'filter-tray-open' : 'filter-tray-close'">
	<div class="toggle-button">
		<div>
			<lm-quick-filter
				[selectedfilter]="defaultoption"
				class="p-3.5"
				[events]="eventsSubject.asObservable()"
				*ngIf="IsEnabled"
				[modalshow]="modalshow"
				(saveInQuickFilter)="saveInQuickFilter(cacheObj.isAutoRefresh, $event)"
				(applyquickfilter)="applyfilter($event)"
				(disabledbutton)="disablesave($event)"
			>
			</lm-quick-filter>
			<lm-filter-summary (resetFilters)="filterSummaryChipClear($event)"></lm-filter-summary>
		</div>
		<div class="refreshedTime">
			<lm-refreshed-date-time *ngIf="refreshedDateTime" class="pr-3 d-inline-block"></lm-refreshed-date-time>
			<gfk-toggle-button
				*ngIf="EnableRefresh && IsEnabled"
				[checked]="cacheObj.isAutoRefresh || false"
				title="Auto Refresh"
				class="autoRefresh d-inline-block"
				[disabled]="defaultoption === 'Default'"
				(onChange)="toggleChecked($event)"
			>
			</gfk-toggle-button>
		</div>
	</div>
</div>

<article class="table-grid">
	<div class="mat-elevation-z8 tab-container" [ngClass]="(rangedWeightingImportData?.length) ? 'table-height' : ''">
		<table mat-table [dataSource]="dataSource" matSort class="full-width-table js-tbl-source-result">
			<ng-container matColumnDef="weightingId">
				<th mat-header-cell *matHeaderCellDef mat-sort-header>Weighting ID</th>
				<td mat-cell *matCellDef="let element" class="js-result-weighting-id">{{ element.weightingId }}</td>
			</ng-container>

			<ng-container matColumnDef="status">
				<th mat-header-cell *matHeaderCellDef mat-sort-header>Status</th>
				<td mat-cell *matCellDef="let element" class="js-result-status">{{ element.status }}</td>
			</ng-container>

			<ng-container matColumnDef="message">
				<th mat-header-cell *matHeaderCellDef mat-sort-header>Message</th>
				<td mat-cell *matCellDef="let element" class="js-result-message">{{ element.message }}</td>
			</ng-container>

			<ng-container matColumnDef="qcProjectId">
				<th mat-header-cell *matHeaderCellDef mat-sort-header>QC Project</th>
				<td mat-cell *matCellDef="let element" class="js-result-qc-project-id">{{ element.qcProjectId }}</td>
			</ng-container>

			<ng-container matColumnDef="baseProjectName">
				<th mat-header-cell *matHeaderCellDef mat-sort-header>Base Project</th>
				<td mat-cell *matCellDef="let element" class="js-result-base-project-name">{{ element.baseProjectName }}</td>
			</ng-container>

			<ng-container matColumnDef="username">
				<th mat-header-cell *matHeaderCellDef mat-sort-header>Username</th>
				<td mat-cell *matCellDef="let element" class="js-result-username">{{ element.username }}</td>
			</ng-container>

			<ng-container matColumnDef="notifyEmail">
				<th mat-header-cell *matHeaderCellDef mat-sort-header>Notify Email</th>
				<td mat-cell *matCellDef="let element" class="js-result-notify-email">{{ element.notifyEmail }}</td>
			</ng-container>

			<ng-container matColumnDef="jeeJobId">
				<th mat-header-cell *matHeaderCellDef mat-sort-header>JEE Job Id</th>
				<td mat-cell *matCellDef="let element" class="js-result-jee-job-id">{{ element.jeeJobId }}</td>
			</ng-container>

			<ng-container matColumnDef="jeeJobInfo">
				<th mat-header-cell *matHeaderCellDef>JEE Job Info</th>
				<td mat-cell *matCellDef="let element">
					<lm-jee-job-info jobId="{{ element.jeeJobId }}"></lm-jee-job-info>
				</td>
			</ng-container>

			<ng-container matColumnDef="periodDescription">
				<th mat-header-cell *matHeaderCellDef mat-sort-header>Period Desc.</th>
				<td mat-cell *matCellDef="let element" class="js-result-period-description">
					{{ element.periodDescription }}
				</td>
			</ng-container>

			<ng-container matColumnDef="created">
				<th mat-header-cell *matHeaderCellDef mat-sort-header>Created On</th>
				<td mat-cell *matCellDef="let element" class="js-result-created">
					{{ element.created | date: date_with_time }}
				</td>
			</ng-container>

			<ng-container matColumnDef="started">
				<th mat-header-cell *matHeaderCellDef mat-sort-header>Started On</th>
				<td mat-cell *matCellDef="let element" class="js-result-started">
					{{ element.started | date: date_with_time }}
				</td>
			</ng-container>

			<ng-container matColumnDef="finished">
				<th mat-header-cell *matHeaderCellDef mat-sort-header>Finished On</th>
				<td mat-cell *matCellDef="let element" class="js-result-finished">
					{{ element.finished | date: date_with_time }}
				</td>
			</ng-container>

			<tr mat-header-row *matHeaderRowDef="displayedColumns; sticky: true" class="columnHeader"></tr>
			<tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
		</table>
	</div>
	<mat-paginator [pageSizeOptions]="[10, 20, 50, 100]" showFirstLastButtons></mat-paginator>
</article>
<lm-version></lm-version>