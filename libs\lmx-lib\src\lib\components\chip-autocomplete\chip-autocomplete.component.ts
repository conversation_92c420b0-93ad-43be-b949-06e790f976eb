import {
  Component,
  ElementRef,
  EventEmitter,
  forwardRef,
  Input,
  OnChanges,
  OnDestroy,
  OnInit,
  Output,
  SimpleChanges,
  ViewChild,
} from '@angular/core';
import { ControlValueAccessor, FormControl, NG_VALUE_ACCESSOR, ReactiveFormsModule } from '@angular/forms';
import { COMMA, ENTER } from '@angular/cdk/keycodes';
import { MatAutocompleteModule, MatAutocompleteSelectedEvent } from '@angular/material/autocomplete';
import { CommonModule } from '@angular/common';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatOptionModule } from '@angular/material/core';
import { MatChipsModule } from '@angular/material/chips';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { Subject, takeUntil } from 'rxjs';

const materialComponents = [MatCheckboxModule, MatOptionModule, MatAutocompleteModule, MatChipsModule, MatFormFieldModule, MatIconModule];

export interface AutocompleteOption {
  id: number;
  name: string;
}

interface Option extends AutocompleteOption {
  isSelected: boolean;
}

export const CHIP_AUTOCOMPLETE_VALUE_ACCESSOR: any = {
  provide: NG_VALUE_ACCESSOR,
  useExisting: forwardRef(() => ChipAutocompleteComponent),
  multi: true,
};

@Component({
  selector: 'lmx-chip-autocomplete',
  templateUrl: './chip-autocomplete.component.html',
  styleUrls: ['./chip-autocomplete.component.scss'],
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule, ...materialComponents],
  providers: [CHIP_AUTOCOMPLETE_VALUE_ACCESSOR],
})
export class ChipAutocompleteComponent implements OnInit, OnChanges, OnDestroy, ControlValueAccessor {
  @Input() options: AutocompleteOption[] | null = [];
  @Input() label!: string;
  @Input() placeholder!: string;
  @Input() id!: string;
  @Output() val = new EventEmitter<string | null>();
  @Output() resetUserList = new EventEmitter();
  @ViewChild('input') inputEl!: ElementRef<HTMLInputElement>;

  inputFC = new FormControl<string>('');
  chips: AutocompleteOption[] = [];
  filtered: Option[] = [];
  selected: AutocompleteOption | AutocompleteOption[] | null = [];

  private _onChange!: (value: any) => void;
  private destroy$ = new Subject<void>();

  readonly SEPARATOR_KEYS_CODES = [ENTER, COMMA] as const;
  search!: boolean;

  inputclass = '';

  ngOnInit(): void {
    if (this.label) {
      const formattedLabel = this.label.replace(/ /g, '');
      this.inputclass = formattedLabel.charAt(0).toLowerCase() + formattedLabel.slice(1);
    } 
    else {
        // Handle the case when this.label is undefined
        this.inputclass = ''; // or some default value
    }
    this.inputFC.valueChanges.pipe(takeUntil(this.destroy$)).subscribe((value) => {
      this.val.emit(value);
      if (typeof value === 'string' && value !== '') {
        this.filtered = this.options?.filter((option) => option?.name?.toLocaleLowerCase().includes(value?.toLocaleLowerCase())) as Option[];
      } else {
        this.filtered = (this.options as Option[]) || [];
      }
    });
    this.filtered = (this.options?.filter((option) => !this.chips.includes(option)) as Option[]) || [];
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['options'].currentValue) {
      this.options = changes['options'].currentValue;
      this.updateChips();
      this.updatefilteredOptionList();
    }
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  writeValue(object: AutocompleteOption | AutocompleteOption[] | null): void {
    this.selected = object;
    this.updateChips();
  }

  updateChips(): void {
    if (!this.chips) {
      this.filtered.forEach((i) => (i.isSelected = false));
      this.chips = [];
      return;
    } 
    else {
      setTimeout(() => {
        this.filtered.forEach((i) => {
          this.chips?.forEach((selectedItem) => {
            if (selectedItem.id == i.id) {
              i.isSelected = true;
            }
          });
        });
      }, 100);
    }
  }

  registerOnChange(fn: (_: any) => void): void {
    this._onChange = fn;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars, @typescript-eslint/no-empty-function
  registerOnTouched(fn: any): void { }

  onOptionSelected(event: MatAutocompleteSelectedEvent): void {
    this.toggleSelection(event.option.value as Option);
  }

  onChipRemove(chip: AutocompleteOption) {
    this.removeChip(chip);
  }

  toggleSelection(item: Option): void {
    item.isSelected = !item.isSelected;
    const previousitem = item;
    if (item.isSelected) {
      this.addChip(item);
    } 
    else {
      this.removeChip(item);
    }
    item = previousitem;
  }
  
  onAutocompleteOpened(){
    if (this.label == 'Users') {
      if(!this.inputFC.value){
        this.resetUserList.emit(true);
      }
    }
  }

  private removeChip(chipToRemove: AutocompleteOption): void {
    this.chips = this.chips.filter((chip) => chipToRemove.id !== chip.id);
    if (Array.isArray(this.selected)) this.selected = this.selected?.filter((chip) => chipToRemove.id !== chip.id);
    const dropdownItem = this.filtered.find((option) => option.id === chipToRemove.id);
    if (dropdownItem) {
      dropdownItem.isSelected = false;
    }
    this._onChange(this.chips);
  }

  private addChip(chipToAdd: Option): void {
    this.chips = [...this.chips, chipToAdd];
    this._onChange(this.chips);
    this.clearInput();
  }

  private updatefilteredOptionList(): void {
    this.filtered = (this.options as Option[]) || [];
  }

  private clearInput(): void {
    this.inputFC.reset();
    this.inputEl.nativeElement.value = '';
  }
}