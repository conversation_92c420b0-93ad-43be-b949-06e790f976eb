<!DOCTYPE html>
<html lang='en'>
<head>
  <meta charset='utf-8' />
  <title>Load Monitor</title>
  <base href='/' />
  <meta name='viewport' content='width=device-width, initial-scale=1' />
  <link rel='icon' type='image/x-icon' src='apps\loadmonitor\src\favicon.ico' />

  <!-- the following two lines to optimize the token fetch request -->
  <link crossorigin href="https://gfkdataplatform.b2clogin.com" rel="dns-prefetch">
  <link crossorigin href="https://gfkdataplatform.b2clogin.com" rel="preconnect">
  <!-- the following line optimizes the oauth redirect -->
  <link crossorigin href="https://login.microsoftonline.com" rel="dns-prefetch">
  <!-- the following line optimizes the API requests -->
  <link crossorigin href="#{apiBaseUrl}" rel="dns-prefetch">
  <link crossorigin href="#{apiBaseUrl}" rel="preconnect">
  <!-- the following line optimizes the APM requests -->
  <link crossorigin href="#{ELASTIC_RUM_URL}" rel="dns-prefetch">
  <link crossorigin href="#{ELASTIC_RUM_URL}" rel="preconnect">

  <script type='text/javascript'>
    function EnvConfig() {
      // [0]: deployment variable, [1] fallback for developers
      // #{token} - Deploy variable
      // #{token}# - Build variable
      var config = {
        apiBaseUrl: [
          '#{apiBaseUrl}',
          '/api',
          //'https://loadmonitor-api.gfkmst1.int/api'
        ],
        jeeApiBaseUrl: [
          '#{jeeApiBaseUrl}',
          '/jee-api',
          //'https://loadmonitor-api.gfkmst1.int/api'
        ],
        countryApiBaseUrl: [
          '#{countryApiBaseUrl}',
          '/api/country-api/api',
          //'https://builder-api.gfkmst1.int/api'
        ],
        periodicityApiBaseUrl: [
          '#{periodicityApiBaseUrl}',
          '/api/date-api/api',
          //'https://builder-api.gfkmst1.int/api'
        ],
        version: [
          '#{Build.BuildNumber}#',
          'Next'],
        apiVersion: [
          '#{apiVersion}',
          'v1'],
        ENV: [
          '#{ENV}',
          'local'],
        jeeApiVersion: [
          '#{jeeApiVersion}',
          'v2',
        ],
        isElasticRUMEnabled: [
          '#{isElasticRUMEnabled}',
          'true'],
        ELASTIC_RUM_URL: [
          '#{ELASTIC_RUM_URL}',
          'https://dcex1076lgstest.gfk.com:8200/'],
        ELASTIC_RUM_SERVICE_NAME: [
          '#{ELASTIC_RUM_SERVICE_NAME}',
          'DWHLoadMonitorUI']
      };
      this.get = function(propName) {
        var prop = config[propName];
        return prop ? (/#{.+}/.test(prop[0]) ? prop[1] : prop[0]) : null;
      };
      this.set = function(propName, value) {
        config[propName] = [value];
      };
    }

    window = window || {};
    window.CONFIG = new EnvConfig();

    const agent = window.navigator.userAgent.toLowerCase();
    if (agent.indexOf('msie ') >= 0 || agent.indexOf('trident') >= 0) {
      window.location.href =
        window.location.origin +
        window.location.pathname.replace('index.html', '') +
        'unsupported_browser.html';
    }
  </script>
</head>

<body class="mat-typography">
  <lm-app-root></lm-app-root>
  <script type="text/javascript"
    src="https://jira.gfk.com/s/dbf5799de65143d45ced7a527b423cca-T/9cifr7/816001/107bizu/4351bdce5ee9d680205493d1352f62a7/_/download/contextbatch/js/com.atlassian.jira.collector.plugin.jira-issue-collector-plugin:issuecollector/batch.js?locale=en-UK&collectorId=488d54f8"></script>
</body>

</html>
