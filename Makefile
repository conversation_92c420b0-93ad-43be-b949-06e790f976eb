.PHONY: help

project-name = loadmonitor
api-url = https://loadmonitor-api.gfkmst1.int
docker-repo = st-nexus.gfk.com:5050/gfk

# Metadata for docker image
branch != git branch --show-current
author != git show -s --format='%aL' HEAD
sha != git show -s --format='%h' HEAD
msg != git show -s --format='%s' HEAD

# Show this help
help:
	@awk '/^#/{c=substr($$0,3);next}c&&/^[[:alpha:]][[:alnum:]_-]+:/{print substr($$1,1,index($$1,":")),c}1{c=0}' $(MAKEFILE_LIST) | column -s: -t

# Build base docker packages image
build-pkgs:
	docker build -t ${docker-repo}/${project-name}-pkgs:latest \
        --label "git.branch=${branch}" \
        --label "git.author=${author}" \
        --label "git.commit.sha=${sha}" \
        --label "git.commit.msg=${msg}" \
        -f ./ci/Dockerfile.Pkgs .

# Build a development ui image for local use
build-dev:
	docker build -t ${project-name}-ui-dev:latest \
        --build-arg BUILD_OPTION="--configuration=development" \
        --build-arg PROJECT_NAME=${project-name} \
        --label "project=${project-name}" \
        --label "git.branch=${branch}" \
        --label "git.author=${author}" \
        --label "git.commit.sha=${sha}" \
        --label "git.commit.msg=${msg}" \
        -f ./ci/Dockerfile .

# Build docker ui image
build:
	docker build -t ${docker-repo}/${project-name}-ui:latest \
        --build-arg PROJECT_NAME=${project-name} \
        --label "project=${project-name}" \
        --label "git.branch=${branch}" \
        --label "git.author=${author}" \
        --label "git.commit.sha=${sha}" \
        --label "git.commit.msg=${msg}" \
        -f ./ci/Dockerfile .

# Push base docker image packages to Nexus
push-pkgs:
	docker push ${docker-repo}/${project-name}-pkgs:latest

# Push production docker ui image to Nexus
push:
	docker push ${docker-repo}/${project-name}-ui:latest

# Build and push packages to Nexus
build-push-pkgs: build-pkgs push-pkgs

# Deploy ui to DEV
deploy: push
	kubectl rollout restart -n dev deploy ${project-name}-ui

# Build and Deploy ui to DEV
build-deploy: build deploy

# Check the codebase inside a docker container
check:
	docker build -t ${project-name}-ui-check:latest \
        --build-arg PROJECT_NAME=${project-name} \
        --label "project=${project-name}" \
        --label "git.branch=${branch}" \
        --label "git.author=${author}" \
        --label "git.commit.sha=${sha}" \
        --label "git.commit.msg=${msg}" \
        -f ./ci/Dockerfile.Check .

# For future use:
#   -v ./assets/config/config.json:/usr/share/nginx/html/assets/config/config.json

# Run the development image locally
run-dev:
	docker rm -f ${project-name}-ui-dev-run 2>/dev/null \
		&& docker run --name ${project-name}-ui-dev-run \
		-e API_URL=${api-url} \
		--rm -it -p 5200:8080 ${project-name}-ui-dev:latest

# Run the production image locally
run:
	docker rm -f ${project-name}-ui-prod-run 2>/dev/null \
		&& docker run --name ${project-name}-ui-prod-run \
		-e API_URL=${api-url} \
		--rm -it -p 5300:8080 ${docker-repo}/${project-name}-ui:latest

ci-reports:
	npx ng test --code-coverage
