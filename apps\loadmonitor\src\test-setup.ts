import 'jest-preset-angular/setup-jest';

declare global {
  interface Window {
    CONFIG: {
      get: (propName: string) => string;
      set: (propName: string, value: any) => void;
    };
  }
}

function EnvConfig() {
  /* eslint-disable */

  // [0]: deployment variable, [1] fallback for developers
  // #{token} - Deploy variable
  // #{token}# - Build variable
  const config = {
    apiBaseUrl: [
      '#{apiBaseUrl}',
      '/api',
      //'https://loadmonitor-api.gfkmst1.int/api'
    ],
    jeeApiBaseUrl: [
      '#{jeeApiBaseUrl}',
      'http://localhost:4200/jee-api',
      //'https://loadmonitor-api.gfkmst1.int/api'
    ],
    apiVersion: ['#{apiVersion}', 'v1'],
    ENV: ['#{ENV}', 'local'],
    version: ['#{Build.BuildNumber}#', 'Next'],
    isElasticRUMEnabled: ['#{isElasticRUMEnabled}', 'true'],
    ELASTIC_RUM_URL: [
      '#{ELASTIC_RUM_URL}',
      'https://dcex1076lgstest.gfk.com:8200/',
    ],
    ELASTIC_RUM_SERVICE_NAME: [
      '#{ELASTIC_RUM_SERVICE_NAME}',
      'DWHLoadMonitorUI',
    ],
  };
  // @ts-ignore
  this.get = function (propName: string) {
    // @ts-ignore
    const prop = config[propName];
    return prop ? (/#{.+}/.test(prop[0]) ? prop[1] : prop[0]) : null;
  };
  // @ts-ignore
  this.set = function (propName, value) {
    // @ts-ignore
    config[propName] = [value];
  };
}

window = window || {};
// @ts-ignore
window.CONFIG = new EnvConfig();

import { getTestBed } from '@angular/core/testing';
import {
  BrowserDynamicTestingModule,
  platformBrowserDynamicTesting,
} from '@angular/platform-browser-dynamic/testing';

getTestBed().resetTestEnvironment();
getTestBed().initTestEnvironment(
  BrowserDynamicTestingModule,
  platformBrowserDynamicTesting(),
  { teardown: { destroyAfterEach: false } }
);
