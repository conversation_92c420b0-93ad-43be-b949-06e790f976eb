import { HelperService } from '@loadmonitor/shared/services/helper.service';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { MatBottomSheetModule } from '@angular/material/bottom-sheet';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { DetailMenuComponent } from './detail-menu.component';
import { DWHReleaseService } from '@loadmonitor/shared/services/dwhrelease.service';
import { CommonModule, DatePipe } from '@angular/common';
import { MatSnackBarModule } from '@angular/material/snack-bar';
import { MatIconModule } from '@angular/material/icon';
import { BrowserModule } from '@angular/platform-browser';

describe('DetailMenuComponent', () => {
  const mockLoadId = 1;
  let component: DetailMenuComponent;
  let fixture: ComponentFixture<DetailMenuComponent>;
  let dwhReleaseService: DWHReleaseService;
  let helperService: HelperService;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [DetailMenuComponent],
      imports: [
        HttpClientTestingModule,
        MatBottomSheetModule,
        NoopAnimationsModule,
        MatSnackBarModule,
        MatIconModule,
        CommonModule,
        BrowserModule
      ],
      providers: [
        DWHReleaseService,
        HelperService,
        DatePipe
      ],
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(DetailMenuComponent);
    component = fixture.componentInstance;
    dwhReleaseService = TestBed.inject(DWHReleaseService);
    helperService = TestBed.inject(HelperService);

    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  xdescribe('openContext() [Method]', () => {
    const mockService = 'Test';
    const mockParameters = {};
    let mockIsShowDetails = false;
    let mockIsMessageLink =false;
    let mockIsFTXLink: any;
    let mockLink: any;

    beforeEach(() => {
      jest.spyOn(component, 'openContext');
    });
    it('should be called with correct arg', () => {
      component.openContext(mockService, mockParameters, mockIsShowDetails,mockIsMessageLink, mockIsFTXLink, mockLink);

      expect(component.openContext).toHaveBeenCalledWith(
        mockService,
        mockParameters,
        mockIsShowDetails,
        mockIsMessageLink
      );
    });

    it('should call helperService.createLink() [Method] if isShowDetails is falsy', () => {
      jest.spyOn(helperService, 'createLink');

      component.openContext(mockService, mockParameters, mockIsShowDetails, mockIsMessageLink, mockIsFTXLink, mockLink);

      expect(helperService.createLink).toHaveBeenCalledWith(
        mockService,
        mockParameters
      );
    });

    it('should be called openDetails() [Method] if the isShowDetails is truthy', () => {
      jest.spyOn(component, 'openDetails');

      mockIsShowDetails = true;
      mockIsMessageLink =true;
      component.openContext(mockService, mockParameters, mockIsShowDetails, mockIsMessageLink, mockIsFTXLink, mockLink);

      expect(component.openDetails).toHaveBeenCalledWith(
        mockService,
        mockParameters
      );
    });
  });

  xdescribe('openDetails() [Method]', () => {
    const mockService = 'test';
    const mockParameters = {
      loadid: 1,
    };

    beforeEach(() => {
      jest.spyOn(component, 'openDetails');
    });

    it('should be called with correct args', () => {
      component.openDetails(mockService, mockParameters);

      expect(component.openDetails).toHaveBeenCalledWith(
        mockService,
        mockParameters
      );
    });

    it('should set detailsButtonClicked.emit() [Method] to true', () => {
      jest.spyOn(component.detailsButtonClicked, 'emit');

      component.openDetails(mockService, mockParameters);

      expect(component.detailsButtonClicked.emit).toHaveBeenCalledWith(true);
    });

    it('should openDetailsByLoadId() [Method] to be called', () => {
      jest.spyOn(component, 'openDetailsByLoadId');

      component.openDetails(mockService, mockParameters);

      expect(component.openDetailsByLoadId).toHaveBeenCalledWith(
        mockParameters.loadid
      );
    });
  });

  xdescribe('openDetailsByLoadId() [Method]', () => {
    beforeEach(() => {
      jest.spyOn(component, 'openDetailsByLoadId');
    });

    it('should be called with correct arg', () => {
      component.openDetailsByLoadId(mockLoadId);

      expect(component.openDetailsByLoadId).toHaveBeenCalledWith(mockLoadId);
    });

    it('should be called openFloatingPanel() [Method] with correct arg', () => {
      jest.spyOn(component, 'openFloatingPanel');

      component.openDetailsByLoadId(mockLoadId);

      expect(component.openFloatingPanel).toHaveBeenCalledWith(mockLoadId);
    });
  });

  xdescribe('openFloatingPanel() [Method]', () => {
    beforeEach(() => {
      jest.spyOn(component, 'openFloatingPanel');
    });

    it('should call dwhReleaseService getDetailAsync() [Method] with correct arg', () => {
      jest.spyOn(dwhReleaseService, 'getDetailAsync');

      component.openFloatingPanel(mockLoadId);

      expect(dwhReleaseService.getDetailAsync).toHaveBeenCalledWith(mockLoadId);
    });

    it('should dwhReleaseService.getDetailAsync() [Method] return result', () => {
      component.openFloatingPanel(mockLoadId);

      dwhReleaseService.getDetailAsync(mockLoadId).subscribe((res) => {
        expect(res).not.toBeFalsy();
        (err) => fail('Test is failing!');
      });
    });
  });

  describe('notify() [Method]', () => {
    const mockNotificationMessage =
      'Deep link sucessfully copied to clipboard!';
    it('should  be called with correct message', () => {
      jest.spyOn<DetailMenuComponent, any>(component, 'notify');

      component['notify'](mockNotificationMessage);

      expect(component['notify']).toHaveBeenCalledWith(mockNotificationMessage);
    });
  });
});
