import { AfterViewInit, ChangeDetector<PERSON><PERSON>, Component, HostL<PERSON>ener, <PERSON><PERSON><PERSON>roy, OnInit, inject } from '@angular/core';
import { AuthFacade } from '@dwh/lmx-lib/src/lib/services/auth';
import { ConfigService } from '@loadmonitor/shared/services/config.service';
import { Subject } from 'rxjs';
import { map } from 'rxjs/operators';

interface ICard {
	title: string;
	description: string;
    url: string;
	tooltip:string;
	disableEnableButton:boolean;
}



@Component({
  selector: 'lm-home-page',
  templateUrl: './home-page.component.html',
  styleUrls: ['./home-page.component.scss'],
})
export class HomePageComponent implements OnInit, OnDestroy, AfterViewInit{

  private readonly _destroying$ = new Subject<void>();
	private readonly authFacade = inject(AuthFacade);

	LandingPageURL = '/https://home.de.vpc1445.in.gfk.com';
	TestApp = 'Load Monitor';
	withProfile = true;
	pageName!: string;
	withSection = false;
	version?: string;
	currentTab = window.location.href.split('/')[3];
	IsSubNavBarOpened = true;

	isIframe = false;

 userName$ = this.authFacade.user$.pipe(
    map((user: any) => {
        if (user) {
            const emailParts = user.email.split('@');
            const domain = emailParts[1];
            const username = emailParts[0].split('.')[1] + ', ' + emailParts[0].split('.')[0];

            localStorage.setItem('Email', user.email);

            return username;
        } else {
            return '';
        }
    })
);
  cards: ICard[]= [
    {
      title: "BCR Reworks",
	    description: `Monitoring of the Base Channel Rearrangement rework jobs`,
      url: "/bcr-reworks",
      disableEnableButton:false,
      tooltip: "",
     },
     {
      title: "Coverage Imports",
	    description: `Overview on the Coverage Imports`,
	    url: "/coverage-imports",
      disableEnableButton:false,
      tooltip: ``,
     },
     {
      title: "DWH/QC",
	    description: `Status overview of existing QC load jobs`,
	    url: "/dwh-qc",
      disableEnableButton:false,
      tooltip: ``,
     },
    {
      title: "Ranged Weighted Import",
	    description: `Status overview of Ranged Weighting Imports`,
      url: "/ranged-weighting-import",
      disableEnableButton:false,
      tooltip: ``,
     },
     {
      title: "Item Master Data",
	    description: `Status overview of existing Item Master Loads`,
      url: "/item-master-data",
      disableEnableButton:false,
      tooltip: ``,
     },
     {
      title: "LD General",
	    description: `Status overview of existing Load Definitions`,
      url: "/ld-general",
      disableEnableButton:false,
      tooltip: ``,
     },
     {
      title: "Logging QC",
	    description: `Status overview of existing Logging QC exports`,
      url: "/logging-qc",
      disableEnableButton:false,
      tooltip: ``,
     },
     {
      title: "Publish/Republish",
	    description: `Status overview of Publish/Unpublish jobs`,
      url: "/publish-republish",
      disableEnableButton:false,
      tooltip: ``,
     },
     {
      title: "Feature Move",
	    description: `Monitoring of Feature Move jobs for Admins & Support`,
      url: "/feature-move",
      disableEnableButton:false,
      tooltip: ``,
     },
     {
      title: "DWH Release",
	    description: `Status overview of existing Release/Unrelease activities`,
      url: "/dwhrelease",
      disableEnableButton:false,
      tooltip: ``,
     }
     ,
     {
      title: "Migration Rule Runs",
	    description: `Status of existing Migration Rule runs`,
      url: "/migration-rule-runs",
      disableEnableButton:false,
      tooltip: ``,
     }
     ,
     {
      title: "Data Order",
	    description: `Status overview of existing Data Orders`,
      url: "/data-order",
      disableEnableButton:false,
      tooltip: ``,
     }
     ,
     {
      title: "Export",
	    description: `Status of Data export, GSE scheduled Batch-to-Target exports`,
      url: "/export",
      disableEnableButton:false,
      tooltip: ``,
     }
     ,
     {
      title: "CSV Import",
	    description: `Status overview of existing CSV Imports`,
      url: "/csv-imports",
      disableEnableButton:false,
      tooltip: ``,
     }
     ,
     {
      title: "Shop Master Data",
	    description: `Status monitoring of existing Shop Master Data loads`,
      url: "/shop-master-data",
      disableEnableButton:false,
      tooltip: ``,
     }


  ];

  constructor(
    private configService: ConfigService,
    private cdr: ChangeDetectorRef
  ) {
		const packageVersion = this.configService.getVersion();
		this.version = packageVersion.substring(packageVersion.indexOf('_') + 1);
	}

  @HostListener('window:resize', ['$event'])
	onResize() {
		this.getMarginTop(0);
	}

	tabActive(activetab: string, pagesName: string, toggle: boolean) {
		if (this.currentTab == activetab && toggle) {
			this.currentTab = '';
			this.IsSubNavBarOpened = false;
		} else {
			this.currentTab = activetab;
			this.IsSubNavBarOpened = true;
		}

		this.pageName = pagesName;
	}

	getMarginTop(number) {
		if (this.IsSubNavBarOpened) return number + 25 + 'px';
		else return number + 9 - 56 + 'px';
	}


	ngOnInit(): void {
		if (
			this.currentTab === 'publish-republish' ||
			this.currentTab === 'migration-rule-runs' ||
			this.currentTab === 'data-order' ||
			this.currentTab === 'ld-general'
		) {
			this.pageName = 'Monitor DWH Process';
		} else if (this.currentTab === 'bcr-reworks' || this.currentTab === 'feature-move') {
			this.pageName = 'DWH Global Process';
		} else if (this.currentTab === 'dwh-qc' || this.currentTab === 'dwhrelease' || this.currentTab === 'logging-qc') {
			this.pageName = 'QC Monitoring';
		} else if (this.currentTab === 'shop-master-data' || this.currentTab === 'item-master-data') {
			this.pageName = 'Monitor Master Data';
		} else if (this.currentTab === 'coverage-imports' || this.currentTab === 'csv-imports' || this.currentTab === 'ranged-weighting-import') {
			this.pageName = 'Monitor Data Import';
		} else {
			this.pageName = 'Monitor Data Export';
		}
		this.isIframe = window !== window.parent && !window.opener;

		this.tabActive(this.currentTab, this.pageName, false);
	}

	ngOnDestroy(): void {
		this._destroying$.next(undefined);
		this._destroying$.complete();
	}

  ngAfterViewInit() {
    this.cdr.detectChanges();
  }

  openJiraFeedback(e: Event): void {
		e.preventDefault();
		const jiraFeedbackButton = document.querySelector('#atlwdg-trigger');
		if (jiraFeedbackButton) {
			const event = new Event('click');
			jiraFeedbackButton.dispatchEvent(event);
		}
	}

	signOut(): void {
		this.authFacade.signOut().subscribe();
	}

	signIn(): void {
		this.authFacade.signIn();
	}

}
