<mat-form-field class="chip-list" appearance="outline">
	<mat-label>Periodicity </mat-label>
	<mat-chip-list class="chip-list-wrapper" #chipListPeriodicity aria-label="Periodicity selection">
		<!-- <mat-chip *ngIf="selectedPeriodicity" [removable]="true" >
			{{ this.selectedPeriodicity.name }}
		</mat-chip> -->
		<input #periodicityInput
    class="js-filter-inp-period"
    placeholder="New ..."
    type="text" matInput
    [formControl]="periodicityFormCtrl"
    [matAutocomplete]="autoPeriodicity"
    [matChipInputFor]="chipListPeriodicity"
    [value]="this.selectedPeriodicity?.name"
    (keydown)="$event.preventDefault()"
    />
	</mat-chip-list>
	<mat-autocomplete
		autoActiveFirstOption
		#autoPeriodicity="matAutocomplete"
		(optionSelected)="optionSelected($event.option)"
		[displayWith]="displayFn"
	>
		<mat-option *ngFor="let item of filteredPeriodicities | async" [value]="item">
			{{ item.name }}
		</mat-option>
	</mat-autocomplete>
</mat-form-field>


<mat-form-field class="chip-list" *ngIf="!isPeriodFilterHidden" appearance="outline">
  <mat-label>Period </mat-label>
  <mat-chip-list class="chip-list-wrapper" #chipListPeriod aria-label="Period selection">
      <mat-chip *ngFor="let item of selectedPeriods" [selectable]="true" [removable]="true"
          (removed)="removeChipsItem(item, 'Period')">
          {{ item.name }}
          <mat-icon matChipRemove *ngIf="true">cancel</mat-icon>
      </mat-chip>
      <input placeholder="New ..." #periodInput [formControl]="periodFormCtrl" [matAutocomplete]="autoPeriod"
          [matChipInputFor]="chipListPeriod" [matChipInputSeparatorKeyCodes]="separatorKeysCodes"
          class="js-filter-inp-period" />
  </mat-chip-list>
  <mat-autocomplete #autoPeriod="matAutocomplete" [autoActiveFirstOption]="true">
      <mat-option *ngFor="let item of filteredPeriods | async" [value]="item">
          <div (click)="optionClicked($event, item, 'Period')">
              <mat-checkbox class="option-checkbox" [checked]="item.selected" (change)="togglePeriodSelection(item)"
                  (click)="$event.stopPropagation()">
                  {{ item.name }}
              </mat-checkbox>
          </div>
      </mat-option>
  </mat-autocomplete>
</mat-form-field>

