<mat-form-field class="chip-list" appearance="outline">
  <mat-label>Unit Type </mat-label>
  <mat-chip-list class="chip-list-wrapper" #chipListUnitType aria-label="unit type selection">
    <mat-chip *ngFor="let item of selectedUnitTypeIds | async" [selectable]="true" [removable]="true"
      (removed)="removeUnitTypeChips(item)">
      {{ item.name }}
      <mat-icon matChipRemove *ngIf="true">cancel</mat-icon>
    </mat-chip>
    <input placeholder="New ..." #unitTypeIdsInput [formControl]="unitTypeIdsCtrl" [matAutocomplete]="autoUnitType"
      [matChipInputFor]="chipListUnitType" [matChipInputSeparatorKeyCodes]="separatorKeysCodes"
      class="js-filter-inp-unit-type" />
  </mat-chip-list>
  <mat-autocomplete #autoUnitType="matAutocomplete" (optionSelected)="selectedUnitTypeChips($event)"
    [autoActiveFirstOption]="true">
    <mat-option *ngFor="let item of filteredUnitTypeIds | async" [value]="item">
      <div (click)="optionClicked($event, item)">
        <mat-checkbox class="option-checkbox" [checked]="item.selected" (change)="toggleSelection(item)"
          (click)="$event.stopPropagation()">
          {{ item.name }}
        </mat-checkbox>
      </div>
    </mat-option>
  </mat-autocomplete>
</mat-form-field>
